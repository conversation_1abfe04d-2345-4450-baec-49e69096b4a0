package com.imile.hrms.service.approval.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.bpm.enums.*;
import com.imile.bpm.mq.dto.*;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.vendor.dto.VendorInfoSimpleApiDTO;
import com.imile.hrms.api.salary.dto.UserInfoDetailApiDTO;
import com.imile.hrms.api.user.enums.UserDynamicFieldEnum;
import com.imile.hrms.api.user.result.UserDynamicInfoDTO;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.enums.approval.*;
import com.imile.hrms.common.enums.approval.AddDurationCustomFieldEnum;
import com.imile.hrms.common.enums.approval.ApplicationDataSourceEnum;
import com.imile.hrms.common.enums.approval.ApplicationRelationTypeEnum;
import com.imile.hrms.common.enums.approval.ApprovalNoPrefixEnum;
import com.imile.hrms.common.enums.approval.AttendanceAbnormalOperationTypeEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormAttrKeyEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormStatusEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormTypeEnum;
import com.imile.hrms.common.enums.approval.LeaveCustomFieldEnum;
import com.imile.hrms.common.enums.approval.OutOfOfficeCustomFieldEnum;
import com.imile.hrms.common.enums.approval.ReissueCardCustomFieldEnum;
import com.imile.hrms.common.enums.attendance.AbnormalAttendanceStatusEnum;
import com.imile.hrms.common.enums.attendance.AttendanceAbnormalTypeEnum;
import com.imile.hrms.common.enums.attendance.AttendanceCycleTypeEnum;
import com.imile.hrms.common.enums.attendance.AttendanceLeaveRestrictionEnum;
import com.imile.hrms.common.enums.leave.LeaveTypeEnum;
import com.imile.hrms.common.enums.leave.LeaveUnitEnum;
import com.imile.hrms.common.enums.punch.AttendancePunchTypeEnum;
import com.imile.hrms.common.enums.punch.SourceTypeEnum;
import com.imile.hrms.common.enums.salary.CycleTypeEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.*;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.common.util.DateFormatUtils;
import com.imile.hrms.common.util.DateFormatterUtil;
import com.imile.hrms.common.util.HrmsCollectionUtils;
import com.imile.hrms.common.util.IdWorkUtils;
import com.imile.hrms.common.util.IpepUtils;
import com.imile.hrms.dao.approval.mapper.HrmsApplicationFormMapper;
import com.imile.hrms.dao.approval.model.HrmsApplicationFormAttrDO;
import com.imile.hrms.dao.approval.model.HrmsApplicationFormDO;
import com.imile.hrms.dao.approval.model.HrmsApplicationFormRelationDO;
import com.imile.hrms.dao.approval.query.ApplicationFormQuery;
import com.imile.hrms.dao.approval.query.AttendanceApprovalInfoQuery;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceCycleConfigDO;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalOperationRecordDO;
import com.imile.hrms.dao.organization.dao.HrmsEntCompanyDao;
import com.imile.hrms.dao.organization.dao.HrmsEntPostDao;
import com.imile.hrms.dao.organization.model.HrmsEntCompanyDO;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsEntPostDO;
import com.imile.hrms.dao.punch.dao.*;
import com.imile.hrms.dao.punch.model.*;
import com.imile.hrms.dao.punch.param.WarehouseDetailParam;
import com.imile.hrms.dao.punch.query.AttendancePunchConfigRangeByDateQuery;
import com.imile.hrms.dao.punch.query.EmployeePunchCardRecordQuery;
import com.imile.hrms.dao.salary.model.HrmsSalaryConfigDO;
import com.imile.hrms.dao.user.dao.UserDao;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import com.imile.hrms.dao.user.dto.UserDTO;
import com.imile.hrms.dao.user.model.*;
import com.imile.hrms.dao.user.model.HrmsCompanyLeaveConfigDO;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveDetailDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveStageDetailDO;
import com.imile.hrms.dao.user.query.CompanyLeaveQuery;
import com.imile.hrms.dao.user.query.UserAssociateConditionBuilder;
import com.imile.hrms.dao.user.query.UserLeaveDetailQuery;
import com.imile.hrms.dao.user.query.UserLeaveStageDetailQuery;
import com.imile.hrms.integration.bpm.service.BpmCreateApprovalService;
import com.imile.hrms.integration.dict.service.DictService;
import com.imile.hrms.integration.dict.vo.DictVO;
import com.imile.hrms.integration.hermes.service.CountryService;
import com.imile.hrms.integration.hermes.service.VendorService;
import com.imile.hrms.manage.approval.HrmsApplicationFormAttrManage;
import com.imile.hrms.manage.approval.HrmsApplicationFormManage;
import com.imile.hrms.manage.approval.HrmsApplicationFormRelationManage;
import com.imile.hrms.manage.approval.HrmsAttendanceApprovalManage;
import com.imile.hrms.manage.approval.bo.HrmsApplicationFormDetailBO;
import com.imile.hrms.manage.attendance.HrmsEmployeeAbnormalAttendanceManage;
import com.imile.hrms.manage.company.HrmsCompanyLeaveConfigManage;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigDaoFacade;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigManageAdapter;
import com.imile.hrms.manage.organization.HrmsDeptManage;
import com.imile.hrms.manage.punch.HrmsAttendanceClassEmployeeConfigManage;
import com.imile.hrms.manage.punch.HrmsAttendanceUserCardConfigManage;
import com.imile.hrms.manage.user.HrmsUserEntryRecordManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.manage.user.HrmsUserLeaveDetailManage;
import com.imile.hrms.manage.user.HrmsUserLeaveStageDetailManage;
import com.imile.hrms.service.approval.HrmsAttendanceApprovalService;
import com.imile.hrms.service.approval.dto.*;
import com.imile.hrms.service.approval.param.*;
import com.imile.hrms.service.approval.vo.*;
import com.imile.hrms.service.attendance.AttendanceGenerateService;
import com.imile.hrms.service.attendance.HrmsAttendanceBaseService;
import com.imile.hrms.service.attendance.dto.DayAttendanceHandlerDTO;
import com.imile.hrms.service.attendance.dto.UserPunchRecordDTO;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.bpm.dto.ApprovalUserInfoDTO;
import com.imile.hrms.service.leave.HrmsCompanyLeaveConfigCarryOverService;
import com.imile.hrms.service.leave.HrmsCompanyWelfareLeaveConfigService;
import com.imile.hrms.service.punch.EmployeePunchCardService;
import com.imile.hrms.service.punch.HrmsAttendanceMobilePunchService;
import com.imile.hrms.service.punch.WarehouseSupplierService;
import com.imile.hrms.service.user.HrmsUserInfoService;
import com.imile.hrms.service.user.UserResourceService;
import com.imile.hrms.service.user.UserService;
import com.imile.hrms.service.user.vo.PermissionDeptVO;
import com.imile.idwork.IdWorkerUtil;
import com.imile.util.BeanUtils;
import com.imile.util.date.DateUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Service
@Slf4j
public class HrmsAttendanceApprovalServiceImpl extends BaseService implements HrmsAttendanceApprovalService {

    @Autowired
    private HrmsCompanyLeaveConfigManage hrmsCompanyLeaveConfigManage;
    @Autowired
    private BpmCreateApprovalService bpmCreateApprovalService;
    @Autowired
    private HrmsApplicationFormManage hrmsApplicationFormManage;
    @Autowired
    private HrmsApplicationFormMapper hrmsApplicationFormMapper;
    @Autowired
    private HrmsApplicationFormAttrManage hrmsApplicationFormAttrManage;
    @Autowired
    private HrmsApplicationFormRelationManage hrmsApplicationFormRelationManage;
    @Autowired
    private HrmsAttendanceClassEmployeeConfigManage hrmsAttendanceClassEmployeeConfigManage;
    @Autowired
    private HrmsAttendanceApprovalManage hrmsAttendanceApprovalManage;
    @Autowired
    private HrmsUserLeaveDetailManage hrmsUserLeaveDetailManage;
    @Autowired
    private HrmsUserLeaveStageDetailManage hrmsUserLeaveStageDetailManage;
    @Autowired
    private HrmsEmployeeAbnormalAttendanceManage hrmsEmployeeAbnormalAttendanceManage;
    @Autowired
    private HrmsAttendanceUserCardConfigManage hrmsAttendanceUserCardConfigManage;
    @Autowired
    private HrmsAttendanceBaseService hrmsAttendanceBaseService;
    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Autowired
    private HrmsDeptManage hrmsDeptManage;
    @Autowired
    private HrmsEntPostDao hrmsEntPostDao;
    @Autowired
    private HrmsEntCompanyDao hrmsEntCompanyDao;
    @Autowired
    private HrmsProperties hrmsProperties;
    @Autowired
    private EmployeePunchRecordDao punchRecordDao;
    @Autowired
    private IdWorkUtils idWorkUtils;
    @Autowired
    private AttendanceGenerateService attendanceGenerateService;
    @Autowired
    private UserResourceService userResourceService;
    @Autowired
    private HrmsUserInfoService hrmsUserInfoService;
    @Autowired
    private CountryService countryService;
    @Autowired
    private HrmsCompanyLeaveConfigCarryOverService hrmsCompanyLeaveConfigCarryOverService;
    @Autowired
    private HrmsAttendanceMobilePunchService hrmsAttendanceMobilePunchService;
//    @Autowired
//    private HrmsAttendancePunchClassItemConfigDao hrmsAttendancePunchClassItemConfigDao;
    @Resource
    private PunchConfigDaoFacade punchConfigDaoFacade;
    @Resource
    private PunchConfigManageAdapter punchConfigManageAdapter;


    @Value("#{${country.main.poc.map:{UAE:'2102255',OMN:'2102255',KSA:'2102474',CHN:'2101947',MEX:'2104959',TUR:'2102354',BRA:'2105554',KWT:'2103221',QAT:'2103221',JOR:'2103221',BHR:'2103221',LBN:'2103221',HQ:'2104453',POL:'2104453',DEU:'2104453',FRA:'2104453',GBR:'2104453',CHL:'2104453',RSA:'2104453',NLD:'2104453',AUS:'2104453',ITA:'2104453'}}}")
    private Map<String, String> countryMainPocMap;
    @Autowired
    private HrmsUserEntryRecordManage userEntryRecordManage;
    @Autowired
    private DictService dictService;
    @Autowired
    private HrmsAttendanceClassEmployeeConfigDao classEmployeeConfigDao;
    @Autowired
    private EmployeePunchCardService punchCardService;
    @Autowired
    private HrmsCompanyWelfareLeaveConfigService leaveConfigService;

    @Autowired
    private HrmsWarehouseDetailDao warehouseDetailDao;

    @Autowired
    private HrmsWarehouseDetailAbnormalDao warehouseDetailAbnormalDao;

    @Autowired
    private WarehouseSupplierService warehouseSupplierService;

    @Autowired
    private UserDao userDao;
    @Autowired
    private UserService userService;

    @Autowired
    private VendorService vendorService;

//    @Autowired
//    private HrmsAttendancePunchClassConfigDao attendancePunchClassConfigDao;

    @Value(value = "${mex.qwb.check.flag}")
    private String mexQwbCheckFlag;

    @Override
    public List<UserLeaveResidualVO> selectUserLeaveResidual(Long userId) {
        HrmsUserInfoDO userInfoDO = hrmsUserInfoManage.getUserInfoById(userId);
        if (userInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        UserLeaveDetailQuery userLeaveDetailQuery = new UserLeaveDetailQuery();
        userLeaveDetailQuery.setUserId(userId);
        userLeaveDetailQuery.setStatus(StatusEnum.ACTIVE.getCode());
        List<HrmsUserLeaveDetailDO> userLeaveDetailDOList = hrmsUserLeaveDetailManage.selectUserLeaveDetail(userLeaveDetailQuery);
        if (CollectionUtils.isEmpty(userLeaveDetailDOList)) {
            return new ArrayList<>();
        }
        List<Long> userLeaveIdList = userLeaveDetailDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailDOList = hrmsUserLeaveStageDetailManage.selectByLeaveId(userLeaveIdList);
        Map<Long, List<HrmsUserLeaveStageDetailDO>> userLeaveIdMap = userLeaveStageDetailDOList.stream().collect(Collectors.groupingBy(HrmsUserLeaveStageDetailDO::getLeaveId));
        // 人员假期通过configId关联
        List<Long> companyLeaveConfigIdList = userLeaveDetailDOList.stream()
                .filter(item -> Objects.nonNull(item.getConfigId()))
                .map(item -> item.getConfigId())
                .collect(Collectors.toList());
        CompanyLeaveQuery companyLeaveQuery = CompanyLeaveQuery.builder()
                .idList(companyLeaveConfigIdList).build();
        List<HrmsCompanyLeaveConfigDO> companyLeaveConfigDOList = hrmsCompanyLeaveConfigManage.selectCompanyLeave(companyLeaveQuery);
        companyLeaveConfigDOList = hrmsCompanyLeaveConfigManage.filterUserCompanyConfig(companyLeaveConfigDOList, userInfoDO);
        if (CollectionUtils.isEmpty(companyLeaveConfigDOList)) {
            return new ArrayList<>();
        }
        // 过滤用户请假时可选的假期
        this.filterUserLeaveCompanyConfig(companyLeaveConfigDOList, userInfoDO);
        //获取假期类型枚举(后续改为假期名称)
        Map<String, DictVO> leaveTypeEnumMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.HRMS_ATTENDANCE_LEAVE_TYPE);
        Map<String, DictVO> lowerleaveTypeEnumMap = leaveTypeEnumMap.entrySet().stream().collect(Collectors.toMap(item -> item.getKey().toLowerCase(), Map.Entry::getValue));

        Map<Long, List<HrmsCompanyLeaveConfigDO>> configIdMap = companyLeaveConfigDOList.stream().collect(Collectors.groupingBy(HrmsCompanyLeaveConfigDO::getId));
        List<UserLeaveResidualVO> leaveResidualVOS = new ArrayList<>();
        for (HrmsUserLeaveDetailDO userLeaveDetailDO : userLeaveDetailDOList) {
            UserLeaveResidualVO residualVO = new UserLeaveResidualVO();
            residualVO.setConfigId(userLeaveDetailDO.getConfigId());
            residualVO.setLeaveName(userLeaveDetailDO.getLeaveName());
            residualVO.setLeaveNameByLang(userLeaveDetailDO.getLeaveName());
            residualVO.setLeaveType(userLeaveDetailDO.getLeaveType());
            residualVO.setLeaveTypeByLang(userLeaveDetailDO.getLeaveType());
            List<HrmsCompanyLeaveConfigDO> userLeaveConfig = configIdMap.get(userLeaveDetailDO.getConfigId());
            if (CollectionUtils.isEmpty(userLeaveConfig)) {
                continue;
            }
            // 多语转换
            DictVO dictLeaveName = lowerleaveTypeEnumMap.get(userLeaveDetailDO.getLeaveName().toLowerCase());
            if (Objects.nonNull(dictLeaveName)) {
                residualVO.setLeaveNameByLang(dictLeaveName.getDataValue());
            }
            residualVO.setLeaveShortName(userLeaveConfig.get(0).getLeaveShortName());
            residualVO.setConsumeLeaveType(userLeaveConfig.get(0).getConsumeLeaveType());
            residualVO.setIsUploadAttachment(userLeaveConfig.get(0).getIsUploadAttachment());
            residualVO.setUploadAttachmentCondition(userLeaveConfig.get(0).getUploadAttachmentCondition());
            residualVO.setAttachmentUnit(userLeaveConfig.get(0).getAttachmentUnit());
            residualVO.setLeaveUnit(userLeaveConfig.get(0).getLeaveUnit());
            residualVO.setMiniLeaveDuration(userLeaveConfig.get(0).getMiniLeaveDuration());
            List<HrmsUserLeaveStageDetailDO> stageDetailDOS = userLeaveIdMap.get(userLeaveDetailDO.getId());
            if (CollectionUtils.isEmpty(stageDetailDOS)) {
                continue;
            }
            BigDecimal leaveResidueMinutes = BigDecimal.ZERO;
            for (HrmsUserLeaveStageDetailDO stageDetailDO : stageDetailDOS) {
                if (stageDetailDO.getLeaveResidueMinutes() == null) {
                    continue;
                }
                leaveResidueMinutes = leaveResidueMinutes.add(stageDetailDO.getLeaveResidueMinutes());
            }
            residualVO.setLeaveResidueMinutes(leaveResidueMinutes);
            leaveResidualVOS.add(residualVO);
        }
        return leaveResidualVOS;
    }

    @Override
    public PaginationResult<AttendanceApprovalInfoVO> list(AttendanceApprovalInfoParam param) {
        AttendanceApprovalInfoQuery query = BeanUtils.convert(param, AttendanceApprovalInfoQuery.class);
        query.setLeaveType(param.getLeaveName());
        if (StringUtils.equalsIgnoreCase(param.getDataSource(), "HRMS")) {
            // 获取部门权限
            UserAuthParam userAuthParam = UserAuthParam.builder().userId(RequestInfoHolder.getUserId()).build();
            UserAuthDTO userAuthDTO = userDeptAuthList(userAuthParam);
            if (!checkDeptAuth(query, userAuthDTO)) {
                return getPageResult(BeanUtils.convert(new PageInfo<>(), PageInfo.class), param);
            }
        }
        if (StringUtils.equalsIgnoreCase(param.getDataSource(), "CLOVER")) {
            query.setUserIdList(Arrays.asList(RequestInfoHolder.getUserId()));
        }
        if (StringUtils.isNotBlank(param.getFormStatus())) {
            query.setFormStatusList(Arrays.asList(param.getFormStatus()));
        }
        query.setExcludeApplicationDataSource(ApplicationDataSourceEnum.IMPORT.getCode());
        List<String> leaveTypeList = Arrays.asList(HrAttendanceApplicationFormTypeEnum.LEAVE.getCode(),
                HrAttendanceApplicationFormTypeEnum.LEAVE_REVOKE.getCode(), HrAttendanceApplicationFormTypeEnum.REISSUE_CARD.getCode(),
                HrAttendanceApplicationFormTypeEnum.REISSUE_CARD_REVOKE.getCode(), HrAttendanceApplicationFormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode());
        List<String> formTypeList = query.getFormTypeList();
        Page<HrmsApplicationFormDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount(), param.getShowCount() > 0);
        PageInfo<HrmsApplicationFormDO> pageInfo = new PageInfo<>();
        // 请假申请列表---特殊处理
        if (leaveTypeList.containsAll(formTypeList)) {
            pageInfo = page.doSelectPageInfo(() -> hrmsApplicationFormMapper.selectAttendanceApprovalInfo(query));
        } else {
            pageInfo = page.doSelectPageInfo(() -> hrmsApplicationFormManage.selectAttendanceApprovalInfo(query));
        }

        //PageInfo<HrmsApplicationFormDO> pageInfo = page.doSelectPageInfo(() -> hrmsApplicationFormManage.selectAttendanceApprovalInfo(query));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            PageInfo<AttendanceApprovalInfoVO> pageInfoResult = BeanUtils.convert(new PageInfo<>(), PageInfo.class);
            return getPageResult(pageInfoResult, param);
        }
        List<Long> formIdList = pageInfo.getList().stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsApplicationFormAttrDO> attrDOList = hrmsApplicationFormAttrManage.selectFormAttrByFormIdLit(formIdList);
        Map<Long, List<HrmsApplicationFormAttrDO>> fromIdMap = attrDOList.stream().collect(Collectors.groupingBy(HrmsApplicationFormAttrDO::getFormId));

        List<HrmsApplicationFormRelationDO> relationDOList = hrmsApplicationFormRelationManage.selectRelationByRelationIdList(formIdList).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.APPLICATION_FORM.getCode())).collect(Collectors.toList());
        List<Long> relationFormIdList = relationDOList.stream().map(item -> item.getFormId()).collect(Collectors.toList());
        List<HrmsApplicationFormDO> relationFormList = hrmsApplicationFormManage.selectByIdList(relationFormIdList).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getFormStatus(), HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode())
                        || StringUtils.equalsIgnoreCase(item.getFormStatus(), HrAttendanceApplicationFormStatusEnum.PASS.getCode())).collect(Collectors.toList());

        List<Long> deptIdList = pageInfo.getList().stream().map(item -> item.getDeptId()).collect(Collectors.toList());
        List<HrmsEntDeptDO> deptDOList = hrmsDeptManage.selectDeptByIds(deptIdList);
        Map<Long, List<HrmsEntDeptDO>> deptIdMap = deptDOList.stream().collect(Collectors.groupingBy(HrmsEntDeptDO::getId));

        List<Long> postIdList = pageInfo.getList().stream().map(item -> item.getPostId()).collect(Collectors.toList());
        List<HrmsEntPostDO> entPostDOList = hrmsEntPostDao.listByIds(postIdList);
        Map<Long, List<HrmsEntPostDO>> postIdMap = entPostDOList.stream().collect(Collectors.groupingBy(HrmsEntPostDO::getId));

        List<AttendanceApprovalInfoVO> approvalInfoVOList = new ArrayList<>();
        for (HrmsApplicationFormDO formDO : pageInfo.getList()) {
            AttendanceApprovalInfoVO infoVO = BeanUtils.convert(formDO, AttendanceApprovalInfoVO.class);
            approvalInfoVOList.add(infoVO);
            HrAttendanceApplicationFormStatusEnum statusEnum = HrAttendanceApplicationFormStatusEnum.getInstance(infoVO.getFormStatus());
            if (statusEnum != null) {
                infoVO.setFormStatusDesc(RequestInfoHolder.isChinese() ? statusEnum.getDesc() : statusEnum.getDescEn());
            }
            List<HrmsEntDeptDO> userDeptList = deptIdMap.get(formDO.getDeptId());
            if (CollectionUtils.isNotEmpty(userDeptList)) {
                infoVO.setDeptName(RequestInfoHolder.isChinese() ? userDeptList.get(0).getDeptNameCn() : userDeptList.get(0).getDeptNameEn());
            }
            List<HrmsEntPostDO> userPostList = postIdMap.get(formDO.getPostId());
            if (CollectionUtils.isNotEmpty(userPostList)) {
                infoVO.setPostName(RequestInfoHolder.isChinese() ? userPostList.get(0).getPostNameCn() : userPostList.get(0).getPostNameEn());
            }
            infoVO.setApplicationCode(formDO.getApplicationCode());
            infoVO.setApplicationFormId(formDO.getId());
            List<HrmsApplicationFormAttrDO> userAttrDOList = fromIdMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(userAttrDOList)) {
                continue;
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.LEAVE.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.LEAVE_REVOKE.getCode())) {
                //假期名称(后续单据leave_type都存储假期名称)
                List<HrmsApplicationFormAttrDO> leaveName = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveName)) {
                    infoVO.setLeaveName(leaveName.get(0).getAttrValue());
                    infoVO.setLeaveNameByLang(leaveName.get(0).getAttrValue());
                    //获取假期类型枚举进行翻译
                    Map<String, DictVO> leaveTypeEnumMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.HRMS_ATTENDANCE_LEAVE_TYPE);
                    Map<String, DictVO> lowerleaveTypeEnumMap = leaveTypeEnumMap.entrySet().stream().collect(Collectors.toMap(item -> item.getKey().toLowerCase(), Map.Entry::getValue));
                    DictVO dictVO = lowerleaveTypeEnumMap.get(leaveName.get(0).getAttrValue().toLowerCase());
                    if (Objects.nonNull(dictVO)) {
                        infoVO.setLeaveNameByLang(dictVO.getDataValue());
                    }
                }

                //假期简称
                List<HrmsApplicationFormAttrDO> leaveShortName = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveShortName.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveShortName)) {
                    infoVO.setLeaveShortName(leaveShortName.get(0).getAttrValue());
                }

                //请假开始时间
                List<HrmsApplicationFormAttrDO> leaveStartDate = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveStartDate)) {
                    infoVO.setLeaveStartDate(DateUtil.parse(leaveStartDate.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
                }

                //请假结束时间
                List<HrmsApplicationFormAttrDO> leaveEndDate = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveEndDate)) {
                    infoVO.setLeaveEndDate(DateUtil.parse(leaveEndDate.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
                }
                List<HrmsApplicationFormAttrDO> dayDurationAttr = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dayDurationAttr)) {
                    List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayDurationAttr.get(0).getAttrValue(), DayDurationInfoDTO.class);
                    BigDecimal days = BigDecimal.ZERO;
                    BigDecimal hours = BigDecimal.ZERO;
                    BigDecimal minutes = BigDecimal.ZERO;
                    for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                        days = days.add(dayDurationInfoDTO.getDays());
                        hours = hours.add(dayDurationInfoDTO.getHours());
                        minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                    }
                    if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.LEAVE.getCode())) {
                        infoVO.setExpectedLeaveTime(days + "days" + hours + "hours" + minutes + "minutes");
                        if (RequestInfoHolder.isChinese()) {
                            infoVO.setExpectedLeaveTime(days + "天" + hours + "小时" + minutes + "分钟");
                        }
                    }
                }

                //请假单位
                List<HrmsApplicationFormAttrDO> leaveUnit = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveUnit.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveUnit)) {
                    infoVO.setLeaveUnit(leaveUnit.get(0).getAttrValue());
                }
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE_REVOKE.getCode())) {
                //外勤开始时间
                List<HrmsApplicationFormAttrDO> outOfOfficeStartDate = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outOfOfficeStartDate)) {
                    infoVO.setOutOfOfficeStartDate(DateUtil.parse(outOfOfficeStartDate.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
                }

                //外勤结束时间
                List<HrmsApplicationFormAttrDO> outOfOfficeEndDate = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outOfOfficeEndDate)) {
                    infoVO.setOutOfOfficeEndDate(DateUtil.parse(outOfOfficeEndDate.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
                }

                List<HrmsApplicationFormAttrDO> dayDurationAttr = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dayDurationAttr)) {
                    List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayDurationAttr.get(0).getAttrValue(), DayDurationInfoDTO.class);
                    BigDecimal days = BigDecimal.ZERO;
                    BigDecimal hours = BigDecimal.ZERO;
                    BigDecimal minutes = BigDecimal.ZERO;
                    for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                        days = days.add(dayDurationInfoDTO.getDays());
                        hours = hours.add(dayDurationInfoDTO.getHours());
                        minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                    }
                    if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode())) {
                        infoVO.setExpectedOutOfOfficeTime(days + "days" + hours + "hours" + minutes + "minutes");
                        if (RequestInfoHolder.isChinese()) {
                            infoVO.setExpectedOutOfOfficeTime(days + "天" + hours + "小时" + minutes + "分钟");
                        }
                    }
                }
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.REISSUE_CARD.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.REISSUE_CARD_REVOKE.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode())) {
                //补卡类型
                List<HrmsApplicationFormAttrDO> reissueCardType = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardType.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(reissueCardType)) {
                    infoVO.setReissueCardType(reissueCardType.get(0).getAttrValue());
                }

                //补卡时间
                List<HrmsApplicationFormAttrDO> correctPunchTime = userAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(correctPunchTime)) {
                    infoVO.setCorrectPunchTime(DateUtil.parse(correctPunchTime.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
                }
            }

            List<HrmsApplicationFormRelationDO> existRelationList = relationDOList.stream().filter(item -> item.getRelationId().equals(formDO.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existRelationList)) {
                for (HrmsApplicationFormRelationDO relationDO : existRelationList) {
                    List<HrmsApplicationFormDO> existRelationFormList = relationFormList.stream().filter(item -> item.getId().equals(relationDO.getFormId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(existRelationFormList)) {
                        infoVO.setRevokeApplicationCode(existRelationFormList.get(0).getApplicationCode());
                        infoVO.setRevokeApplicationFormId(existRelationFormList.get(0).getId());
                        infoVO.setRevokeFormStatus(existRelationFormList.get(0).getFormStatus());
                        infoVO.setRevokeFormType(existRelationFormList.get(0).getFormType());
                    }
                }
            }
        }
        PageInfo<AttendanceApprovalInfoVO> pageInfoResult = BeanUtils.convert(pageInfo, PageInfo.class);
        pageInfoResult.setList(approvalInfoVOList);
        return getPageResult(pageInfoResult, param);
    }

    // permission_reconfiguration
    @Override
    public List<UserAuthVO> userAuthList(UserAuthParam param) {
        List<UserAuthVO> userAuthVOList = new ArrayList<>();
        HrmsUserInfoDO applyUserInfoDO = hrmsUserInfoManage.getUserInfoById(param.getUserId());
        if (applyUserInfoDO == null) {
            return userAuthVOList;
        }
        // 获取用户部门权限
        UserAuthDTO userAuthDTO = userDeptAuthList(param);
        UserAssociateConditionBuilder condition = BeanUtils.convert(param, UserAssociateConditionBuilder.class);
        condition.setDeptIdList(userAuthDTO.getDeptIds());
        List<HrmsUserInfoDO> userList = userDao.selectByAssociateCondition(condition);
        if (CollectionUtils.isEmpty(userList)) {
            return userAuthVOList;
        }
        userAuthVOList = userList.stream()
                .map(s -> UserAuthVO.builder()
                        .userId(s.getId())
                        .userCode(s.getUserCode())
                        .userName(BusinessFieldUtils.getUnifiedUserName(s.getUserName(), s.getUserNameEn()))
                        .deptId(s.getDeptId())
                        .postId(s.getPostId())
                        .build())
                .collect(Collectors.toList());
        return userAuthVOList;
    }

    @Override
    public UserAuthDTO userDeptAuthList(UserAuthParam param) {
        HrmsUserInfoDO applyUserInfoDO = hrmsUserInfoManage.getUserInfoById(param.getUserId());
        if (applyUserInfoDO == null) {
            return UserAuthDTO.builder().isAdmin(Boolean.FALSE).deptIds(Lists.newArrayList()).build();
        }
        //系统管理员
        PermissionDeptVO deptVO = userResourceService.getPermissionDept(param.getUserId());
        if (deptVO != null && deptVO.getIsSysAdmin()) {
            return UserAuthDTO.builder().isAdmin(Boolean.TRUE).deptIds(Lists.newArrayList()).build();
        }
        List<Long> deptAuthList = Lists.newArrayList();
        // 部门负责人
        List<HrmsEntDeptDO> deptDOList = hrmsDeptManage.listByDeptLeader(Arrays.asList(param.getUserId()));
        if (CollectionUtils.isNotEmpty(deptDOList)) {
            List<Long> deptIdList = deptDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
            deptAuthList.addAll(deptIdList);
        }
        //普通管理员
        if (deptVO != null && CollectionUtils.isNotEmpty(deptVO.getDeptIdList())) {
            deptAuthList.addAll(deptVO.getDeptIdList());
        }
        return UserAuthDTO.builder().isAdmin(Boolean.FALSE)
                .deptIds(deptAuthList.stream().distinct().collect(Collectors.toList()))
                .build();
    }

    @Override
    public DurationDetailVO durationDetail(DurationDetailParam param) {
        HrmsUserInfoDO userInfoDO = hrmsUserInfoManage.getUserInfoById(param.getUserId());
        if (userInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        if (StringUtils.isBlank(param.getCountry())) {
            param.setCountry(userInfoDO.getLocationCountry());
        }
        DurationDetailVO durationDetailVO = new DurationDetailVO();
        boolean tag = true;
        if (param.getFormId() != null) {
            HrmsApplicationFormDetailBO detailBO = hrmsApplicationFormManage.getFormDetailById(param.getFormId());
            HrmsApplicationFormDO formDO = detailBO.getFormDO();
            if (formDO != null && !StringUtils.equalsIgnoreCase(formDO.getFormStatus(), HrAttendanceApplicationFormStatusEnum.STAGING.getCode()) && !StringUtils.equalsIgnoreCase(formDO.getFormStatus(), HrAttendanceApplicationFormStatusEnum.REJECT.getCode())) {
                List<HrmsApplicationFormAttrDO> attrDOS = detailBO.getAttrDOS();
                Map<String, HrmsApplicationFormAttrDO> attrMap = attrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
                HrmsApplicationFormAttrDO dayDurationAttr = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode());
                if (dayDurationAttr != null) {
                    List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayDurationAttr.getAttrValue(), DayDurationInfoDTO.class);
                    //过滤不占用请假时间的日期
                    dayDurationInfoDTOList = dayDurationInfoDTOList.stream()
                            .filter(item -> item.getDays().compareTo(BigDecimal.ZERO) > 0
                                    || item.getHours().compareTo(BigDecimal.ZERO) > 0
                                    || item.getMinutes().compareTo(BigDecimal.ZERO) > 0)
                            .collect(Collectors.toList());
                    durationDetailVO.setDayDurationInfoDTOList(dayDurationInfoDTOList);
                    tag = false;
                }
            }
        }

        HrmsCompanyLeaveConfigDO companyLeaveConfigDO = null;
        if (tag) {
            //查询该假期配置（外勤不需要）
            if (Objects.nonNull(param.getConfigId())) {
                companyLeaveConfigDO = hrmsCompanyLeaveConfigManage.getById(param.getConfigId());
            } else if (StringUtils.isNotBlank(param.getLeaveName()) && StringUtils.isNotBlank(param.getCountry())) {
                // 为了适配历史数据 保留此方法
                companyLeaveConfigDO = selectByNameAndUserCode(Arrays.asList(userInfoDO.getUserCode()), param.getLeaveName());
            }
            //查找冲突单据
            List<ClashApplicationInfoDTO> clashApplicationInfoDTOList = new ArrayList<>();
            selectClashApplication(param.getUserId(), param.getStartDate(), param.getEndDate(), clashApplicationInfoDTOList);
            durationDetailVO.setClashApplicationInfoDTOList(clashApplicationInfoDTOList);
            //每天时长计算明细
            List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
            Date nowDate = new Date();
            //获取用户的薪资方案
            //HrmsSalaryConfigDO hrmsSalaryConfigDO = hrmsAttendanceBaseService.getUserSalaryConfig(param.getUserId());
            //查询是否超过请假时间
            //leaveDataConfirmCycleCheck(nowDate, hrmsSalaryConfigDO, param.getStartDate());
            // 考勤周期
            HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(param.getUserId());
            confirmCycleCheck(nowDate, userAttendanceCycleConfig, param.getStartDate());

            dayDurationInfoHandler(param.getUserId(), param.getStartDate(), param.getEndDate(), companyLeaveConfigDO, dayDurationInfoDTOList, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
            //过滤不占用请假时间的日期
            dayDurationInfoDTOList = dayDurationInfoDTOList.stream()
                    .filter(item -> item.getDays().compareTo(BigDecimal.ZERO) > 0
                            || item.getHours().compareTo(BigDecimal.ZERO) > 0
                            || item.getMinutes().compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.toList());
            durationDetailVO.setDayDurationInfoDTOList(dayDurationInfoDTOList);
        }

        BigDecimal days = BigDecimal.ZERO;
        BigDecimal hours = BigDecimal.ZERO;
        BigDecimal minutes = BigDecimal.ZERO;
        for (DayDurationInfoDTO dayDurationInfoDTO : durationDetailVO.getDayDurationInfoDTOList()) {
            days = days.add(dayDurationInfoDTO.getDays());
            hours = hours.add(dayDurationInfoDTO.getHours());
            minutes = minutes.add(dayDurationInfoDTO.getMinutes());
        }
        if (companyLeaveConfigDO != null && StringUtils.equalsIgnoreCase(companyLeaveConfigDO.getLeaveUnit(), LeaveUnitEnum.DAYS.getCode())) {
            durationDetailVO.setExpectedTime(days + "days");
            if (RequestInfoHolder.isChinese()) {
                durationDetailVO.setExpectedTime(days + "天");
            }
        }
        // 请假考虑弹性时长：弹性时长存在半个小时的情况，请假结束时间可能存在17:30的情况，所以这边加上了分钟。
        if (companyLeaveConfigDO != null && StringUtils.equalsIgnoreCase(companyLeaveConfigDO.getLeaveUnit(), LeaveUnitEnum.HOURS.getCode())) {
            durationDetailVO.setExpectedTime(days + "days" + hours + "hours" + minutes + "minutes");
            if (RequestInfoHolder.isChinese()) {
                durationDetailVO.setExpectedTime(days + "天" + hours + "小时" + minutes + "分钟");
            }
        }
        if (companyLeaveConfigDO != null && StringUtils.equalsIgnoreCase(companyLeaveConfigDO.getLeaveUnit(), LeaveUnitEnum.MINUTES.getCode())) {
            durationDetailVO.setExpectedTime(days + "days" + hours + "hours" + minutes + "minutes");
            if (RequestInfoHolder.isChinese()) {
                durationDetailVO.setExpectedTime(days + "天" + hours + "小时" + minutes + "分钟");
            }
        }
        if (companyLeaveConfigDO == null) {
            durationDetailVO.setExpectedTime(days + "days" + hours + "hours" + minutes + "minutes");
            if (RequestInfoHolder.isChinese()) {
                durationDetailVO.setExpectedTime(days + "天" + hours + "小时" + minutes + "分钟");
            }
        }
        return durationDetailVO;
    }

    @Override
    public UserDayReissueInfoVO getUserDayReissueInfo(UserDayReissueInfoParam param) {
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(param.getUserId());
        if (hrmsUserInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        //获取用户的薪资方案
        //HrmsSalaryConfigDO hrmsSalaryConfigDO = hrmsAttendanceBaseService.getUserSalaryConfig(param.getUserId());
        HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(param.getUserId());
        //查询是否超过补卡时间
        Date nowDate = Objects.isNull(param.getDateTime()) ? new Date() : param.getDateTime();

        Date abnormalDate = DateUtil.parse(param.getDayId().toString(), "yyyyMMdd");
        //leaveDataConfirmCycleCheck(nowDate, hrmsSalaryConfigDO, abnormalDate);
        confirmCycleCheck(nowDate, userAttendanceCycleConfig, abnormalDate);

        UserDayReissueInfoVO userDayReissueInfoVO = new UserDayReissueInfoVO();

        List<UserDayReissueAbnormalDTO> userDayReissueAbnormalDTOList = new ArrayList<>();
        userDayReissueInfoVO.setUserDayReissueAbnormalDTOList(userDayReissueAbnormalDTOList);
        //查询当天没有被申请关联的未处理异常（可以进行补卡操作的）
        List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectAbnormalByUserIdList(Arrays.asList(param.getUserId()), Arrays.asList(param.getDayId())).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode())
                        || StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.REJECT.getCode()))
                .filter(item -> AttendanceAbnormalTypeEnum.getRessiueCodeList().contains(item.getAbnormalType())).collect(Collectors.toList());
        List<Long> classId = abnormalAttendanceDOList.stream()
                .map(HrmsEmployeeAbnormalAttendanceDO::getPunchClassConfigId).collect(Collectors.toList());
//        List<HrmsAttendancePunchClassItemConfigDO> attendancePunchClassItemConfig = hrmsAttendancePunchClassItemConfigDao.selectItemConfigByClassId(classId);
        List<HrmsAttendancePunchClassItemConfigDO> attendancePunchClassItemConfig = punchConfigDaoFacade.getClassItemConfigAdapter().selectItemConfigByClassId(classId);
        hrmsAttendanceMobilePunchService.transferItemConfigTimeFormat(attendancePunchClassItemConfig, param.getDayId());
        hrmsAttendanceMobilePunchService.filterAbnormalAttendance(abnormalAttendanceDOList, attendancePunchClassItemConfig, nowDate);
        List<Long> abnormalIdList = abnormalAttendanceDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsApplicationFormRelationDO> abnormalRelationDOList = hrmsApplicationFormRelationManage.selectRelationByRelationIdList(abnormalIdList).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode())).collect(Collectors.toList());
        List<Long> abnormalFormIdList = abnormalRelationDOList.stream().map(item -> item.getFormId()).collect(Collectors.toList());
        List<HrmsApplicationFormDO> formDOList = hrmsApplicationFormManage.selectByIdList(abnormalFormIdList);

        Long punchClassId = null;
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            //自由补卡逻辑(无异常补加班卡)
            List<HrmsAttendanceClassEmployeeConfigDO> employeeConfigDOS = classEmployeeConfigDao.selectRecordByDayList(param.getUserId(), Arrays.asList(param.getDayId()));
            if (CollectionUtils.isEmpty(employeeConfigDOS)) {
                return userDayReissueInfoVO;
            }
            punchClassId = employeeConfigDOS.get(0).getClassId();
            if (Objects.isNull(punchClassId)) {
                userDayReissueInfoVO.setPunchConfigClassItemInfo(employeeConfigDOS.get(0).getDayPunchType());
            }
            userDayReissueInfoVO.setPunchClassId(punchClassId);
            userDayReissueInfoVO.setPunchConfigId(employeeConfigDOS.get(0).getPunchConfigId());
//            attendancePunchClassItemConfig = hrmsAttendancePunchClassItemConfigDao.selectItemConfigByClassId(Arrays.asList(punchClassId));
            attendancePunchClassItemConfig = punchConfigDaoFacade.getClassItemConfigAdapter().selectItemConfigByClassId(Arrays.asList(punchClassId));
            hrmsAttendanceMobilePunchService.transferItemConfigTimeFormat(attendancePunchClassItemConfig, param.getDayId());
            hrmsAttendanceMobilePunchService.filterAbnormalAttendance(abnormalAttendanceDOList, attendancePunchClassItemConfig, nowDate);
            String configClassItemInfo = getConfigClassItemInfo(attendancePunchClassItemConfig);
            if (StringUtils.isNotBlank(configClassItemInfo)) {
                userDayReissueInfoVO.setPunchConfigClassItemInfo(configClassItemInfo);
            }
            //查询用户当日最早最晚打卡记录
            List<EmployeePunchRecordDO> userPunchRecordList = punchCardService.getUserPunchRecords(hrmsUserInfoDO.getUserCode(), Arrays.asList(param.getDayId()))
                    .stream()
                    .filter(item -> !SourceTypeEnum.REISSUE_CARD.name().equals(item.getSourceType()))
                    .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userPunchRecordList)) {
                userDayReissueInfoVO.setActualEarliestPunchTime(userPunchRecordList.get(0).getPunchTime());
                userDayReissueInfoVO.setActualLatestPunchTime(userPunchRecordList.get(userPunchRecordList.size() - 1).getPunchTime());
            }
        }
        for (HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO : abnormalAttendanceDOList) {
//            List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(Arrays.asList(abnormalAttendanceDO.getPunchClassConfigId()));
            List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOList = punchConfigManageAdapter.selectItemConfigByClassId(Arrays.asList(abnormalAttendanceDO.getPunchClassConfigId()));
            List<HrmsApplicationFormRelationDO> userRelationList = abnormalRelationDOList.stream().filter(item -> item.getRelationId().equals(abnormalAttendanceDO.getId())).collect(Collectors.toList());
            //没有被任何人申请过
            if (CollectionUtils.isEmpty(userRelationList)) {
                punchClassId = abnormalAttendanceDO.getPunchClassConfigId();
                userDayReissueAbnormalDTOBuild(abnormalAttendanceDO, itemConfigDOList, userDayReissueInfoVO, userDayReissueAbnormalDTOList);
                continue;
            }
            //被关联了，看关联的申请单的状态
            List<HrmsApplicationFormDO> userFormList = formDOList.stream()
                    .filter(item -> item.getId().equals(userRelationList.get(0).getFormId()))
                    .filter(item -> !StringUtils.equalsIgnoreCase(item.getFormStatus(), HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode())
                            && !StringUtils.equalsIgnoreCase(item.getFormStatus(), HrAttendanceApplicationFormStatusEnum.PASS.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userFormList)) {
                continue;
            }
            punchClassId = abnormalAttendanceDO.getPunchClassConfigId();
            userDayReissueAbnormalDTOBuild(abnormalAttendanceDO, itemConfigDOList, userDayReissueInfoVO, userDayReissueAbnormalDTOList);
        }
        //设置最早最晚打卡时间
        setEarAndLatePunchTime(punchClassId, param.getDayId(), param.getUserId(), attendancePunchClassItemConfig, userDayReissueInfoVO);

        //获取用户指定日期所属的考勤周期
        //AttendanceDayCycleDTO attendanceDayCycleDTO = hrmsAttendanceBaseService.getUserAttendanceDayCycle(param.getDayId(), hrmsSalaryConfigDO);
        AttendanceDayCycleDTO attendanceDayCycleDTO = hrmsAttendanceBaseService.getUserAttendanceCycleConfigDay(param.getDayId(), userAttendanceCycleConfig);
        if (attendanceDayCycleDTO != null) {
            userDayReissueInfoVO.setAttendanceStartDate(attendanceDayCycleDTO.getAttendanceStartDate());
            userDayReissueInfoVO.setAttendanceEndDate(attendanceDayCycleDTO.getAttendanceEndDate());
        }
        ReissueCardAddParam reissueCardAddParam = new ReissueCardAddParam();
        reissueCardAddParam.setUserId(param.getUserId());
        int residueReissueCardCount = getUserResidueReissueCardCount(reissueCardAddParam, abnormalDate);
        userDayReissueInfoVO.setResidueReissueCardCount(residueReissueCardCount);
        //设置打卡规则类型、名称
        if (Objects.isNull(userDayReissueInfoVO.getPunchConfigId())) {
            return userDayReissueInfoVO;
        }
//        HrmsAttendancePunchConfigDO punchConfigDO = punchConfigDao.getById(userDayReissueInfoVO.getPunchConfigId());
        HrmsAttendancePunchConfigDO punchConfigDO = punchConfigDaoFacade.getConfigAdapter().getById(userDayReissueInfoVO.getPunchConfigId());
        if (Objects.nonNull(punchConfigDO)) {
            userDayReissueInfoVO.setPunchConfigType(punchConfigDO.getPunchConfigType());
            userDayReissueInfoVO.setPunchConfigName(punchConfigDO.getPunchConfigName());
        }
        return userDayReissueInfoVO;
    }

    @Override
    public ApprovalResultVO leaveAdd(LeaveAddParam param) {
        log.info("leaveAdd | LeaveAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        // 现在该接口不存在operationType = 2的情况，预览是新的接口了
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        userBaseInfoBuild(param, null, null);
        //暂存不校验任何信息，直接落库成功，提交时校验
        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOArrayList = new ArrayList<>();
        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        // 需要扣减假期余额、增加已使用余额的假期详情数据列表
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        // 用户请假记录数据
        HrmsUserLeaveRecordDO userLeaveRecord = null;
        if (param.getOperationType() == 1) {
            //异常判断
            abnormalAttendanceDO = userAbnormalRecordCheck(param.getAbnormalId());
            // 新增返回请假总时长-单位分钟
            BigDecimal totalLeaveTime = leaveAddDataCheck(param);
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode());
            // 创建用户请假详情信息数据
            buildUserLeaveStageDetailList(userLeaveStageDetailList, param, totalLeaveTime);
            // 创建用户请假记录数据
            userLeaveRecord = buildUserLeaveRecord(totalLeaveTime, param, LeaveTypeEnum.LEAVE.getCode(), "【hrms或clover】新增请假申请入口");
        }
        //注意，不仅需要构建审批表信息，正常考勤表也需要落库，状态为未生效,注意假期比例也是为空，只有审批通过，才会扣除假期
        leaveDataAddBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOArrayList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            //暂存不用落正常考勤表/异常表
            hrmsAttendanceApprovalManage.formAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOArrayList, hrmsApplicationFormAttrDOArrayList, null, null, userLeaveStageDetailList, null);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        leaveAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        hrmsApplicationFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        hrmsAttendanceApprovalManage.formAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOArrayList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO, userLeaveStageDetailList, userLeaveRecord);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * 校验选中部门和用户部门权限
     *
     * @param query
     * @param userAuthDTO
     * @return
     */
    private Boolean checkDeptAuth(AttendanceApprovalInfoQuery query, UserAuthDTO userAuthDTO) {
        // 系统管理员
        if (userAuthDTO.getIsAdmin()) {
            return Boolean.TRUE;
        }
        List<Long> deptAuthList = userAuthDTO.getDeptIds();
        // 非系统管理员且无部门权限
        if (CollectionUtils.isEmpty(deptAuthList)) {
            return Boolean.FALSE;
        }
        // 普通用户
        List<Long> deptIds = query.getDeptIds();
        if (CollectionUtils.isEmpty(deptIds)) {
            query.setDeptIds(deptAuthList);
            return Boolean.TRUE;
        }
        List<Long> interSectionList = deptAuthList.stream().filter(item -> deptIds.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(interSectionList)) {
            return Boolean.FALSE;
        }
        query.setDeptIds(interSectionList);
        return Boolean.TRUE;
    }

    /**
     * 构建用户请假记录数据
     *
     * @param param  入参
     * @param remark 备注
     * @param type   请假类型
     */
    private HrmsUserLeaveRecordDO buildUserLeaveRecord(BigDecimal totalLeaveTime, LeaveAddParam param, String type, String remark) {
        HrmsUserLeaveRecordDO userLeaveRecord = new HrmsUserLeaveRecordDO();
        userLeaveRecord.setId(iHrmsIdWorker.nextId());
        userLeaveRecord.setUserId(param.getUserId());
        userLeaveRecord.setUserCode(param.getUserCode());
        userLeaveRecord.setDate(new Date());
        userLeaveRecord.setDayId(Long.parseLong(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
        userLeaveRecord.setConfigId(param.getConfigId());
        userLeaveRecord.setLeaveName(param.getLeaveName());
        userLeaveRecord.setLeaveType(param.getLeaveType());
        userLeaveRecord.setType(type);
        userLeaveRecord.setLeaveStartDay(param.getLeaveStartDate());
        userLeaveRecord.setLeaveEndDay(param.getLeaveEndDate());
        userLeaveRecord.setLeaveMinutes(totalLeaveTime);
        userLeaveRecord.setRemark(remark);
        fillDOInsert(userLeaveRecord);
        userLeaveRecord.setOperationUserCode(RequestInfoHolder.getUserCode());
        userLeaveRecord.setOperationUserName(RequestInfoHolder.getUserName());
        return userLeaveRecord;
    }

    /**
     * 构建假期详情表数据
     *
     * @param userLeaveStageDetailInfoList 需要修改的假期详情数据
     * @param param                        入参
     * @param totalLeaveTime               请假总时长
     */
    private void buildUserLeaveStageDetailList(List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailInfoList, LeaveAddParam param, BigDecimal totalLeaveTime) {
        UserLeaveDetailQuery query = new UserLeaveDetailQuery();
        // param参数在前面已经校验了
        query.setUserId(param.getUserId());
        query.setConfigId(param.getConfigId());
        List<HrmsUserLeaveDetailDO> userLeaveDetail = hrmsUserLeaveDetailManage.selectUserLeaveDetail(query);
        if (CollUtil.isEmpty(userLeaveDetail)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.USER_NOT_HAVE_THIS_LEAVE_TYPE);
        }
        List<Long> leaveIdList = userLeaveDetail.stream().map(HrmsUserLeaveDetailDO::getId).collect(Collectors.toList());
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = hrmsUserLeaveStageDetailManage.selectByLeaveId(leaveIdList);
        if (CollUtil.isEmpty(userLeaveStageDetailList)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.THE_USER_LACKS_DETAILED_DATA_FOR_THE_LEAVE_TYPE);
        }
        // 先扣除结转的假期，不够的时候再扣除非结转的假期余额,将假期先按照是否结转倒叙排序后再按照阶段排序
        // 将假期按照阶段倒序，优先使用比率高的假期余额
        List<HrmsUserLeaveStageDetailDO> reversedUserLeaveStageDetailList = userLeaveStageDetailList.stream()
                .sorted(Comparator.comparingInt(HrmsUserLeaveStageDetailDO::getLeaveMark)
                        .thenComparing(HrmsUserLeaveStageDetailDO::getPercentSalary).reversed())
                .collect(Collectors.toList());
        // 这边无需考虑假期余额不够扣减的情况，因为如果假期余额不够扣减，前面校验都不会通过的
        for (int i = 0; i < reversedUserLeaveStageDetailList.size(); i++) {
            HrmsUserLeaveStageDetailDO stageDetailInfo = reversedUserLeaveStageDetailList.get(i);
            // 不是最后一个阶梯，并且有假期<=0
            // 当前逻辑 ：1. 不是最后一个阶梯，判断假期余额是否小于等于0，如果小于等于0则跳过该假期阶梯。2. 如果是最后一个阶梯，直接使用最后一个阶梯
            //if (i != reversedUserLeaveStageDetailList.size() - 1 && stageDetailInfo.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) < 1) {
            //    continue;
            //}

            // 如果当前阶段假期余额 <= 0，则表示无法扣减当前阶段假期余额
            if (stageDetailInfo.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            /*
                当前阶段(请假时长 - 假期余额)
                    大于0:该阶段假期不足以请假，需要加上下一个阶段的假期余额
                    等于0:正好完成请该阶段的假期
                    小于0:可以完全请该阶段的假期
             */
            BigDecimal difference = totalLeaveTime.subtract(stageDetailInfo.getLeaveResidueMinutes());
            if (difference.compareTo(BigDecimal.ZERO) <= 0) {
                stageDetailInfo.setLeaveUsedMinutes(stageDetailInfo.getLeaveUsedMinutes().add(totalLeaveTime));
                stageDetailInfo.setLeaveResidueMinutes(stageDetailInfo.getLeaveResidueMinutes().subtract(totalLeaveTime));
                stageDetailInfo.setLastUpdDate(new Date());
                stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
                stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
                userLeaveStageDetailInfoList.add(stageDetailInfo);
                break;
            }
            // 提前存储假期剩余时间，为后面减法做准备
            BigDecimal subtractLeaveResidueMinutes = stageDetailInfo.getLeaveResidueMinutes();
            // 大于0：直接该阶段假期余额变成0，已使用 = 已使用 + 假期余额
            stageDetailInfo.setLeaveUsedMinutes(stageDetailInfo.getLeaveUsedMinutes().add(stageDetailInfo.getLeaveResidueMinutes()));
            stageDetailInfo.setLeaveResidueMinutes(BigDecimal.ZERO);
            stageDetailInfo.setLastUpdDate(new Date());
            stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
            stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
            userLeaveStageDetailInfoList.add(stageDetailInfo);
            // 更新请假时间，为后续循环准备
            totalLeaveTime = totalLeaveTime.subtract(subtractLeaveResidueMinutes);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResultVO leaveUpdate(LeaveAddParam param) {
        log.info("leaveUpdate | LeaveAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        userBaseInfoBuild(param, null, null);
        HrmsApplicationFormDetailBO formDetailBO = hrmsApplicationFormManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //暂存不校验必填项，提交时校验，暂存后进入单据管理-状态为暂存待办
        HrmsApplicationFormDO hrmsApplicationFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList = new ArrayList<>();
        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        // 需要扣减假期余额、增加已使用余额的假期详情数据列表
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        // 用户请假记录数据
        HrmsUserLeaveRecordDO userLeaveRecord = null;
        if (param.getOperationType() == 1) {
            abnormalAttendanceDO = userAbnormalRecordCheck(param.getAbnormalId());
            // 新增返回请假总时长-单位分钟
            BigDecimal totalLeaveTime = leaveAddDataCheck(param);
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode());
            buildUserLeaveStageDetailList(userLeaveStageDetailList, param, totalLeaveTime);
            // 创建用户请假记录数据
            userLeaveRecord = buildUserLeaveRecord(totalLeaveTime, param, LeaveTypeEnum.LEAVE.getCode(), "【hrms或clover驳回重新提交 或 hrms暂存重新提交】修改请假申请入口");
        }
        leaveDataUpdateBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            //落库,需要删除所有旧的属性表/关联表数据   主表不能删除在新增，要保持单号的一致，主表更新
            List<HrmsApplicationFormAttrDO> oldAttrList = formDetailBO.getAttrDOS();
            oldAttrList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdate(item);
                item.setLastUpdDate(new Date());
            });
            List<HrmsApplicationFormRelationDO> oldRelationList = formDetailBO.getRelationDOS();
            oldRelationList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdate(item);
            });
            hrmsAttendanceApprovalManage.formUpdate(hrmsApplicationFormDO, oldRelationList, oldAttrList);
            hrmsAttendanceApprovalManage.formAdd(null, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, null, null, userLeaveStageDetailList, null);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        leaveAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);
        //本次保存是否是驳回后重提
        if (hrmsApplicationFormDO.getApprovalId() != null && StringUtils.isNotBlank(hrmsApplicationFormDO.getApprovalProcessInfo())) {
            ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO = JSON.parseObject(hrmsApplicationFormDO.getApprovalProcessInfo(), ApprovalPushStatusMsgDTO.class);
            if (approvalPushStatusMsgDTO != null && approvalPushStatusMsgDTO.getStatus().equals(-2)) {
                initInfoApiDTO.setResubmitApprovalId(hrmsApplicationFormDO.getApprovalId());
            }
        }
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        hrmsApplicationFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        //需要删除所有旧的属性表/关联表数据
        List<HrmsApplicationFormAttrDO> oldAttrList = formDetailBO.getAttrDOS();
        oldAttrList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(item);
            item.setLastUpdDate(new Date());
        });
        List<HrmsApplicationFormRelationDO> oldRelationList = formDetailBO.getRelationDOS();
        oldRelationList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(item);
        });
        hrmsAttendanceApprovalManage.formUpdate(hrmsApplicationFormDO, oldRelationList, oldAttrList);
        hrmsAttendanceApprovalManage.formAdd(null, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO, userLeaveStageDetailList, userLeaveRecord);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    @Override
    public ApprovalResultVO outOfOfficeAdd(OutOfOfficeAddParam param) {
        log.info("outOfOfficeAdd | OutOfOfficeAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        userBaseInfoBuild(null, param, null);
        // mex特殊校验
        checkMexUser(param.getUserId(), HrmsErrorCodeEnums.WAREHOUSE_NOT_ALLOW_OUT_OF_WORK, EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE);
        //暂存不校验任何信息，直接落库成功，提交时校验
        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList = new ArrayList<>();
        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        // 外勤不需要操作假期详情数据列表
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        if (param.getOperationType() == 1) {
            abnormalAttendanceDO = userAbnormalRecordCheck(param.getAbnormalId());
            outOfOfficeAddDataCheck(param);
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode());
        }
        //注意，不仅需要构建审批表信息，正常考勤表也需要落库，状态为未生效,注意假期比例也是为空，只有审批通过，才会扣除假期
        outOfOfficeDataAddBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            //暂存不用落正常考勤表
            hrmsAttendanceApprovalManage.formAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, null, null, userLeaveStageDetailList, null);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        outOfOfficeAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        hrmsApplicationFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        hrmsAttendanceApprovalManage.formAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO, userLeaveStageDetailList, null);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResultVO outOfOfficeUpdate(OutOfOfficeAddParam param) {
        log.info("outOfOfficeUpdate | OutOfOfficeAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        userBaseInfoBuild(null, param, null);
        // mex特殊校验
        checkMexUser(param.getUserId(), HrmsErrorCodeEnums.WAREHOUSE_NOT_ALLOW_OUT_OF_WORK, EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE);
        HrmsApplicationFormDetailBO formDetailBO = hrmsApplicationFormManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //暂存不校验必填项，提交时校验，暂存后进入单据管理-状态为暂存待办
        HrmsApplicationFormDO hrmsApplicationFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList = new ArrayList<>();
        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        // 外勤不需要操作假期详情数据列表
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        if (param.getOperationType() == 1) {
            abnormalAttendanceDO = userAbnormalRecordCheck(param.getAbnormalId());
            outOfOfficeAddDataCheck(param);
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode());
        }
        outOfOfficeDataUpdateBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            //落库,需要删除所有旧的属性表/关联表数据   主表不能删除在新增，要保持单号的一致，主表更新
            List<HrmsApplicationFormAttrDO> oldAttrList = formDetailBO.getAttrDOS();
            oldAttrList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdate(item);
                item.setLastUpdDate(new Date());
            });
            List<HrmsApplicationFormRelationDO> oldRelationList = formDetailBO.getRelationDOS();
            oldRelationList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdate(item);
            });
            hrmsAttendanceApprovalManage.formUpdate(hrmsApplicationFormDO, oldRelationList, oldAttrList);
            hrmsAttendanceApprovalManage.formAdd(null, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, null, null, userLeaveStageDetailList, null);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        outOfOfficeAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);
        //本次保存是否是驳回后重提
        if (hrmsApplicationFormDO.getApprovalId() != null && StringUtils.isNotBlank(hrmsApplicationFormDO.getApprovalProcessInfo())) {
            ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO = JSON.parseObject(hrmsApplicationFormDO.getApprovalProcessInfo(), ApprovalPushStatusMsgDTO.class);
            if (approvalPushStatusMsgDTO != null && approvalPushStatusMsgDTO.getStatus().equals(-2)) {
                initInfoApiDTO.setResubmitApprovalId(hrmsApplicationFormDO.getApprovalId());
            }
        }
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        hrmsApplicationFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        //需要删除所有旧的属性表/关联表数据
        List<HrmsApplicationFormAttrDO> oldAttrList = formDetailBO.getAttrDOS();
        oldAttrList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(item);
            item.setLastUpdDate(new Date());
        });
        List<HrmsApplicationFormRelationDO> oldRelationList = formDetailBO.getRelationDOS();
        oldRelationList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(item);
        });
        hrmsAttendanceApprovalManage.formUpdate(hrmsApplicationFormDO, oldRelationList, oldAttrList);
        hrmsAttendanceApprovalManage.formAdd(null, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO, userLeaveStageDetailList, null);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    @Override
    public ApprovalResultVO reissueCardAdd(ReissueCardAddParam param) {
        log.info("reissueCardAdd | ReissueCardAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        userBaseInfoBuild(null, null, param);
        //暂存不校验任何信息，直接落库成功，提交时校验
        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList = new ArrayList<>();
        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        //特殊操作，暂存，也需要异常的天数
        if (param.getOperationType() == 0 && param.getAbnormalId() != null) {
            List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectByIdList(Arrays.asList(param.getAbnormalId()));
            if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
                throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
            }
        }

        if (Objects.isNull(param.getAbnormalId())) {
            param.setReissueCardType(AttendanceAbnormalTypeEnum.REISSUE_CARD.getCode());
        }

        if (param.getOperationType() == 1) {
            abnormalAttendanceDO = reissueAddDataCheck(param);
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode());
        }
        //注意，审批发起后，需要先扣除打卡次数1次
        reissueCardDataAddBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            hrmsAttendanceApprovalManage.reissueFormAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, null, null, null);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        reissueCardAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        hrmsApplicationFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        hrmsAttendanceApprovalManage.reissueFormAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, param.getUserCardConfigDO(), abnormalAttendanceDO);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    @Override
    public ApprovalResultVO addDuration(AddDurationParam param) {
        log.info("addDuration | AddDurationParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();

        Long userId = param.getUserId();
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(userId);
        if (hrmsUserInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        param.setUserCode(hrmsUserInfoDO.getUserCode());
        param.setUserName(hrmsUserInfoDO.getUserName());
        param.setDeptId(hrmsUserInfoDO.getDeptId());
        param.setPostId(hrmsUserInfoDO.getPostId());
        param.setCountry(hrmsUserInfoDO.getLocationCountry());
        param.setOriginCountry(hrmsUserInfoDO.getOriginCountry());
        param.setIsWarehouseStaff(hrmsUserInfoDO.getIsWarehouseStaff());

        //暂存不校验任何信息，直接落库成功，提交时校验
        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList = new ArrayList<>();
        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
        List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectByIdList(Collections.singletonList(param.getAbnormalId()));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);

        if (param.getOperationType() == 1) {
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode());
        }

        addDurationDateBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            hrmsAttendanceApprovalManage.addDurationAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, null, null);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        addDurationApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList, abnormalAttendanceDO);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        hrmsApplicationFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        hrmsAttendanceApprovalManage.addDurationAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResultVO reissueCardUpdate(ReissueCardAddParam param) {
        log.info("reissueCardUpdate | ReissueCardAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        userBaseInfoBuild(null, null, param);
        HrmsApplicationFormDetailBO formDetailBO = hrmsApplicationFormManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //暂存不校验必填项，提交时校验，暂存后进入单据管理-状态为暂存待办
        HrmsApplicationFormDO hrmsApplicationFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList = new ArrayList<>();
        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        //特殊操作，暂存，也需要异常的天数
        if (param.getOperationType() == 0 && param.getAbnormalId() != null) {
            List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectByIdList(Arrays.asList(param.getAbnormalId()));
            if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
                throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
            }
//            param.setReissueCardDayId(abnormalAttendanceDOList.get(0).getDayId());
        }
        if (Objects.isNull(param.getAbnormalId())) {
            param.setReissueCardType(AttendanceAbnormalTypeEnum.REISSUE_CARD.getCode());
        }
        if (param.getOperationType() == 1) {
            abnormalAttendanceDO = reissueAddDataCheck(param);
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode());
        }
        reissueCardDataUpdateBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            //落库,需要删除所有旧的属性表/关联表数据   主表不能删除在新增，要保持单号的一致，主表更新
            List<HrmsApplicationFormAttrDO> oldAttrList = formDetailBO.getAttrDOS();
            oldAttrList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdate(item);
                item.setLastUpdDate(new Date());
            });
            List<HrmsApplicationFormRelationDO> oldRelationList = formDetailBO.getRelationDOS();
            oldRelationList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdate(item);
            });
            hrmsAttendanceApprovalManage.formUpdate(hrmsApplicationFormDO, oldRelationList, oldAttrList);
            hrmsAttendanceApprovalManage.reissueFormAdd(null, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, null, null, null);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        reissueCardAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);
        //本次保存是否是驳回后重提
        if (hrmsApplicationFormDO.getApprovalId() != null && StringUtils.isNotBlank(hrmsApplicationFormDO.getApprovalProcessInfo())) {
            ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO = JSON.parseObject(hrmsApplicationFormDO.getApprovalProcessInfo(), ApprovalPushStatusMsgDTO.class);
            if (approvalPushStatusMsgDTO != null && approvalPushStatusMsgDTO.getStatus().equals(-2)) {
                initInfoApiDTO.setResubmitApprovalId(hrmsApplicationFormDO.getApprovalId());
            }
        }
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        hrmsApplicationFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        //需要删除所有旧的属性表/关联表数据
        List<HrmsApplicationFormAttrDO> oldAttrList = formDetailBO.getAttrDOS();
        oldAttrList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(item);
            item.setLastUpdDate(new Date());
        });
        List<HrmsApplicationFormRelationDO> oldRelationList = formDetailBO.getRelationDOS();
        oldRelationList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(item);
        });
        hrmsAttendanceApprovalManage.formUpdate(hrmsApplicationFormDO, oldRelationList, oldAttrList);
        hrmsAttendanceApprovalManage.reissueFormAdd(null, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, param.getUserCardConfigDO(), abnormalAttendanceDO);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    @Override
    public ApprovalResultVO leaveRevokeAdd(LeaveRevokeAddParam param) {
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        HrmsApplicationFormDetailBO formDetailBO;
        if (param.getApplicationFormId() != null) {
            formDetailBO = hrmsApplicationFormManage.getFormDetailById(param.getApplicationFormId());
        } else {
            formDetailBO = hrmsApplicationFormManage.getFormDetailByCode(param.getApplicationFormCode());
        }
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        HrmsApplicationFormDO leaveFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOS();
        Map<String, HrmsApplicationFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        // mex特殊校验
        Long userId = leaveFormDO.getUserId();
        List<String> employeeTypeList = Lists.newArrayList(EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode());
        checkMexUser(userId, HrmsErrorCodeEnums.WAREHOUSE_EMPLOYEE_NOT_ALLOW_REVOKE, employeeTypeList);

        //防止被重复撤销
        repeatRevokeCheck(leaveFormDO.getId());
        HrmsApplicationFormAttrDO leaveStartDate = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
        if (leaveStartDate == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.LEAVE_START_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.LEAVE_START_DATE_NOT_EMPTY.getDesc()));
        }
        Date startDate = DateUtil.parse(leaveStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Date nowDate = new Date();
        //获取用户的薪资方案
        //HrmsSalaryConfigDO hrmsSalaryConfigDO = hrmsAttendanceBaseService.getUserSalaryConfig(leaveFormDO.getUserId());
        //查询是否超过请假时间
        //leaveDataConfirmCycleCheck(nowDate, hrmsSalaryConfigDO, startDate);

        // 考勤周期
        HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(leaveFormDO.getUserId());
        confirmCycleCheck(nowDate, userAttendanceCycleConfig, startDate);


        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOS = new ArrayList<>();
        // 【请假审核通过】销假：重新生成一条请假销假单，创建请假销假单与原请假单的关联。所以不需要操作假期详情数据列表
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        leaveRevokeDataAddBuild(param, formDetailBO, hrmsApplicationFormDO, hrmsApplicationFormRelationDOS, hrmsApplicationFormAttrDOArrayList);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        leaveRevokeAddApprovalDataBuild(initInfoApiDTO, leaveFormDO.getApprovalId(), hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        hrmsApplicationFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        hrmsAttendanceApprovalManage.formAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOS, hrmsApplicationFormAttrDOArrayList, null, null, userLeaveStageDetailList, null);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * mex特殊校验
     *
     * @param userId        用户id
     * @param errorCodeEnum 异常枚举
     */
    private void checkMexUser(Long userId, HrmsErrorCodeEnums errorCodeEnum, List<String> employeeTypeList) {
        if (ObjectUtil.equal(mexQwbCheckFlag, "false")) {
            log.info("mexQwbCheckFlag is false");
            return;
        }
        if (ObjectUtil.isNull(userId)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_VALID_ERROR);
        }
        // 查询用户
        HrmsUserInfoDO userInfo = hrmsUserInfoService.getByUserId(userId);
        if (ObjectUtil.isNull(userInfo)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS);
        }
        if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(userInfo.getLocationCountry())
                && ObjectUtil.equal(userInfo.getIsWarehouseStaff(), BusinessConstant.Y)
                && employeeTypeList.contains(userInfo.getEmployeeType())) {
            // mex + 仓内 + 员工、挂靠 不允许销假
            throw BusinessLogicException.getException(errorCodeEnum);
        }
    }


    @Override
    public ApprovalResultVO outOfOfficeRevokeAdd(OutOfOfficeRevokeAddParam param) {
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        HrmsApplicationFormDetailBO formDetailBO = null;
        if (param.getApplicationFormId() != null) {
            formDetailBO = hrmsApplicationFormManage.getFormDetailById(param.getApplicationFormId());
        } else {
            formDetailBO = hrmsApplicationFormManage.getFormDetailByCode(param.getApplicationFormCode());
        }
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        HrmsApplicationFormDO leaveFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOS();
        Map<String, HrmsApplicationFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        // mex特殊校验
        checkMexUser(leaveFormDO.getUserId(), HrmsErrorCodeEnums.WAREHOUSE_NOT_ALLOW_OUT_OF_WORK, EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE);
        //防止被重复撤销
        repeatRevokeCheck(leaveFormDO.getId());
        HrmsApplicationFormAttrDO outOfOfficeStartDate = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode());
        if (outOfOfficeStartDate == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getDesc()));
        }
        Date startDate = DateUtil.parse(outOfOfficeStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Date nowDate = new Date();
        //获取用户的薪资方案
        //HrmsSalaryConfigDO hrmsSalaryConfigDO = hrmsAttendanceBaseService.getUserSalaryConfig(leaveFormDO.getUserId());
        //查询是否超过请假时间
        //leaveDataConfirmCycleCheck(nowDate, hrmsSalaryConfigDO, startDate);
        // 考勤周期
        HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(leaveFormDO.getUserId());
        confirmCycleCheck(nowDate, userAttendanceCycleConfig, startDate);

        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOS = new ArrayList<>();
        // 【外勤申请通过】外勤销假：重新生成一条外勤销假单，创建外勤销假单与外勤申请单的关联。所以不需要操作假期详情数据列表
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        outOfOfficeRevokeDataAddBuild(param, formDetailBO, hrmsApplicationFormDO, hrmsApplicationFormRelationDOS, hrmsApplicationFormAttrDOArrayList);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        outOfOfficeRevokeAddApprovalDataBuild(initInfoApiDTO, leaveFormDO.getApprovalId(), hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        hrmsApplicationFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        hrmsAttendanceApprovalManage.formAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOS, hrmsApplicationFormAttrDOArrayList, null, null, userLeaveStageDetailList, null);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    @Override
    public ApprovalResultVO reissueCardRevokeAdd(ReissueCardRevokeAddParam param) {
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        HrmsApplicationFormDetailBO formDetailBO = null;
        if (param.getApplicationFormId() != null) {
            formDetailBO = hrmsApplicationFormManage.getFormDetailById(param.getApplicationFormId());
        } else {
            formDetailBO = hrmsApplicationFormManage.getFormDetailByCode(param.getApplicationFormCode());
        }
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        HrmsApplicationFormDO leaveFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOS();
        Map<String, HrmsApplicationFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        // mex特殊校验
        checkMexUser(leaveFormDO.getUserId(), HrmsErrorCodeEnums.WAREHOUSE_NOT_ALLOW_CARD_REVOKE, EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE);
        //防止被重复撤销
        repeatRevokeCheck(leaveFormDO.getId());
        HrmsApplicationFormAttrDO reissueCardDayId = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode());
        if (reissueCardDayId == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ABNORMAL_RECORD_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ABNORMAL_RECORD_DATE_NOT_EMPTY.getDesc()));
        }
        Date startDate = DateUtil.parse(reissueCardDayId.getAttrValue(), "yyyyMMdd");
        Date nowDate = new Date();
        //获取用户的薪资方案
        //HrmsSalaryConfigDO hrmsSalaryConfigDO = hrmsAttendanceBaseService.getUserSalaryConfig(leaveFormDO.getUserId());
        //查询是否超过请假时间
        //leaveDataConfirmCycleCheck(nowDate, hrmsSalaryConfigDO, startDate);
        // 考勤周期
        HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(leaveFormDO.getUserId());
        confirmCycleCheck(nowDate, userAttendanceCycleConfig, startDate);

        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOS = new ArrayList<>();
        // 【补卡申请通过】撤销补卡申请：重新生成一条补卡取消单，创建补卡取消单与补卡申请单的关联。所以不需要操作假期详情数据列表
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        reissueCardRevokeDataAddBuild(param, formDetailBO, hrmsApplicationFormDO, hrmsApplicationFormRelationDOS, hrmsApplicationFormAttrDOArrayList);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        reissueCardRevokeAddApprovalDataBuild(initInfoApiDTO, leaveFormDO.getApprovalId(), hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        hrmsApplicationFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        hrmsAttendanceApprovalManage.formAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOS, hrmsApplicationFormAttrDOArrayList, null, null, userLeaveStageDetailList, null);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    @Override
    public void cancel(Long formId) {
        //查询当前单据是否存在
        HrmsApplicationFormDetailBO detailBO = hrmsApplicationFormManage.getFormDetailById(formId);
        HrmsApplicationFormDO formDO = detailBO.getFormDO();
        if (formDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //非这些状态的单据不处理
        if (!StringUtils.equalsIgnoreCase(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode(), formDO.getFormStatus())
                && !StringUtils.equalsIgnoreCase(HrAttendanceApplicationFormStatusEnum.REJECT.getCode(), formDO.getFormStatus())) {
            throw BusinessException.get(HrmsErrorCodeEnums.FORM_STATUS_NOT_CANCEL.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.FORM_STATUS_NOT_CANCEL.getDesc()));
        }
        //查询该审批单有咩有关联异常考勤，如果有，异常考勤状态变为待审批
        List<Long> abnormalIdList = hrmsApplicationFormRelationManage.selectRelationByFormIdList(Arrays.asList(formId)).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode())).map(item -> item.getRelationId()).collect(Collectors.toList());
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        HrmsAttendanceUserCardConfigDO userCardConfigDO = null;
        if (CollectionUtils.isNotEmpty(abnormalIdList)) {
            List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectByIdList(abnormalIdList);
            if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
                throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
            }
            abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
            if (!StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
                throw BusinessException.get(HrmsErrorCodeEnums.ABNORMAL_HAS_BEEN_HANDLER.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ABNORMAL_HAS_BEEN_HANDLER.getDesc()));
            }
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);

        }
        userCardConfigDO = reissueCardCountHandler(detailBO);

        /*
            取消：需要判断当前审批单状态
                驳回状态的取消：不需要返假期（因为驳回的时候已经返回假期了）
                非驳回状态的（也就是只有审核中状态的，上面有校验，只有审核中、驳回才能走取消逻辑）取消：需要返假期
             注意：只有请假申请单 + 非驳回状态的，取消的才会【返还假期余额、扣减已使用假期余额】。
             销假这种场景，只有审核通过的时候才会【返还假期余额、扣减已使用假期余额】
        */
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailInfoList = Lists.newArrayList();
        // 处理用户请假记录
        HrmsUserLeaveRecordDO userLeaveRecord = null;
        if (ObjectUtil.equal(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.LEAVE.getCode()) &&
                ObjectUtil.notEqual(formDO.getFormStatus(), HrAttendanceApplicationFormStatusEnum.REJECT.getCode())) {

            userLeaveRecord = handlerUserLeaveStageDetailList(detailBO, userLeaveStageDetailInfoList, "【hrms或clover审核中】取消请假申请单");
        }
        bpmCreateApprovalService.backApply(detailBO.getFormDO().getApprovalId());
        formDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.CANCEL.getCode());
        fillDOUpdate(formDO);
        hrmsAttendanceApprovalManage.cancel(formDO, abnormalAttendanceDO, userCardConfigDO, userLeaveStageDetailInfoList, userLeaveRecord);
    }

    /**
     * 构建假期详情表数据
     *
     * @param detailBO                     假期申请单数据、假期申请单详情数据、考勤申请单关联表数据
     * @param userLeaveStageDetailInfoList 用来接收-需要修改的假期详情数据列表
     * @param remark                       用来接收-需要修改的假期详情数据列表
     * @return HrmsUserLeaveRecordDO
     */
    private HrmsUserLeaveRecordDO handlerUserLeaveStageDetailList(HrmsApplicationFormDetailBO detailBO,
                                                                  List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailInfoList,
                                                                  String remark) {
        // 假期申请单信息
        HrmsApplicationFormDO formInfo = detailBO.getFormDO();
        // 假期申请单详情数据
        List<HrmsApplicationFormAttrDO> attrInfo = detailBO.getAttrDOS();
        // 获取这笔销假申请所关联的原始申请单
        List<HrmsApplicationFormRelationDO> relationInfo = detailBO.getRelationDOS();
        // 获取申请单详情数据-假期配置主键信息
        List<HrmsApplicationFormAttrDO> configIdInfo = attrInfo.stream().
                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.configID.getLowerCode())).
                collect(Collectors.toList());
        // 获取申请单详情数据-请假名称信息
        List<HrmsApplicationFormAttrDO> leaveNameInfo = attrInfo.stream().
                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode())).
                collect(Collectors.toList());
        // 获取申请单详情数据-请假开始时间
        List<HrmsApplicationFormAttrDO> leaveStartDateInfo = attrInfo.stream().
                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).
                collect(Collectors.toList());
        // 获取申请单详情数据-请假结束时间
        List<HrmsApplicationFormAttrDO> leaveEndDateInfo = attrInfo.stream().
                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).
                collect(Collectors.toList());
        // 如果请假名称集合为空，直接返回
        if (CollUtil.isEmpty(leaveNameInfo)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.THERE_IS_NO_LEAVE_TYPE_INFORMATION_FOR_THE_DETAILS_OF_THE_APPLICATION_FORM);
        }
        // 如果请假开始时间、结束时间集合为空，直接返回
        if (CollUtil.isEmpty(leaveStartDateInfo) || CollUtil.isEmpty(leaveEndDateInfo)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.THERE_IS_NO_LEAVE_START_OR_END_TIME_FOR_THE_DETAILS_OF_THE_APPLICATION_FORM);
        }
        // 转日期格式
        Date startDate = DateUtil.parse(leaveStartDateInfo.get(0).getAttrValue(), DatePattern.NORM_DATETIME_PATTERN);
        Date endDate = DateUtil.parse(leaveEndDateInfo.get(0).getAttrValue(), DatePattern.NORM_DATETIME_PATTERN);

        // 查询该假期配置
        // 修改为根据人员范围查询
        HrmsCompanyLeaveConfigDO companyLeaveConfigDO;
        if (CollectionUtils.isNotEmpty(configIdInfo) && StringUtils.isNotBlank(configIdInfo.get(0).getAttrValue())) {
            companyLeaveConfigDO = hrmsCompanyLeaveConfigManage.getById(Long.valueOf(configIdInfo.get(0).getAttrValue()));
        } else {
            // 存在历史数据保留此方法
            companyLeaveConfigDO = selectByNameAndUserCode(Arrays.asList(formInfo.getUserCode()), leaveNameInfo.get(0).getAttrValue());
        }
        // 重新计算每天请假时间
        List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
        dayDurationInfoHandler(formInfo.getUserId(), startDate, endDate, companyLeaveConfigDO, dayDurationInfoDTOList, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
        // 计算请假总时长-单位分钟
        BigDecimal totalLeaveTime = handlerLeaveTotalTime(dayDurationInfoDTOList);
        // 用户请假记录数据 需要总的请假时长，这边重新接收一下。保证不会变化
        BigDecimal userLeaveRecordTotalLeaveTime = BigDecimal.ZERO;

        UserLeaveDetailQuery query = new UserLeaveDetailQuery();
        // param参数在前面已经校验了
        query.setUserId(formInfo.getUserId());
        if (CollectionUtils.isNotEmpty(configIdInfo) && StringUtils.isNotBlank(configIdInfo.get(0).getAttrValue())) {
            query.setConfigId(Long.valueOf(configIdInfo.get(0).getAttrValue()));
        } else {
            query.setLeaveName(leaveNameInfo.get(0).getAttrValue());
        }
        List<HrmsUserLeaveDetailDO> userLeaveDetail = hrmsUserLeaveDetailManage.selectUserLeaveDetail(query);
        List<Long> leaveIdList = userLeaveDetail.stream().map(HrmsUserLeaveDetailDO::getId).collect(Collectors.toList());
//        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = hrmsUserLeaveStageDetailManage.selectByLeaveId(leaveIdList);
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = hrmsUserLeaveStageDetailManage.selectByCondition(UserLeaveStageDetailQuery
                .builder()
                .leaveIdList(leaveIdList)
                .build());
        // 先返还非结转的假期，然后再返还结转的假期余额,将假期先按照是否结转排序后再按照阶段排序
        // 将假期按照阶段正序，优先返还比率低（其实就是请假扣钱多的）的假期余额
        List<HrmsUserLeaveStageDetailDO> orthodoxUserLeaveStageDetailList = userLeaveStageDetailList.stream()
                .filter(item -> WhetherEnum.NO.getKey().equals(item.getIsInvalid()))
                .sorted(Comparator.comparingInt(HrmsUserLeaveStageDetailDO::getLeaveMark)
                        .thenComparing(HrmsUserLeaveStageDetailDO::getPercentSalary))
                .collect(Collectors.toList());
        // 遍历正序的假期详情信息数据
        for (HrmsUserLeaveStageDetailDO stageDetailInfo : orthodoxUserLeaveStageDetailList) {
            // 不是最后一个阶梯，并且有假期<=0
            // 当前逻辑 ：1. 不是最后一个阶梯，判断假期余额是否小于等于0，如果小于等于0则跳过该假期阶梯。2. 如果是最后一个阶梯，直接使用最后一个阶梯
            //if (i != reversedUserLeaveStageDetailList.size() - 1 && stageDetailInfo.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) < 1) {
            //    continue;
            //}

            // 如果当前阶段所使用假期余额 <= 0，则表示无法扣减当前阶段已使用假期余额（也表示之前请假扣减假期余额、增加已使用假期余额，没有执行到该阶段假期）
            if (stageDetailInfo.getLeaveUsedMinutes().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            /*
                当前阶段(请假时长 - 已使用假期余额)
                    大于0:该阶段已使用假期不足以返还用户全部请假时间，需要加上下一个阶段的已使用假期余额
                    等于0:正好完全返还该阶段的请假时长
                    小于0:可以完全返还该阶段的请假时长
            */
            BigDecimal usedDifference = totalLeaveTime.subtract(stageDetailInfo.getLeaveUsedMinutes());
            if (usedDifference.compareTo(BigDecimal.ZERO) <= 0) {
                stageDetailInfo.setLeaveUsedMinutes(stageDetailInfo.getLeaveUsedMinutes().subtract(totalLeaveTime));
                stageDetailInfo.setLeaveResidueMinutes(stageDetailInfo.getLeaveResidueMinutes().add(totalLeaveTime));
                stageDetailInfo.setLastUpdDate(new Date());
                stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
                stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
                userLeaveStageDetailInfoList.add(stageDetailInfo);
                userLeaveRecordTotalLeaveTime = userLeaveRecordTotalLeaveTime.add(totalLeaveTime);
                break;
            }
            // 提前存储该阶段假期的已使用余额，为后续循环扣减使用
            BigDecimal subtractLeaveUsedMinutes = stageDetailInfo.getLeaveUsedMinutes();
            // 大于0：直接该阶段假期余额 = 假期余额 + 已使用假期余额，已使用假期余额变成0
            stageDetailInfo.setLeaveResidueMinutes(stageDetailInfo.getLeaveResidueMinutes().add(stageDetailInfo.getLeaveUsedMinutes()));
            stageDetailInfo.setLeaveUsedMinutes(BigDecimal.ZERO);
            stageDetailInfo.setLastUpdDate(new Date());
            stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
            stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
            userLeaveStageDetailInfoList.add(stageDetailInfo);
            // 更新请假时间，为后续循环准备
            totalLeaveTime = totalLeaveTime.subtract(subtractLeaveUsedMinutes);
            userLeaveRecordTotalLeaveTime = userLeaveRecordTotalLeaveTime.add(subtractLeaveUsedMinutes);
        }

        // 封装请假记录表
        LeaveAddParam leaveAddParam = new LeaveAddParam();
        leaveAddParam.setUserId(formInfo.getUserId());
        leaveAddParam.setUserCode(formInfo.getUserCode());
        if (CollectionUtils.isNotEmpty(configIdInfo) && StringUtils.isNotBlank(configIdInfo.get(0).getAttrValue())) {
            leaveAddParam.setConfigId(Long.valueOf(configIdInfo.get(0).getAttrValue()));
        }
        leaveAddParam.setLeaveType(companyLeaveConfigDO.getLeaveType());
        leaveAddParam.setLeaveName(leaveNameInfo.get(0).getAttrValue());
        leaveAddParam.setLeaveStartDate(startDate);
        leaveAddParam.setLeaveEndDate(endDate);

        //查询假期结转配置，如果请假开始时间小于结转时间，当前时间大于等于结转时间，则代表当前假期已经结转，收入记录备注存在结转假期
        //如果请假开始时间小于失效时间，当前时间大于等于失效时间，则代表当前结转假期已经失效，收入记录备注存在失效假期
        Integer carryOverStatus = hrmsCompanyLeaveConfigCarryOverService.checkIfCarryOver(companyLeaveConfigDO.getId()
                , formInfo.getCreateDate()
                , companyLeaveConfigDO.getCountry()
                , userLeaveStageDetailList);
        switch (carryOverStatus) {
            case 1:
                remark = "【假期已经结转，存在部分假期不返还】 The holiday has been carried forward, and some data may not be returned";
                break;
            case 2:
                remark = "【假期已经失效，存在部分假期不返还】 The holiday has expired, some data may not be returned";
                break;
            default:
                break;
        }

        return buildUserLeaveRecord(userLeaveRecordTotalLeaveTime, leaveAddParam, LeaveTypeEnum.CANCEL.getCode(), remark);
    }

    /**
     * 补卡申请  除了审批通过之外的一切操作，都要归还补卡次数
     */
    private HrmsAttendanceUserCardConfigDO reissueCardCountHandler(HrmsApplicationFormDetailBO applicationFormDetailBO) {
        HrmsApplicationFormDO formDO = applicationFormDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> attrDOS = applicationFormDetailBO.getAttrDOS();
        if (!CollUtil.contains(HrAttendanceApplicationFormTypeEnum.TYPE_OF_REISSUE_CARD_TYPE, formDO.getFormType())) {
            return null;
        }
        List<HrmsApplicationFormAttrDO> reissueCardDayIdDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reissueCardDayIdDO)) {
            return null;
        }
        Long reissueCardDayId = Long.valueOf(reissueCardDayIdDO.get(0).getAttrValue());
        Date reissueCardDayDate = DateUtil.parse(reissueCardDayId.toString(), "yyyyMMdd");

        List<HrmsAttendanceUserCardConfigDO> userCardConfigDOS = hrmsAttendanceUserCardConfigManage.selectByUserIdList(Arrays.asList(formDO.getUserId()));
        if (CollectionUtils.isEmpty(userCardConfigDOS)) {
            return null;
        }
        List<HrmsAttendanceUserCardConfigDO> attendanceUserCardConfigDOS = userCardConfigDOS.stream()
                .filter(item -> item.getCycleStartDate().compareTo(reissueCardDayDate) < 1 && item.getCycleEndDate().compareTo(reissueCardDayDate) > -1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(attendanceUserCardConfigDOS)) {
            return null;
        }
        HrmsAttendanceUserCardConfigDO attendanceUserCardConfigDO = attendanceUserCardConfigDOS.get(0);
        attendanceUserCardConfigDO.setUsedCardCount(attendanceUserCardConfigDO.getUsedCardCount() - 1);
        this.fillDOUpdate(attendanceUserCardConfigDO);
        return attendanceUserCardConfigDO;
    }

    @Override
    public AttendanceApplicationFromDetailVO getFromDetail(Long formId) {
        //查询当前单据是否存在
        HrmsApplicationFormDetailBO detailBO = hrmsApplicationFormManage.getFormDetailById(formId);
        HrmsApplicationFormDO formDO = detailBO.getFormDO();
        if (formDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        List<HrmsApplicationFormRelationDO> relationDOS = detailBO.getRelationDOS();
        if (CollectionUtils.isEmpty(relationDOS)) {
            relationDOS = new ArrayList<>();
        }
        AttendanceApplicationFromDetailVO detailVO = BeanUtils.convert(formDO, AttendanceApplicationFromDetailVO.class);
        List<HrmsApplicationFormAttrDO> attrDOS = detailBO.getAttrDOS();
        Map<String, HrmsApplicationFormAttrDO> attrMap = attrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));

        //去user表查询这个人的最新信息，可能部门岗位都变了
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(formDO.getUserId());
        if (hrmsUserInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        HrmsEntDeptDO entDeptDO = hrmsDeptManage.selectById(hrmsUserInfoDO.getDeptId());
        if (entDeptDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc()));
        }
        HrmsEntPostDO entPostDO = null;
        // 如果是仓内补卡单据，不需要查询岗位，因为仓内外包没有岗位，仓内自有也不查询了:(mex不存在销卡功能，所以不存在其他单据类型)
        if (ObjectUtil.notEqual(detailVO.getFormType(), HrAttendanceApplicationFormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode())) {
            entPostDO = hrmsEntPostDao.getById(hrmsUserInfoDO.getPostId());
            if (entPostDO == null) {
                throw BusinessException.get(HrmsErrorCodeEnums.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.POST_NOT_EXITS.getDesc()));
            }
        }
        detailVO.setUserId(hrmsUserInfoDO.getId());
        detailVO.setUserCode(hrmsUserInfoDO.getUserCode());
        detailVO.setUserName(hrmsUserInfoDO.getUserName());
        detailVO.setDeptId(hrmsUserInfoDO.getDeptId());
        detailVO.setDeptName(RequestInfoHolder.isChinese() ? entDeptDO.getDeptNameCn() : entDeptDO.getDeptNameEn());
        detailVO.setPostId(hrmsUserInfoDO.getPostId());
        if (ObjectUtil.isNotNull(entPostDO)) {
            detailVO.setPostName(RequestInfoHolder.isChinese() ? entPostDO.getPostNameCn() : entPostDO.getPostNameEn());
        }
        detailVO.setCountry(hrmsUserInfoDO.getLocationCountry());
        detailVO.setApplicationCode(formDO.getApplicationCode());
        detailVO.setApplicationFormId(formDO.getId());
        detailVO.setFormType(formDO.getFormType());
        detailVO.setFormStatus(formDO.getFormStatus());
        HrmsApplicationFormAttrDO configId = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.configID.getLowerCode());
        if (configId != null) {
            detailVO.setConfigId(Long.valueOf(configId.getAttrValue()));
        }
        // 由于单据历史数据问题，请假单leave_type字段暂时不修改，后续leave_type字段都存储假期名称
        HrmsApplicationFormAttrDO leaveName = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode());
        if (leaveName != null) {
            detailVO.setLeaveName(leaveName.getAttrValue());
            detailVO.setLeaveNameByLang(leaveName.getAttrValue());
            //获取假期类型枚举进行翻译
            Map<String, DictVO> leaveTypeEnumMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.HRMS_ATTENDANCE_LEAVE_TYPE);
            Map<String, DictVO> lowerleaveTypeEnumMap = leaveTypeEnumMap.entrySet().stream().collect(Collectors.toMap(item -> item.getKey().toLowerCase(), Map.Entry::getValue));
            DictVO dictVO = lowerleaveTypeEnumMap.get(leaveName.getAttrValue().toLowerCase());
            if (Objects.nonNull(dictVO)) {
                detailVO.setLeaveNameByLang(dictVO.getDataValue());
            }
        }
        HrmsApplicationFormAttrDO leaveShortName = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveShortName.getLowerCode());
        if (leaveShortName != null) {
            detailVO.setLeaveShortName(leaveShortName.getAttrValue());
        }
        HrmsApplicationFormAttrDO leaveResidueMinutes = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveResidueMinutes.getLowerCode());
        if (leaveResidueMinutes != null) {
            detailVO.setLeaveResidueMinutes(new BigDecimal(leaveResidueMinutes.getAttrValue()));
        }
        HrmsApplicationFormAttrDO leaveUnitName = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveUnit.getLowerCode());
        if (leaveUnitName != null) {
            detailVO.setLeaveUnit(leaveUnitName.getAttrValue());
        }


        HrmsApplicationFormAttrDO leaveStartDate = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
        if (leaveStartDate != null) {
            detailVO.setLeaveStartDate(DateUtil.parse(leaveStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        HrmsApplicationFormAttrDO leaveEndDate = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode());
        if (leaveEndDate != null) {
            detailVO.setLeaveEndDate(DateUtil.parse(leaveEndDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }


        HrmsApplicationFormAttrDO remark = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode());
        if (remark != null) {
            detailVO.setRemark(remark.getAttrValue());
        }
        HrmsApplicationFormAttrDO attachmentList = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode());
        if (attachmentList != null) {
            List<AttachmentDTO> attachmentDTOS = JSONObject.parseArray(attachmentList.getAttrValue(), AttachmentDTO.class);
            detailVO.setAttachmentList(attachmentDTOS);
        }

        HrmsApplicationFormAttrDO dayDurationAttr = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode());
        if (dayDurationAttr != null) {
            List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayDurationAttr.getAttrValue(), DayDurationInfoDTO.class);
            detailVO.setDayDurationInfoDTOList(dayDurationInfoDTOList);
            BigDecimal days = BigDecimal.ZERO;
            BigDecimal hours = BigDecimal.ZERO;
            BigDecimal minutes = BigDecimal.ZERO;
            for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                days = days.add(dayDurationInfoDTO.getDays());
                hours = hours.add(dayDurationInfoDTO.getHours());
                minutes = minutes.add(dayDurationInfoDTO.getMinutes());
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.LEAVE.getCode())) {
                if (StringUtils.equalsIgnoreCase(detailVO.getLeaveUnit(), LeaveUnitEnum.DAYS.getCode())) {
                    detailVO.setExpectedLeaveTime(days + "days");
                    if (RequestInfoHolder.isChinese()) {
                        detailVO.setExpectedLeaveTime(days + "天");
                    }
                }
                if (StringUtils.equalsIgnoreCase(detailVO.getLeaveUnit(), LeaveUnitEnum.HOURS.getCode())) {
                    detailVO.setExpectedLeaveTime(days + "days" + hours + "hours");
                    if (RequestInfoHolder.isChinese()) {
                        detailVO.setExpectedLeaveTime(days + "天" + hours + "小时");
                    }
                }
                if (StringUtils.equalsIgnoreCase(detailVO.getLeaveUnit(), LeaveUnitEnum.MINUTES.getCode())) {
                    detailVO.setExpectedLeaveTime(days + "days" + hours + "hours" + minutes + "minutes");
                    if (RequestInfoHolder.isChinese()) {
                        detailVO.setExpectedLeaveTime(days + "天" + hours + "小时" + minutes + "分钟");
                    }
                }
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode())) {
                detailVO.setExpectedLeaveTime(days + "days" + hours + "hours");
                if (RequestInfoHolder.isChinese()) {
                    detailVO.setExpectedLeaveTime(days + "天" + hours + "小时");
                }
            }
        }

        HrmsApplicationFormAttrDO outOfOfficeStartDate = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode());
        if (outOfOfficeStartDate != null) {
            detailVO.setOutOfOfficeStartDate(DateUtil.parse(outOfOfficeStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        HrmsApplicationFormAttrDO outOfOfficeEndDate = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode());
        if (outOfOfficeEndDate != null) {
            detailVO.setOutOfOfficeEndDate(DateUtil.parse(outOfOfficeEndDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }

        //补卡相关信息
        HrmsApplicationFormAttrDO reissueCardDayId = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode());
        if (reissueCardDayId != null) {
            detailVO.setReissueCardDayId(Long.valueOf(reissueCardDayId.getAttrValue()));
        }
        HrmsApplicationFormAttrDO reissueCardType = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.reissueCardType.getLowerCode());
        if (reissueCardType != null) {
            detailVO.setReissueCardType(reissueCardType.getAttrValue());
        }
        HrmsApplicationFormAttrDO residueReissueCardCount = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.residueReissueCardCount.getLowerCode());
        if (residueReissueCardCount != null) {
            detailVO.setResidueReissueCardCount(Integer.valueOf(residueReissueCardCount.getAttrValue()));
        }
        HrmsApplicationFormAttrDO attendanceStartDate = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.attendanceStartDate.getLowerCode());
        if (attendanceStartDate != null) {
            detailVO.setAttendanceStartDate(DateUtil.parse(attendanceStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        HrmsApplicationFormAttrDO attendanceEndDate = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.attendanceEndDate.getLowerCode());
        if (attendanceEndDate != null) {
            detailVO.setAttendanceEndDate(DateUtil.parse(attendanceEndDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        HrmsApplicationFormAttrDO punchConfigClassItemInfo = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.punchConfigClassItemInfo.getLowerCode());
        if (punchConfigClassItemInfo != null) {
            detailVO.setPunchConfigClassItemInfo(punchConfigClassItemInfo.getAttrValue());
        }
        HrmsApplicationFormAttrDO actualPunchTime = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.actualPunchTime.getLowerCode());
        if (actualPunchTime != null) {
            detailVO.setActualPunchTime(DateUtil.parse(actualPunchTime.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        HrmsApplicationFormAttrDO correctPunchTime = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode());
        if (correctPunchTime != null) {
            detailVO.setCorrectPunchTime(DateUtil.parse(correctPunchTime.getAttrValue(), "yyyy-MM-dd HH:mm:ss"));
        }
        List<Long> abnormalIdList = relationDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode())).map(item -> item.getRelationId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(abnormalIdList)) {
            detailVO.setAbnormalId(abnormalIdList.get(0));
        }

        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.LEAVE.getCode())
                || StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.LEAVE_REVOKE.getCode())) {
            if (StringUtils.equalsIgnoreCase(formDO.getFormStatus(), HrAttendanceApplicationFormStatusEnum.STAGING.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormStatus(), HrAttendanceApplicationFormStatusEnum.REJECT.getCode())) {
                //获取最新的假期相关信息(暂时适配没有configId的历史数据情况)
                if (StringUtils.isNotBlank(detailVO.getLeaveType())) {
                    List<UserLeaveResidualVO> leaveResidualVOList = selectUserLeaveResidual(detailVO.getUserId()).stream()
                            .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveType(), detailVO.getLeaveType())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(leaveResidualVOList)) {
                        throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_THIS_LEAVE_TYPE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_THIS_LEAVE_TYPE.getDesc()));
                    }
                    detailVO.setConsumeLeaveType(leaveResidualVOList.get(0).getConsumeLeaveType());
                    detailVO.setIsUploadAttachment(leaveResidualVOList.get(0).getIsUploadAttachment());
                    detailVO.setLeaveUnit(leaveResidualVOList.get(0).getLeaveUnit());
                    detailVO.setLeaveShortName(leaveResidualVOList.get(0).getLeaveShortName());
                    detailVO.setLeaveResidueMinutes(leaveResidualVOList.get(0).getLeaveResidueMinutes());
                }
                //获取最新的假期相关信息
                if (Objects.nonNull(detailVO.getConfigId())) {
                    List<UserLeaveResidualVO> leaveResidualVOList = selectUserLeaveResidual(detailVO.getUserId()).stream()
                            .filter(item -> Objects.equals(item.getConfigId(), detailVO.getConfigId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(leaveResidualVOList)) {
                        throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_THIS_LEAVE_TYPE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_THIS_LEAVE_TYPE.getDesc()));
                    }
                    detailVO.setConsumeLeaveType(leaveResidualVOList.get(0).getConsumeLeaveType());
                    detailVO.setIsUploadAttachment(leaveResidualVOList.get(0).getIsUploadAttachment());
                    detailVO.setLeaveUnit(leaveResidualVOList.get(0).getLeaveUnit());
                    detailVO.setLeaveShortName(leaveResidualVOList.get(0).getLeaveShortName());
                    detailVO.setLeaveResidueMinutes(leaveResidualVOList.get(0).getLeaveResidueMinutes());
                }
                //暂存的时候，可能连请假时间都没填写，那就无需获取最新的
                if (detailVO.getLeaveStartDate() == null || detailVO.getLeaveEndDate() == null) {
                    return detailVO;
                }
            }
        }

        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode())
                || StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE_REVOKE.getCode())) {
            if (StringUtils.equalsIgnoreCase(formDO.getFormStatus(), HrAttendanceApplicationFormStatusEnum.STAGING.getCode())
                    || StringUtils.equalsIgnoreCase(formDO.getFormStatus(), HrAttendanceApplicationFormStatusEnum.REJECT.getCode())) {
                //暂存的时候，可能连请假时间都没填写，那就无需获取最新的
                if (detailVO.getOutOfOfficeStartDate() == null || detailVO.getOutOfOfficeEndDate() == null) {
                    return detailVO;
                }
            }
        }

        return detailVO;
    }

    @Override
    public void delete(Long formId) {
        //查询当前单据是否存在
        HrmsApplicationFormDetailBO detailBO = hrmsApplicationFormManage.getFormDetailById(formId);
        HrmsApplicationFormDO formDO = detailBO.getFormDO();
        if (formDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //只有暂存状态的单据可以被删除
        if (!StringUtils.equalsIgnoreCase(HrAttendanceApplicationFormStatusEnum.STAGING.getCode(), formDO.getFormStatus())) {
            throw BusinessException.get(HrmsErrorCodeEnums.ONLY_STAGE_STATUS_CAN_BE_DELETE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ONLY_STAGE_STATUS_CAN_BE_DELETE.getDesc()));
        }
        formDO.setIsDelete(BusinessConstant.Y);
        fillDOUpdate(formDO);
        List<HrmsApplicationFormRelationDO> relationDOS = detailBO.getRelationDOS();
        relationDOS.forEach(item -> {
            item.setIsDelete(BusinessConstant.Y);
            fillDOUpdate(item);
        });
        List<HrmsApplicationFormAttrDO> attrDOS = detailBO.getAttrDOS();
        attrDOS.forEach(item -> {
            item.setIsDelete(BusinessConstant.Y);
            fillDOUpdate(item);
        });
        hrmsAttendanceApprovalManage.delete(formDO, relationDOS, attrDOS);
    }

    @Override
    public List<ApprovalDetailStepRecordDTO> leavePreview(LeaveAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }

        //暂存就是保存前一步，必填数据都要填写完毕才可以
        userBaseInfoBuild(param, null, null);
        //异常判断
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = userAbnormalRecordCheck(param.getAbnormalId());
        leaveAddDataCheck(param);
        //暂存不校验任何信息，直接落库成功，提交时校验
        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOArrayList = new ArrayList<>();
        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
        //参数构建，不落库
        leaveDataAddBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOArrayList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        leaveAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmCreateApprovalService.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        //previewDTOBuild(recordApiDTOList, resultDTOList);
        previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, param.getUserCode());
        return resultDTOList;
    }

    @Override
    public List<ApprovalDetailStepRecordDTO> outOfOfficePreview(OutOfOfficeAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        //暂存就是保存前一步，必填数据都要填写完毕才可以
        userBaseInfoBuild(null, param, null);
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = userAbnormalRecordCheck(param.getAbnormalId());
        outOfOfficeAddDataCheck(param);
        //暂存不校验任何信息，直接落库成功，提交时校验
        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList = new ArrayList<>();
        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
        //参数构建，不落库
        outOfOfficeDataAddBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        leaveAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmCreateApprovalService.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        //previewDTOBuild(recordApiDTOList, resultDTOList);
        previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, param.getUserCode());
        return resultDTOList;
    }

    @Override
    public List<ApprovalDetailStepRecordDTO> reissueCardPreview(ReissueCardAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        userBaseInfoBuild(null, null, param);
        //暂存就是保存前一步，必填数据都要填写完毕才可以
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = reissueAddDataCheck(param);
        //暂存不校验任何信息，直接落库成功，提交时校验
        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList = new ArrayList<>();
        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
        //参数构建，不落库
        reissueCardDataAddBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        leaveAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmCreateApprovalService.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        //previewDTOBuild(recordApiDTOList, resultDTOList);
        previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, param.getUserCode());
        return resultDTOList;
    }

    @Override
    public List<ApprovalDetailStepRecordDTO> addDurationPreview(AddDurationParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        Long userId = param.getUserId();
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(userId);
        if (hrmsUserInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        param.setUserCode(hrmsUserInfoDO.getUserCode());
        param.setUserName(hrmsUserInfoDO.getUserName());
        param.setDeptId(hrmsUserInfoDO.getDeptId());
        param.setPostId(hrmsUserInfoDO.getPostId());
        param.setCountry(hrmsUserInfoDO.getLocationCountry());
        param.setOriginCountry(hrmsUserInfoDO.getOriginCountry());
        param.setIsWarehouseStaff(hrmsUserInfoDO.getIsWarehouseStaff());

        List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectByIdList(Collections.singletonList(param.getAbnormalId()));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);

        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList = new ArrayList<>();
        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
        //参数构建，不落库
        addDurationDateBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        addDurationApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList, abnormalAttendanceDO);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmCreateApprovalService.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, param.getUserCode());
        return resultDTOList;
    }

    @Override
    public List<ApprovalDetailStepRecordDTO> leaveRevokePreview(LeaveRevokeAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        HrmsApplicationFormDetailBO formDetailBO = hrmsApplicationFormManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        HrmsApplicationFormDO leaveFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOS();
        Map<String, HrmsApplicationFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        //防止被重复撤销
        repeatRevokeCheck(leaveFormDO.getId());
        HrmsApplicationFormAttrDO leaveStartDate = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
        if (leaveStartDate == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.LEAVE_START_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.LEAVE_START_DATE_NOT_EMPTY.getDesc()));
        }
        Date startDate = DateUtil.parse(leaveStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Date nowDate = new Date();
        //获取用户的薪资方案
        //HrmsSalaryConfigDO hrmsSalaryConfigDO = hrmsAttendanceBaseService.getUserSalaryConfig(leaveFormDO.getUserId());
        //查询是否超过请假时间
        //leaveDataConfirmCycleCheck(nowDate, hrmsSalaryConfigDO, startDate);
        // 考勤周期
        HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(leaveFormDO.getUserId());
        confirmCycleCheck(nowDate, userAttendanceCycleConfig, startDate);

        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOS = new ArrayList<>();
        leaveRevokeDataAddBuild(param, formDetailBO, hrmsApplicationFormDO, hrmsApplicationFormRelationDOS, hrmsApplicationFormAttrDOArrayList);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        leaveAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmCreateApprovalService.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        //previewDTOBuild(recordApiDTOList, resultDTOList);
        previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, leaveFormDO.getUserCode());
        return resultDTOList;
    }

    @Override
    public List<ApprovalDetailStepRecordDTO> outOfOfficeRevokePreview(OutOfOfficeRevokeAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        HrmsApplicationFormDetailBO formDetailBO = hrmsApplicationFormManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        HrmsApplicationFormDO leaveFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOS();
        Map<String, HrmsApplicationFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        //防止被重复撤销
        repeatRevokeCheck(leaveFormDO.getId());
        HrmsApplicationFormAttrDO outOfOfficeStartDate = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode());
        if (outOfOfficeStartDate == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getDesc()));
        }
        Date startDate = DateUtil.parse(outOfOfficeStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Date nowDate = new Date();
        //获取用户的薪资方案
        //HrmsSalaryConfigDO hrmsSalaryConfigDO = hrmsAttendanceBaseService.getUserSalaryConfig(leaveFormDO.getUserId());
        //查询是否超过请假时间
        //leaveDataConfirmCycleCheck(nowDate, hrmsSalaryConfigDO, startDate);
        // 考勤周期
        HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(leaveFormDO.getUserId());
        confirmCycleCheck(nowDate, userAttendanceCycleConfig, startDate);


        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOS = new ArrayList<>();
        outOfOfficeRevokeDataAddBuild(param, formDetailBO, hrmsApplicationFormDO, hrmsApplicationFormRelationDOS, hrmsApplicationFormAttrDOArrayList);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        leaveAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmCreateApprovalService.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        //previewDTOBuild(recordApiDTOList, resultDTOList);
        previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, leaveFormDO.getUserCode());
        return resultDTOList;
    }

    @Override
    public List<ApprovalDetailStepRecordDTO> reissueCardRevokePreview(ReissueCardRevokeAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        HrmsApplicationFormDetailBO formDetailBO = hrmsApplicationFormManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        HrmsApplicationFormDO leaveFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOS();
        Map<String, HrmsApplicationFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        //防止被重复撤销
        repeatRevokeCheck(leaveFormDO.getId());
        HrmsApplicationFormAttrDO reissueCardDayId = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode());
        if (reissueCardDayId == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ABNORMAL_RECORD_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ABNORMAL_RECORD_DATE_NOT_EMPTY.getDesc()));
        }
        Date startDate = DateUtil.parse(reissueCardDayId.getAttrValue(), "yyyyMMdd");
        Date nowDate = new Date();
        //获取用户的薪资方案
        //HrmsSalaryConfigDO hrmsSalaryConfigDO = hrmsAttendanceBaseService.getUserSalaryConfig(leaveFormDO.getUserId());
        //查询是否超过请假时间
        //leaveDataConfirmCycleCheck(nowDate, hrmsSalaryConfigDO, startDate);
        // 考勤周期
        HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(leaveFormDO.getUserId());
        confirmCycleCheck(nowDate, userAttendanceCycleConfig, startDate);

        HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
        List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOS = new ArrayList<>();
        reissueCardRevokeDataAddBuild(param, formDetailBO, hrmsApplicationFormDO, hrmsApplicationFormRelationDOS, hrmsApplicationFormAttrDOArrayList);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        leaveAddApprovalDataBuild(initInfoApiDTO, hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmCreateApprovalService.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        //previewDTOBuild(recordApiDTOList, resultDTOList);
        previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, leaveFormDO.getUserCode());
        return resultDTOList;
    }


    @Override
    public List<UserLeaveImportParam> leaveImport(List<UserLeaveImportParam> importList) {
        List<UserLeaveImportParam> failImportList = new ArrayList<>();
        List<UserLeaveImportParam> successImportList = new ArrayList<>();
        leaveImportDataCheck(importList, failImportList, successImportList);
        List<UserLeaveImportParam> filterImportList = new ArrayList<>();
        //冲突校验，导入的和历史存在的都要校验
        leaveImportClashCheck(successImportList, failImportList, filterImportList);
        // 假期导入功能废弃-不需要修改假期详情数据列表，所以在循环外面设置空list
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        for (UserLeaveImportParam importParam : filterImportList) {
            HrmsApplicationFormDO hrmsApplicationFormDO = new HrmsApplicationFormDO();
            List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
            List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOArrayList = new ArrayList<>();
            HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
            HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
            LeaveAddParam param = BeanUtils.convert(importParam, LeaveAddParam.class);
            leaveDataAddBuild(param, hrmsApplicationFormDO, hrmsApplicationFormRelationDOArrayList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.PASS.getCode());
            hrmsApplicationFormDO.setDataSource(ApplicationDataSourceEnum.IMPORT.getCode());
            hrmsAttendanceApprovalManage.formAdd(hrmsApplicationFormDO, hrmsApplicationFormRelationDOArrayList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO, userLeaveStageDetailList, null);
            //调用每日考勤计算
            attendanceHandler(hrmsApplicationFormDO, hrmsApplicationFormAttrDOArrayList);
        }
        return failImportList;
    }

    @Override
    public PaginationResult<LeaveFormInfoExportVO> listExport(AttendanceApprovalInfoParam param) {
        if (StringUtils.isNotBlank(param.getDeptIdString())) {
            List<String> deptIdStringList = Arrays.asList(param.getDeptIdString().split(","));
            List<Long> deptIdList = new ArrayList<>();
            deptIdStringList.forEach(item -> {
                deptIdList.add(Long.valueOf(item));
            });
            param.setDeptIds(deptIdList);
        }
        AttendanceApprovalInfoQuery query = BeanUtils.convert(param, AttendanceApprovalInfoQuery.class);
        if (StringUtils.equalsIgnoreCase(param.getDataSource(), "HRMS")) {
            // 获取部门权限
            UserAuthParam userAuthParam = UserAuthParam.builder().userId(RequestInfoHolder.getUserId()).build();
            UserAuthDTO userAuthDTO = userDeptAuthList(userAuthParam);
            if (!checkDeptAuth(query, userAuthDTO)) {
                return getPageResult(BeanUtils.convert(new PageInfo<>(), PageInfo.class), param);
            }
        }
        if (StringUtils.equalsIgnoreCase(param.getDataSource(), "CLOVER")) {
            query.setUserIdList(Arrays.asList(RequestInfoHolder.getUserId()));
        }
        if (StringUtils.isNotBlank(param.getFormStatus())) {
            query.setFormStatusList(Arrays.asList(param.getFormStatus()));
        }
        query.setExcludeApplicationDataSource(ApplicationDataSourceEnum.IMPORT.getCode());
        Page<HrmsApplicationFormDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount(), param.getShowCount() > 0);
        PageInfo<HrmsApplicationFormDO> pageInfo = page.doSelectPageInfo(() -> hrmsApplicationFormManage.selectAttendanceApprovalInfo(query));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            PageInfo<LeaveFormInfoExportVO> pageInfoResult = BeanUtils.convert(new PageInfo<>(), PageInfo.class);
            return getPageResult(pageInfoResult, param);
        }

        List<Long> formIdList = pageInfo.getList().stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsApplicationFormAttrDO> attrDOList = hrmsApplicationFormAttrManage.selectFormAttrByFormIdLit(formIdList);
        List<Long> uesrIdList = pageInfo.getList().stream().map(item -> item.getUserId()).collect(Collectors.toList());
        List<Long> applyUesrIdList = pageInfo.getList().stream().map(item -> item.getApplyUserId()).collect(Collectors.toList());
        List<Long> allUesrIdList = new ArrayList<>();
        allUesrIdList.addAll(uesrIdList);
        allUesrIdList.addAll(applyUesrIdList);
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectUserInfoByIds(allUesrIdList);
        List<Long> deptIdList = userInfoDOList.stream().filter(item -> item.getDeptId() != null).map(item -> item.getDeptId()).collect(Collectors.toList());
        List<Long> postIdList = userInfoDOList.stream().filter(item -> item.getPostId() != null).map(item -> item.getPostId()).collect(Collectors.toList());
        List<HrmsEntDeptDO> deptDOList = hrmsDeptManage.selectDeptByIds(deptIdList);
        List<HrmsEntPostDO> postDOList = hrmsEntPostDao.listByPostList(postIdList);
        List<LeaveFormInfoExportVO> exportVOList = new ArrayList<>();
        for (HrmsApplicationFormDO formDO : pageInfo.getList()) {
            LeaveFormInfoExportVO exportVO = new LeaveFormInfoExportVO();
            exportVO.setApplicationCode(formDO.getApplicationCode());
            exportVO.setFormStatus(formDO.getFormStatus());
            exportVO.setCreateDate(formDO.getCreateDate());
            List<HrmsApplicationFormAttrDO> leaveNameDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(leaveNameDOList)) {
                exportVO.setLeaveName(leaveNameDOList.get(0).getAttrValue());
            }
            if (query.getFormTypeList().contains(HrAttendanceApplicationFormTypeEnum.LEAVE.getCode())) {
                List<HrmsApplicationFormAttrDO> leaveStartDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveStartDateDOList)) {
                    exportVO.setStartDate(leaveStartDateDOList.get(0).getAttrValue());
                }
                List<HrmsApplicationFormAttrDO> leaveEndDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveEndDateDOList)) {
                    exportVO.setEndDate(leaveEndDateDOList.get(0).getAttrValue());
                }
            }
            if (query.getFormTypeList().contains(HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode())) {
                List<HrmsApplicationFormAttrDO> outOfOfficeStartDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outOfOfficeStartDateDOList)) {
                    exportVO.setStartDate(outOfOfficeStartDateDOList.get(0).getAttrValue());
                }
                List<HrmsApplicationFormAttrDO> outOfOfficeEndDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outOfOfficeEndDateDOList)) {
                    exportVO.setEndDate(outOfOfficeEndDateDOList.get(0).getAttrValue());
                }
            }
            List<HrmsApplicationFormAttrDO> dayInfoList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dayInfoList)) {
                List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoList.get(0).getAttrValue(), DayDurationInfoDTO.class);

                BigDecimal days = BigDecimal.ZERO;
                BigDecimal hours = BigDecimal.ZERO;
                BigDecimal minutes = BigDecimal.ZERO;

                for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                    days = days.add(dayDurationInfoDTO.getDays());
                    hours = hours.add(dayDurationInfoDTO.getHours());
                    minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                }
                String descCN = days + "天" + hours + "小时" + minutes + "分钟";
                String descEN = days + "days" + hours + "hours" + minutes + "minutes";
                Map<String, String> expectedLeaveTimeMap = new HashMap<>();
                expectedLeaveTimeMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
                expectedLeaveTimeMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
                exportVO.setLeaveHours(descEN);
            }

            exportVOList.add(exportVO);
            List<HrmsUserInfoDO> existUserInfoDOList = userInfoDOList.stream().filter(item -> item.getId().equals(formDO.getUserId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existUserInfoDOList)) {
                continue;
            }
            exportVO.setUserCode(existUserInfoDOList.get(0).getUserCode());
            exportVO.setUserName(existUserInfoDOList.get(0).getUserName());
            exportVO.setWorkNo(existUserInfoDOList.get(0).getWorkNo());
            exportVO.setCountry(existUserInfoDOList.get(0).getLocationCountry());
            List<HrmsEntDeptDO> existDeptDOList = deptDOList.stream().filter(item -> item.getId().equals(existUserInfoDOList.get(0).getDeptId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existDeptDOList)) {
                exportVO.setDeptName(existDeptDOList.get(0).getDeptNameEn());
            }
            List<HrmsEntPostDO> existPostDOList = postDOList.stream().filter(item -> item.getId().equals(existUserInfoDOList.get(0).getPostId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existPostDOList)) {
                exportVO.setPostName(existPostDOList.get(0).getPostNameEn());
            }

            List<HrmsUserInfoDO> existApplyUserInfoDOList = userInfoDOList.stream().filter(item -> item.getId().equals(formDO.getApplyUserId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existApplyUserInfoDOList)) {
                continue;
            }
            exportVO.setApplyUserCode(existApplyUserInfoDOList.get(0).getUserCode());
            exportVO.setApplyUserName(existApplyUserInfoDOList.get(0).getUserName());
            exportVO.setApplyUserWorkNo(existApplyUserInfoDOList.get(0).getWorkNo());
        }
        return getPageResult(exportVOList, param, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 外勤/请假-每天时长计算明细
     * 需要获取请假开始时间前一天-请假结束时间后一天的所有排班
     *
     * @param userId                 用户id
     * @param startDate              请假开始时间
     * @param endDate                请假结束时间
     * @param companyLeaveConfigDO   公司假期配置
     * @param dayDurationInfoDTOList 每天时长明细
     * @param dayAttendanceHours     每天考勤时长
     */
    @Override
    public void dayDurationInfoHandler0(Long userId, Date startDate, Date endDate, HrmsCompanyLeaveConfigDO companyLeaveConfigDO, List<DayDurationInfoDTO> dayDurationInfoDTOList, BigDecimal dayAttendanceHours) {
        dayDurationInfoHandler(userId, startDate, endDate, companyLeaveConfigDO, dayDurationInfoDTOList, dayAttendanceHours);
    }

    /**
     * 获取请假总时长-单位分钟
     *
     * @param dayDurationInfoList 每天请假时间
     */
    @Override
    public BigDecimal handlerLeaveTotalTime0(List<DayDurationInfoDTO> dayDurationInfoList) {
        return handlerLeaveTotalTime(dayDurationInfoList);
    }

    /**
     * 查询国家的假期配置
     *
     * @param country   国家
     * @param leaveType 假期类型
     * @return HrmsCompanyLeaveConfigDO 假期配置
     */
    @Override
    public HrmsCompanyLeaveConfigDO getCountryLeaveTypeConfig(String country, String leaveType) {
        return getLeaveTypeDO(country, leaveType);
    }

    /**
     * 通过人员编码关联假期范围查询国家的假期配置
     *
     * @param userCodeList 用户编码
     * @param leaveName    假期名称
     * @return HrmsCompanyLeaveConfigDO 假期配置
     */
    @Override
    public HrmsCompanyLeaveConfigDO getUserLeaveTypeConfig(List<String> userCodeList, String leaveName) {
        return selectByNameAndUserCode(userCodeList, leaveName);
    }

    /**
     * @param detailBO                     假期申请单数据、假期申请单详情数据、考勤申请单关联表数据
     * @param userLeaveStageDetailInfoList 用来接收-需要修改的假期详情数据列表
     * @param remark                       备注
     * @return HrmsUserLeaveRecordDO
     */
    @Override
    public HrmsUserLeaveRecordDO handlerUserLeaveStageDetailList0(HrmsApplicationFormDetailBO detailBO, List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailInfoList, String remark) {
        return handlerUserLeaveStageDetailList(detailBO, userLeaveStageDetailInfoList, remark);
    }

    /**
     * 请假/外勤审批通过，计算考勤
     */
    private void attendanceHandler(HrmsApplicationFormDO formDO, List<HrmsApplicationFormAttrDO> attrDOS) {
        List<HrmsApplicationFormAttrDO> startDateDO = new ArrayList<>();
        List<HrmsApplicationFormAttrDO> endDateDO = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.LEAVE.getCode())) {
            startDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
            endDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
        }
        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode())) {
            startDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
            endDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(startDateDO) || CollectionUtils.isEmpty(endDateDO)) {
            return;
        }
        Date startDate = DateUtil.parse(startDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Date endDate = DateUtil.parse(endDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Long startDayId = Long.valueOf(DateUtil.format(startDate, "yyyyMMdd"));
        Long endDayId = Long.valueOf(DateUtil.format(endDate, "yyyyMMdd"));

        //只有前2天之前的数据才可以立刻计算
        Long effectDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(new Date(), -2), "yyyyMMdd"));
        Long tempDayId = startDayId;
        while (tempDayId <= endDayId) {
            Long finalTempDayId = tempDayId;
            //后移一天
            tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));
            //未来的不用计算
            if (finalTempDayId > effectDayId) {
                continue;
            }
            try {
                DayAttendanceHandlerDTO dayAttendanceHandlerDTO = new DayAttendanceHandlerDTO();
                dayAttendanceHandlerDTO.setUserCodes(formDO.getUserCode());
                dayAttendanceHandlerDTO.setAttendanceDayId(finalTempDayId);
                attendanceGenerateService.dayAttendanceHandler(dayAttendanceHandlerDTO);
            } catch (Exception e) {
                e.getStackTrace();
                log.info("请假申请/撤销通过，重新计算当天考勤失败，单据号:{},当天:{},异常信息为:{}", formDO.getApplicationCode(), finalTempDayId, e.getMessage());
            }
        }
    }

    private void leaveImportClashCheck(List<UserLeaveImportParam> successImportList, List<UserLeaveImportParam> failImportList, List<UserLeaveImportParam> filterImportList) {
        Map<Long, List<UserLeaveImportParam>> importMap = successImportList.stream().collect(Collectors.groupingBy(UserLeaveImportParam::getUserId));
        //先判断业务给的Excel的每个用户有没有给重复假期交集
        for (Map.Entry<Long, List<UserLeaveImportParam>> entry : importMap.entrySet()) {
            List<UserLeaveImportParam> importParamList = entry.getValue();
            boolean tag = false;
            for (int i = 0; i < importParamList.size(); i++) {
                for (int j = 0; j < importParamList.size(); j++) {
                    if (i == j) {
                        continue;
                    }
                    //无交集
                    if (importParamList.get(j).getLeaveEndDate().compareTo(importParamList.get(i).getLeaveStartDate()) < 1
                            || importParamList.get(j).getLeaveStartDate().compareTo(importParamList.get(i).getLeaveEndDate()) > -1) {
                        continue;
                    }
                    //有交集
                    tag = true;
                    break;
                }
                if (tag) {
                    break;
                }
            }
            if (tag) {
                for (UserLeaveImportParam importParam : importParamList) {
                    IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "用户导入假期时间存在交集" : "User import holiday time overlaps");
                    failImportList.add(importParam);
                }
                continue;
            }
            //导入的Excel没有问题，需要看历史的数据有没有交集
            for (UserLeaveImportParam importParam : importParamList) {
                List<ClashApplicationInfoDTO> clashApplicationInfoDTOList = new ArrayList<>();
                selectClashApplication(importParam.getUserId(), importParam.getLeaveStartDate(), importParam.getLeaveEndDate(), clashApplicationInfoDTOList);
                if (CollectionUtils.isNotEmpty(clashApplicationInfoDTOList)) {
                    IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "历史单据时间存在冲突" : "old form date has clash");
                    failImportList.add(importParam);
                    continue;
                }
                filterImportList.add(importParam);
            }
        }
    }

    private void leaveImportDataCheck(List<UserLeaveImportParam> importList, List<UserLeaveImportParam> failImportList, List<UserLeaveImportParam> successImportList) {
        List<String> userCodeList = importList.stream().filter(item -> StringUtils.isNotBlank(item.getUserCode())).map(item -> item.getUserCode()).collect(Collectors.toList());
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectUserInfoByCodes(userCodeList);
        List<Long> userIdList = userInfoDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        UserLeaveDetailQuery userLeaveDetailQuery = new UserLeaveDetailQuery();
        userLeaveDetailQuery.setUserIds(userIdList);
        List<HrmsUserLeaveDetailDO> userLeaveDetailDOList = hrmsUserLeaveDetailManage.selectUserLeaveDetail(userLeaveDetailQuery);
        List<Long> leaveIdList = userLeaveDetailDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsUserLeaveStageDetailDO> hrmsUserLeaveStageDetailDOList = hrmsUserLeaveStageDetailManage.selectByLeaveId(leaveIdList);

        //直接查询所有国家的假期
        List<HrmsCompanyLeaveConfigDO> companyLeaveConfigDOList = hrmsCompanyLeaveConfigManage.selectCompanyLeave(new CompanyLeaveQuery());
        //查询所有子公司
        List<HrmsEntCompanyDO> entCompanyDOList = hrmsEntCompanyDao.selectList();

        for (UserLeaveImportParam importParam : importList) {
            if (StringUtils.isBlank(importParam.getUserCode())) {
                IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "员工账号不能为空" : "employee code cannot be empty");
                failImportList.add(importParam);
                continue;
            }
            if (StringUtils.isBlank(importParam.getLeaveType())) {
                IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "假期类型不能为空" : "leave type cannot be empty");
                failImportList.add(importParam);
                continue;
            }
            List<HrmsUserInfoDO> existUserInfoDOList = userInfoDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getUserCode(), importParam.getUserCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existUserInfoDOList)) {
                IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "员工不存在" : "employee not exist");
                failImportList.add(importParam);
                continue;
            }
            HrmsUserInfoDO userInfoDO = existUserInfoDOList.get(0);
            if (StringUtils.isBlank(userInfoDO.getLocationCountry())) {
                IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "员工所在国家不存在" : "employee country not exist");
                failImportList.add(importParam);
                continue;
            }

            if (StringUtils.isBlank(importParam.getLeaveStartTimeString())) {
                IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "请假开始时间不能为空" : "leave start date cannot be empty");
                failImportList.add(importParam);
                continue;
            }
            if (StringUtils.isBlank(importParam.getLeaveEndTimeString())) {
                IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "请假结束时间不能为空" : "leave end date cannot be empty");
                failImportList.add(importParam);
                continue;
            }
            Date leaveStartDate = DateUtil.date();
            try {
                leaveStartDate = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US).parse(importParam.getLeaveStartTimeString());
            } catch (Exception e) {
                IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "请假开始时间格式有问题" : "There is a problem with the leave start date format");
                failImportList.add(importParam);
                continue;
            }
            importParam.setLeaveStartDate(DateUtil.beginOfDay(leaveStartDate));

            Date leaveEndDate = DateUtil.date();
            try {
                leaveEndDate = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US).parse(importParam.getLeaveEndTimeString());
            } catch (Exception e) {
                IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "请假结束时间格式有问题" : "There is a problem with the leave start end format");
                failImportList.add(importParam);
                continue;
            }
            //TODO 这里和业务明确下
            importParam.setLeaveEndDate(DateUtil.beginOfDay(DateUtil.offsetDay(leaveEndDate, 1)));

            List<HrmsCompanyLeaveConfigDO> existCompanyLeaveConfigDOList = companyLeaveConfigDOList.stream().filter(item -> item.getCountry().equals(userInfoDO.getLocationCountry()) && StringUtils.equalsIgnoreCase(item.getLeaveType(), importParam.getLeaveType())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existCompanyLeaveConfigDOList)) {
                IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "用户所在国家不存在这种假期类型" : "user country not have this leave type");
                failImportList.add(importParam);
                continue;
            }
            HrmsCompanyLeaveConfigDO companyLeaveConfigDO = existCompanyLeaveConfigDOList.get(0);

            List<HrmsUserLeaveDetailDO> existUserLeaveDetailDOList = userLeaveDetailDOList.stream().filter(item -> item.getUserId().equals(userInfoDO.getId()) && StringUtils.equalsIgnoreCase(item.getLeaveType(), importParam.getLeaveType().trim())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existUserLeaveDetailDOList)) {
                IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "用户不存在这种假期类型" : "user not have this leave type");
                failImportList.add(importParam);
                continue;
            }
            HrmsUserLeaveDetailDO userLeaveDetailDO = existUserLeaveDetailDOList.get(0);

            List<HrmsUserLeaveStageDetailDO> existHrmsUserLeaveStageDetailDOList = hrmsUserLeaveStageDetailDOList.stream().filter(item -> item.getLeaveId().equals(userLeaveDetailDO.getId())).sorted(Comparator.comparing(HrmsUserLeaveStageDetailDO::getPercentSalary).reversed()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existHrmsUserLeaveStageDetailDOList)) {
                IpepUtils.putFail(importParam, RequestInfoHolder.isChinese() ? "用户不存在这种假期类型明细" : "user not have this leave type detail");
                failImportList.add(importParam);
                continue;
            }
            importParam.setUserId(userInfoDO.getId());
            importParam.setApplyUserId(userInfoDO.getId());
            importParam.setRemark("特殊入口:用户假期导入");
            importParam.setOperationType(1);
            importParam.setUserName(userInfoDO.getUserName());
            importParam.setDeptId(userInfoDO.getDeptId());
            importParam.setPostId(userInfoDO.getPostId());
            importParam.setCountry(userInfoDO.getLocationCountry());
            if (importParam.getCompanyId().equals(100L)) {
                importParam.setCountry("HQ");
            }
            importParam.setLeaveUnit(companyLeaveConfigDO.getLeaveUnit());
            importParam.setLeaveShortName(companyLeaveConfigDO.getLeaveShortName());
            successImportList.add(importParam);
        }
    }

    private void outOfOfficeAddDataCheck(OutOfOfficeAddParam param) {
        if (param.getOutOfOfficeStartDate() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getDesc()));
        }
        if (param.getOutOfOfficeEndDate() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.OUT_OF_OFFICE_END_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.OUT_OF_OFFICE_END_DATE_NOT_EMPTY.getDesc()));
        }
        if (StringUtils.isEmpty(param.getRemark())) {
            throw BusinessException.get(HrmsErrorCodeEnums.REMARK_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.REMARK_NOT_EMPTY.getDesc()));
        }
        //获取用户的薪资方案
        //HrmsSalaryConfigDO hrmsSalaryConfigDO = hrmsAttendanceBaseService.getUserSalaryConfig(param.getUserId());
        //查询是否超过请假时间
        Date nowDate = new Date();
        //leaveDataConfirmCycleCheck(nowDate, hrmsSalaryConfigDO, param.getOutOfOfficeStartDate());
        // 考勤周期
        HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(param.getUserId());
        confirmCycleCheck(nowDate, userAttendanceCycleConfig, param.getOutOfOfficeStartDate());

        //判断是否还有冲突
        List<ClashApplicationInfoDTO> clashApplicationInfoDTOList = new ArrayList<>();
        selectClashApplication(param.getUserId(), param.getOutOfOfficeStartDate(), param.getOutOfOfficeEndDate(), clashApplicationInfoDTOList);
        if (CollectionUtils.isNotEmpty(clashApplicationInfoDTOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.EXIST_CLASH_TIME_PERIOD.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.EXIST_CLASH_TIME_PERIOD.getDesc()));
        }
        //重新计算每天请假时间
        List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
        dayDurationInfoHandler(param.getUserId(), param.getOutOfOfficeStartDate(), param.getOutOfOfficeEndDate(), null, dayDurationInfoDTOList, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
        //过滤不占用请假时间的日期
        dayDurationInfoDTOList = dayDurationInfoDTOList.stream()
                .filter(item -> item.getDays().compareTo(BigDecimal.ZERO) > 0
                        || item.getHours().compareTo(BigDecimal.ZERO) > 0
                        || item.getMinutes().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        //校验外勤时长不超过7天
        checkOutOfOfficeTotalTime(dayDurationInfoDTOList);
        param.setDayDurationInfoDTOList(dayDurationInfoDTOList);
    }

    private BigDecimal leaveAddDataCheck(LeaveAddParam param) {
        if (StringUtils.isBlank(param.getLeaveName())) {
            throw BusinessException.get(HrmsErrorCodeEnums.LEAVE_NAME_IS_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.LEAVE_NAME_IS_NOT_EMPTY.getDesc()));
        }
        if (param.getLeaveStartDate() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.LEAVE_START_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.LEAVE_START_DATE_NOT_EMPTY.getDesc()));
        }
        if (param.getLeaveEndDate() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.LEAVE_END_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.LEAVE_END_DATE_NOT_EMPTY.getDesc()));
        }
        if (StringUtils.isEmpty(param.getRemark())) {
            throw BusinessException.get(HrmsErrorCodeEnums.REMARK_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.REMARK_NOT_EMPTY.getDesc()));
        }
        param.setDayAttendanceHours(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
        //查询是否超过请假时间
        Date nowDate = new Date();
        // 考勤周期
        HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(param.getUserId());
        confirmCycleCheck(nowDate, userAttendanceCycleConfig, param.getLeaveStartDate());

        // 查询该假期配置
        // 修改为通过人员范围查询假期配置
        HrmsCompanyLeaveConfigDO companyLeaveConfigDO = hrmsCompanyLeaveConfigManage.getById(param.getConfigId());
        param.setLeaveUnit(companyLeaveConfigDO.getLeaveUnit());
        param.setMiniLeaveDuration(companyLeaveConfigDO.getMiniLeaveDuration());
        param.setLeaveShortName(companyLeaveConfigDO.getLeaveShortName());
        param.setIsUploadAttachment(companyLeaveConfigDO.getIsUploadAttachment());
        param.setUploadAttachmentCondition(companyLeaveConfigDO.getUploadAttachmentCondition());
        param.setAttachmentUnit(companyLeaveConfigDO.getAttachmentUnit());

        // 查询该用户假期，获取用户该假期剩余可用余额
        List<UserLeaveResidualVO> userLeaveResidualVOS = selectUserLeaveResidual(param.getUserId());
        List<UserLeaveResidualVO> userLeaveList = userLeaveResidualVOS.stream()
                .filter(item -> Objects.equals(item.getConfigId(), param.getConfigId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userLeaveList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_THIS_LEAVE_TYPE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_THIS_LEAVE_TYPE.getDesc()));
        }
        param.setLeaveResidueMinutes(userLeaveList.get(0).getLeaveResidueMinutes());

        //判断是否还有冲突
        List<ClashApplicationInfoDTO> clashApplicationInfoDTOList = new ArrayList<>();
        selectClashApplication(param.getUserId(), param.getLeaveStartDate(), param.getLeaveEndDate(), clashApplicationInfoDTOList);
        if (CollectionUtils.isNotEmpty(clashApplicationInfoDTOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.EXIST_CLASH_TIME_PERIOD.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.EXIST_CLASH_TIME_PERIOD.getDesc()));
        }
        //重新计算每天请假时间
        List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
        dayDurationInfoHandler(param.getUserId(), param.getLeaveStartDate(), param.getLeaveEndDate(), companyLeaveConfigDO, dayDurationInfoDTOList, param.getDayAttendanceHours());
        //过滤不占用请假时间的日期
        dayDurationInfoDTOList = dayDurationInfoDTOList.stream()
                .filter(item -> item.getDays().compareTo(BigDecimal.ZERO) > 0
                        || item.getHours().compareTo(BigDecimal.ZERO) > 0
                        || item.getMinutes().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        param.setDayDurationInfoDTOList(dayDurationInfoDTOList);
        // 计算请假总时长-单位分钟
        BigDecimal totalLeaveTime = handlerLeaveTotalTime(dayDurationInfoDTOList);
        // 校验最小请假时长及附件条件
        checkLeaveCondition(param, dayDurationInfoDTOList);
        // 校验请假时长与请假余额
        checkLeaveTime(param, userLeaveList, totalLeaveTime);

        return totalLeaveTime;
    }

    /**
     * 获取请假总时长-单位分钟
     *
     * @param dayDurationInfoList 每天请假时间
     */
    private BigDecimal handlerLeaveTotalTime(List<DayDurationInfoDTO> dayDurationInfoList) {
        BigDecimal totalMinutes = BigDecimal.ZERO;
        // 遍历每一天的请假时长
        for (DayDurationInfoDTO dayDurationInfo : dayDurationInfoList) {

            // 计算一天的请假时长-分钟（天 * 法定工作时长 8h * 一小时的分钟数）【这里面的法定时长为什么给8h：因为，假期给的时候一天是按照8h给，所以请一天假，需要扣假期8h】
            totalMinutes = totalMinutes.add(dayDurationInfo.getDays().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES));

            // 换算之后的小时数【这边要根据实际工作时长来换算成8h的时间】【保留两位小数，四舍五入】
            BigDecimal realHours = dayDurationInfo.getHours().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).divide(dayDurationInfo.getLegalWorkingHours(), 2, RoundingMode.HALF_UP);
            // 计算小时的请假时长-分钟（小时 * 一小时的分钟数）
            totalMinutes = totalMinutes.add(realHours.multiply(BusinessConstant.MINUTES));

            /*
                 计算分钟的请假时长-分钟【这边要根据实际工作时长来换算成8h的时间】【保留两位小数，四舍五入】
                    算法1：分钟/60min/工作时长h * 8h = 分钟转换成8h的分钟数
                    //BigDecimal realMinutesHours = dayDurationInfo.getMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP).divide(dayDurationInfo.getLegalWorkingHours(), 2, RoundingMode.HALF_UP).multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
                    算法2：分钟 * 8h * 60min / 工作时长h * 60min
                    算法1 存在很大精度问题，因为有多次除法，每一次除法都会保留两位小数，然后四舍五入，所以会存在精度问题
                    算法2 只有一次除法，所以精度问题 小很多
            */

            BigDecimal realMinutes = dayDurationInfo.getMinutes().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES).divide(dayDurationInfo.getLegalWorkingHours().multiply(BusinessConstant.MINUTES), 2, RoundingMode.HALF_UP);

            totalMinutes = totalMinutes.add(realMinutes);
        }
        return totalMinutes;
    }

    /**
     * 校验外勤时长不超过7天
     *
     * @param dayDurationInfoList
     */
    private void checkOutOfOfficeTotalTime(List<DayDurationInfoDTO> dayDurationInfoList) {
        if (CollectionUtils.isEmpty(dayDurationInfoList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.OUT_OF_OFFICE_TIME_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.OUT_OF_OFFICE_TIME_NOT_EMPTY.getDesc()));
        }
        BigDecimal totalDays = dayDurationInfoList.stream().map(item -> item.getDays()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //判断时长超过7天则报错
        if (totalDays.compareTo(BigDecimal.valueOf(7)) > 0) {
            throw BusinessException.get(HrmsErrorCodeEnums.OUT_OF_OFFICE_DAYS_EXCEED_7.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.OUT_OF_OFFICE_DAYS_EXCEED_7.getDesc()));
        }
        if (totalDays.compareTo(BigDecimal.valueOf(7)) == 0) {
            BigDecimal totalHours = dayDurationInfoList.stream().map(item -> item.getHours()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalMinutes = dayDurationInfoList.stream().map(item -> item.getMinutes()).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (totalHours.compareTo(BigDecimal.ZERO) > 0 || totalMinutes.compareTo(BigDecimal.ZERO) > 0) {
                throw BusinessException.get(HrmsErrorCodeEnums.OUT_OF_OFFICE_DAYS_EXCEED_7.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.OUT_OF_OFFICE_DAYS_EXCEED_7.getDesc()));
            }
        }
    }

    /**
     * 校验请假时长与余额
     *
     * @param param          入参
     * @param userLeaveList  用户假期列表
     * @param totalLeaveTime 请假总时长
     */
    private static void checkLeaveTime(LeaveAddParam param, List<UserLeaveResidualVO> userLeaveList, BigDecimal totalLeaveTime) {
        // 请假时长不能为0
        if (totalLeaveTime.compareTo(BigDecimal.ZERO) == 0) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.LEAVE_TIME_CANNOT_BE_ZERO);
        }
        // 获取请假时长
        long leaveTime = DateUtil.between(param.getLeaveStartDate(), param.getLeaveEndDate(), DateUnit.MINUTE, false);
        // 请假结束时间必须在开始时间之后
        if (leaveTime < 0) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.THE_LEAVE_END_TIME_MUST_BE_AFTER_THE_START_TIME);
        }

        // 1. 过滤假期余额小于等于0的历史数据： 不能请假
        if (userLeaveList.get(0).getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) <= 0) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.THE_LEAVE_BALANCE_IS_INSUFFICIENT);
        }
        // 过滤完1. 在过滤请假余额不能小于请假时长，才会准确
        if (userLeaveList.get(0).getLeaveResidueMinutes().compareTo(totalLeaveTime) < 0) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.THE_BALANCE_OF_LEAVE_CANNOT_BE_LESS_THAN_THE_LENGTH_OF_LEAVE);
        }
    }

    private static void checkLeaveCondition(LeaveAddParam param, List<DayDurationInfoDTO> dayDurationInfoDTOList) {
        //校验天假不能请假小于1天
        if (CollectionUtils.isEmpty(dayDurationInfoDTOList)) {
            return;
        }
        // 获取请假单位及最小请假时长
        String leaveUnit = param.getLeaveUnit();
        Integer miniLeaveDuration = Objects.isNull(param.getMiniLeaveDuration())
                ? BusinessConstant.ZERO : param.getMiniLeaveDuration();
        BigDecimal totalDays = BigDecimal.ZERO;
        BigDecimal totalHours = BigDecimal.ZERO;
        BigDecimal totalMinutes = BigDecimal.ZERO;
        for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
            // 总天数
            totalDays = totalDays.add(dayDurationInfoDTO.getDays());
            BigDecimal legalWorkingHours = dayDurationInfoDTO.getLegalWorkingHours();
            // 总小时数
            BigDecimal dayHours = dayDurationInfoDTO.getDays().multiply(legalWorkingHours)
                    .add(dayDurationInfoDTO.getHours());
            totalHours = totalHours.add(dayHours);
            // 总分钟数
            BigDecimal dayMinutes = dayHours.multiply(BusinessConstant.MINUTES).add(dayDurationInfoDTO.getMinutes());
            totalMinutes = totalMinutes.add(dayMinutes);
            // 天假特殊校验 不能小于一天
            if (LeaveUnitEnum.DAYS.getCode().equals(leaveUnit) && dayDurationInfoDTO.getDays().compareTo(BigDecimal.ZERO) == 0) {
                throw BusinessLogicException.getException(HrmsErrorCodeEnums.LEAVE_DAY_CANNOT_BE_ZERO);
            }
        }
        // 校验请假时长必须大于最小请假时长
        // 最小请假单位是天
        if (LeaveUnitEnum.DAYS.getCode().equals(leaveUnit)
                && totalDays.intValue() < miniLeaveDuration) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.LEAVE_DURATION_CANNOT_BE_LESS_THAN_MINI);
        }
        // 最小请假单位为小时
        if (LeaveUnitEnum.HOURS.getCode().equals(leaveUnit)
                && totalHours.intValue() < miniLeaveDuration) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.LEAVE_DURATION_CANNOT_BE_LESS_THAN_MINI);
        }
        // 最小请假单位为分钟
        if (LeaveUnitEnum.MINUTES.getCode().equals(leaveUnit)
                && totalMinutes.intValue() < miniLeaveDuration) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.LEAVE_DURATION_CANNOT_BE_LESS_THAN_MINI);
        }
        // 校验附件是否必填
        if (!BusinessConstant.Y.equals(param.getIsUploadAttachment())) {
            return;
        }
        // 获取附件单位
        String attachmentUnit = param.getAttachmentUnit();
        // 上传附件单位是天
        if (LeaveUnitEnum.DAYS.getCode().equals(attachmentUnit)
                && totalDays.longValue() >= param.getUploadAttachmentCondition()
                && CollectionUtils.isEmpty(param.getAttachmentList())) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ATTACHMENT_NOT_EMPTY);
        }
        // 上传附件单位为小时
        if (LeaveUnitEnum.HOURS.getCode().equals(attachmentUnit)
                && totalHours.longValue() >= param.getUploadAttachmentCondition()
                && CollectionUtils.isEmpty(param.getAttachmentList())) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ATTACHMENT_NOT_EMPTY);
        }
        // 上传附件单位为分钟
        if (LeaveUnitEnum.MINUTES.getCode().equals(attachmentUnit)
                && totalMinutes.longValue() >= param.getUploadAttachmentCondition()
                && CollectionUtils.isEmpty(param.getAttachmentList())) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ATTACHMENT_NOT_EMPTY);
        }

    }

    private HrmsEmployeeAbnormalAttendanceDO reissueAddDataCheck(ReissueCardAddParam param) {
        if (Objects.isNull(param.getReissueCardDayId())) {
            throw BusinessException.get(HrmsErrorCodeEnums.PARAM_NOT_NULL.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.PARAM_NOT_NULL.getDesc()));
        }
        List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOList;
        List<HrmsAttendancePunchConfigDO> punchConfigDOList;
        Date startDate;
        String abnormalType = null;
        String abnormalExtend = null;
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        if (param.getAbnormalId() != null) {
            abnormalAttendanceDO = userAbnormalRecordCheck(param.getAbnormalId());
            if (!AttendanceAbnormalTypeEnum.getRessiueCodeList().contains(abnormalAttendanceDO.getAbnormalType())) {
                throw BusinessException.get(HrmsErrorCodeEnums.REISSUE_CARD_NOT_HANDLER_THIS_ABNORMAL_TYPE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.REISSUE_CARD_NOT_HANDLER_THIS_ABNORMAL_TYPE.getDesc()));
            }
//            itemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(Arrays.asList(abnormalAttendanceDO.getPunchClassConfigId()));
            itemConfigDOList = punchConfigManageAdapter.selectItemConfigByClassId(Arrays.asList(abnormalAttendanceDO.getPunchClassConfigId()));
//            punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByIdList(Arrays.asList(abnormalAttendanceDO.getPunchConfigId()));
            punchConfigDOList = punchConfigManageAdapter.selectAttendancePunchByIdList(
                    Collections.singletonList(abnormalAttendanceDO.getPunchConfigId()));
            startDate = abnormalAttendanceDO.getDate();
            abnormalType = abnormalAttendanceDO.getAbnormalType();
            abnormalExtend = abnormalAttendanceDO.getExtend();
        } else {
            startDate = DateUtils.str2Date(param.getReissueCardDayId() + " 00:00:00", DateFormatterUtil.FORMAT_YYYYMMDD_HH_MM_SS);
//            itemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(Arrays.asList(param.getReissueCardClassId()));
            itemConfigDOList = punchConfigManageAdapter.selectItemConfigByClassId(Arrays.asList(param.getReissueCardClassId()));
//            punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByIdList(Arrays.asList(param.getReissueCardConfigId()));
            punchConfigDOList = punchConfigManageAdapter.selectAttendancePunchByIdList(
                    Collections.singletonList(param.getReissueCardConfigId()));
        }

        //获取用户的薪资方案
        //HrmsSalaryConfigDO hrmsSalaryConfigDO = hrmsAttendanceBaseService.getUserSalaryConfig(param.getUserId());
        //查询是否超过补卡时间
        Date nowDate = new Date();
        //leaveDataConfirmCycleCheck(nowDate, hrmsSalaryConfigDO, startDate);
        // 考勤周期
        HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(param.getUserId());
        confirmCycleCheck(nowDate, userAttendanceCycleConfig, startDate);

        //查询用户补卡次数,因为有7天的可补卡周期，所已补卡的日期，可能存在上个周期和本次周期
        int residueReissueCardCount = getUserResidueReissueCardCount(param, startDate);
        if (residueReissueCardCount < 1) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_ATTENDANCE_REISSUE_CARD_COUNT_IS_OVER.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_ATTENDANCE_REISSUE_CARD_COUNT_IS_OVER.getDesc()));
        }
        //后面再校验小程序版本
        if (Objects.isNull(param.getCorrectPunchTime())) {
            throw BusinessException.get(HrmsErrorCodeEnums.REQUIRED_FIELDS_CANNOT_BE_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.REQUIRED_FIELDS_CANNOT_BE_EMPTY.getDesc()));
        }
        //构建参数
        if (StringUtils.isNotBlank(abnormalType)) {
            param.setReissueCardType(abnormalType);
        }
//        param.setReissueCardDayId(abnormalAttendanceDO.getDayId());
        param.setResidueReissueCardCount(residueReissueCardCount);
        //AttendanceDayCycleDTO attendanceDayCycleDTO = hrmsAttendanceBaseService.getUserAttendanceDayCycle(param.getReissueCardDayId(), hrmsSalaryConfigDO);
        AttendanceDayCycleDTO attendanceDayCycleDTO = hrmsAttendanceBaseService.getUserAttendanceCycleConfigDay(param.getReissueCardDayId(), userAttendanceCycleConfig);
        if (attendanceDayCycleDTO != null) {
            param.setAttendanceStartDate(attendanceDayCycleDTO.getAttendanceStartDate());
            param.setAttendanceEndDate(attendanceDayCycleDTO.getAttendanceEndDate());
        }
        StringBuilder stringBuilder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(itemConfigDOList)) {
            for (HrmsAttendancePunchClassItemConfigDO itemConfigDO : itemConfigDOList) {
                //自由打卡规则
                if (CollectionUtils.isNotEmpty(punchConfigDOList) && StringUtils.equalsIgnoreCase(punchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())) {
                    stringBuilder.append(DateUtil.format(itemConfigDO.getEarliestPunchInTime(), "HH:mm")).append("-")
                            .append(DateUtil.format(itemConfigDO.getLatestPunchOutTime(), "HH:mm")).append("&");
                    continue;
                }
                stringBuilder.append(DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm")).append("-")
                        .append(DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm")).append("&");
            }
        }
        if (stringBuilder.toString().length() > 0) {
            param.setPunchConfigClassItemInfo(stringBuilder.toString().substring(0, stringBuilder.toString().length() - 1));
        }
        //需要根据不同的异常类型在根据打卡时刻来自动填充
        if (StringUtils.isNotBlank(abnormalExtend)) {
            AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalExtend, AbnormalExtendDTO.class);
            param.setActualPunchTime(abnormalExtendDTO.getActualPunchTime());
//            param.setCorrectPunchTime(abnormalExtendDTO.getCorrectPunchTime());
        }
        //查询用户当日最早最晚打卡记录(过滤补卡得)
        List<EmployeePunchRecordDO> userPunchRecordList = punchCardService.getUserPunchRecords(param.getUserCode(), Arrays.asList(param.getReissueCardDayId()))
                .stream()
                .filter(item -> !SourceTypeEnum.REISSUE_CARD.name().equals(item.getSourceType()))
                .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userPunchRecordList)) {
            if (Objects.isNull(abnormalAttendanceDO) || userPunchRecordList.size() > 1) {
                param.setEarlyPunchTime(userPunchRecordList.get(0).getPunchTime());
                param.setLatePunchTime(userPunchRecordList.get(userPunchRecordList.size() - 1).getPunchTime());
            } else {
                if (!AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode().equals(abnormalAttendanceDO.getAbnormalType())) {
                    param.setEarlyPunchTime(userPunchRecordList.get(0).getPunchTime());
                }
                if (!AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode().equals(abnormalAttendanceDO.getAbnormalType())) {
                    param.setLatePunchTime(userPunchRecordList.get(userPunchRecordList.size() - 1).getPunchTime());
                }
            }
        }
        return abnormalAttendanceDO;
    }

    /**
     * 获取用户补卡数据(实际打卡时间,补卡后的时间)  目前就获取打卡规则的时间，不用实时获取当天
     */
    private void getUserReissueCardTime(ReissueCardAddParam param, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO, List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOList) {
        //当天排班如果是OFF/PH/AL/ML等，也直接返回
        if (StringUtils.isBlank(param.getUserCode()) || abnormalAttendanceDO == null || CollectionUtils.isEmpty(itemConfigDOList) || itemConfigDOList.get(0).getPunchClassId() == null) {
            return;
        }
        itemConfigDOList = itemConfigDOList.stream().sorted(Comparator.comparing(HrmsAttendancePunchClassItemConfigDO::getSortNo)).collect(Collectors.toList());
        List<HrmsAttendancePunchClassItemConfigDO> abnormalItemConfigDOS = itemConfigDOList.stream().filter(item -> item.getId().equals(abnormalAttendanceDO.getPunchClassItemConfigId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(abnormalItemConfigDOS)) {
            return;
        }
        //获取当前班次的考勤时间
        DayPunchTimeDTO dayPunchClassItemTimeDTO = hrmsAttendanceBaseService.getUserPunchClassItemDayTime(abnormalAttendanceDO.getDayId(), abnormalItemConfigDOS.get(0).getPunchClassId(), itemConfigDOList);
        if (dayPunchClassItemTimeDTO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_DAY_PUNCH_TIME_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_DAY_PUNCH_TIME_NOT_EXIST.getDesc()));
        }
        if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode())) {
            param.setCorrectPunchTime(dayPunchClassItemTimeDTO.getDayPunchStartTime());
            return;
        }
        if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode())) {
            param.setCorrectPunchTime(dayPunchClassItemTimeDTO.getDayPunchEndTime());
            return;
        }
        //获取打卡规则的起始结束时间的打卡数据
        EmployeePunchCardRecordQuery punchCardRecordQuery = new EmployeePunchCardRecordQuery();
        punchCardRecordQuery.setStartTime(dayPunchClassItemTimeDTO.getDayPunchStartTime());
        punchCardRecordQuery.setEndTime(dayPunchClassItemTimeDTO.getDayPunchEndTime());
        punchCardRecordQuery.setUserCode(param.getUserCode());
        List<EmployeePunchRecordDO> allPunchRecordList = punchRecordDao.listRecords(punchCardRecordQuery).stream()
                .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime)).collect(Collectors.toList());
        //TODO 迟到  这里需要注重测试下，取的值可能有问题
        if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.LATE.getCode())) {
            if (CollectionUtils.isNotEmpty(allPunchRecordList)) {
                param.setActualPunchTime(allPunchRecordList.get(0).getPunchTime());
            }
        }
        //TODO 早退
        if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode())) {
            if (CollectionUtils.isNotEmpty(allPunchRecordList)) {
                param.setActualPunchTime(allPunchRecordList.get(allPunchRecordList.size() - 1).getPunchTime());
            }
        }
    }

    /**
     * 判断用户处理的异常考勤是否正确(必须是审批中的异常)
     */
    private HrmsEmployeeAbnormalAttendanceDO userAbnormalRecordCheck(Long abnormal) {
        if (abnormal == null) {
            return null;
        }
        List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectByIdList(Arrays.asList(abnormal));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        //如果异常已经被别的单据使用了，审批中，报错提示
        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
        // 如果异常已经处理或过期则提示
        if (AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(abnormalAttendanceDO.getStatus())) {
            throw BusinessException.get(HrmsErrorCodeEnums.ABNORMAL_HAS_BEEN_HANDLER.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ABNORMAL_HAS_BEEN_HANDLER.getDesc()));
        }
        if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
            throw BusinessException.get(HrmsErrorCodeEnums.ABNORMAL_IN_REVIEW.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ABNORMAL_IN_REVIEW.getDesc()));
        }
        return abnormalAttendanceDO;
    }

    /**
     * 获取用户的剩余补卡次数
     */
    private int getUserResidueReissueCardCount(ReissueCardAddParam param, Date reissueCardDate) {
        AttendancePunchConfigRangeByDateQuery configRangeByDateQuery = new AttendancePunchConfigRangeByDateQuery();
        configRangeByDateQuery.setUserIds(Arrays.asList(param.getUserId()));
        configRangeByDateQuery.setStartDate(DateUtil.endOfDay(reissueCardDate));
        configRangeByDateQuery.setEndDate(DateUtil.endOfDay(reissueCardDate));
//        List<HrmsAttendancePunchConfigRangeDO> punchConfigRangeDOS = hrmsAttendancePunchConfigRangeManage.selectPunchConfigRangeByDate(configRangeByDateQuery)
//                .stream().sorted(Comparator.comparing(HrmsAttendancePunchConfigRangeDO::getExpireTime).reversed()).collect(Collectors.toList());
        List<HrmsAttendancePunchConfigRangeDO> punchConfigRangeDOS = punchConfigManageAdapter.selectPunchConfigRangeByDate(configRangeByDateQuery)
                .stream().sorted(Comparator.comparing(HrmsAttendancePunchConfigRangeDO::getExpireTime).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(punchConfigRangeDOS)) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_ATTENDANCE_PUNCH__CONFIG.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_ATTENDANCE_PUNCH__CONFIG.getDesc()));
        }
//        List<HrmsAttendancePunchConfigDO> punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByIdList(Arrays.asList(punchConfigRangeDOS.get(0).getPunchConfigId()));
        List<HrmsAttendancePunchConfigDO> punchConfigDOList = punchConfigManageAdapter.selectAttendancePunchByIdList(
                Collections.singletonList(punchConfigRangeDOS.get(0).getPunchConfigId()));
        if (CollectionUtils.isEmpty(punchConfigDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.ATTENDANCE_PUNCH_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ATTENDANCE_PUNCH_IS_EMPTY.getDesc()));
        }
        Long maxRepunchNumber = punchConfigDOList.get(0).getMaxRepunchNumber();
        if (maxRepunchNumber == null) {
            maxRepunchNumber = 0L;
        }
        List<HrmsAttendanceUserCardConfigDO> userCardConfigDOS = hrmsAttendanceUserCardConfigManage.selectByUserIdList(Arrays.asList(param.getUserId()));
        if (CollectionUtils.isEmpty(userCardConfigDOS)) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_REISSUE_CARD_CONFIG.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_REISSUE_CARD_CONFIG.getDesc()));
        }
        //查询用户本次异常考勤处于的打卡规则配置中的已补卡次数
        List<HrmsAttendanceUserCardConfigDO> attendanceUserCardConfigDOS = userCardConfigDOS.stream()
                .filter(item -> item.getCycleStartDate().compareTo(reissueCardDate) < 1 && item.getCycleEndDate().compareTo(reissueCardDate) > -1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(attendanceUserCardConfigDOS)) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_ATTENDANCE_REISSUE_CARD_CONFIG.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_ATTENDANCE_REISSUE_CARD_CONFIG.getDesc()));
        }
        param.setUserCardConfigDO(attendanceUserCardConfigDOS.get(0));
        Integer usedCardCount = attendanceUserCardConfigDOS.get(0).getUsedCardCount();
        if (usedCardCount == null) {
            usedCardCount = 0;
        }
        return (int) (maxRepunchNumber - usedCardCount);
    }

    /**
     * 查询假期
     */
    private HrmsCompanyLeaveConfigDO getLeaveTypeDO(String country, String leaveType) {
        CompanyLeaveQuery companyLeaveQuery = new CompanyLeaveQuery();
        companyLeaveQuery.setCountry(country);
        companyLeaveQuery.setLeaveType(leaveType);
        companyLeaveQuery.setStatus(StatusEnum.ACTIVE.getCode());
        List<HrmsCompanyLeaveConfigDO> companyLeaveConfigDOList = hrmsCompanyLeaveConfigManage.selectCompanyLeave(companyLeaveQuery);
        if (CollectionUtils.isEmpty(companyLeaveConfigDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR.getDesc()));
        }
        return companyLeaveConfigDOList.get(0);
    }

    /**
     * 通过人员查询假期
     */
    private HrmsCompanyLeaveConfigDO selectByNameAndUserCode(List<String> userCodeList, String leaveName) {
        CompanyLeaveQuery companyLeaveQuery = new CompanyLeaveQuery();
        companyLeaveQuery.setLeaveName(leaveName);
        companyLeaveQuery.setStatus(StatusEnum.ACTIVE.getCode());
        List<HrmsCompanyLeaveConfigDO> companyLeaveConfigDOList = hrmsCompanyLeaveConfigManage.selectConfigByUserCodeAndQueryList(userCodeList, companyLeaveQuery);
        if (CollectionUtils.isEmpty(companyLeaveConfigDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR.getDesc()));
        }
        List<HrmsCompanyLeaveConfigDO> sameTypeList = companyLeaveConfigDOList.stream().filter(item -> leaveName.equals(item.getLeaveName())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sameTypeList)) {
            return sameTypeList.get(0);
        }
        return companyLeaveConfigDOList.get(0);
    }

    /**
     * 查找冲突单据
     */
    private void selectClashApplication(Long userId, Date startDate, Date endDate, List<ClashApplicationInfoDTO> clashApplicationInfoDTOList) {
        ApplicationFormQuery formQuery = new ApplicationFormQuery();
        formQuery.setUserId(userId);
        formQuery.setFromTypeList(Arrays.asList(HrAttendanceApplicationFormTypeEnum.LEAVE.getCode(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode()));
        formQuery.setStatusList(Arrays.asList(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode(), HrAttendanceApplicationFormStatusEnum.PASS.getCode()));
        List<HrmsApplicationFormDO> applicationFormDOList = hrmsApplicationFormManage.selectForm(formQuery);
        List<Long> formIdList = applicationFormDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        //查找属性表
        List<HrmsApplicationFormAttrDO> formAttrDOList = hrmsApplicationFormAttrManage.selectFormAttrByFormIdLit(formIdList);
        Map<Long, List<HrmsApplicationFormAttrDO>> formIdMap = formAttrDOList.stream().collect(Collectors.groupingBy(HrmsApplicationFormAttrDO::getFormId));
        for (HrmsApplicationFormDO formDO : applicationFormDOList) {
            List<HrmsApplicationFormAttrDO> existFormList = formIdMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(existFormList)) {
                continue;
            }
            Map<String, HrmsApplicationFormAttrDO> attrMap = existFormList.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
            HrmsApplicationFormAttrDO dayInfoDO = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode());
            String descCN = null;
            String descEN = null;
            if (dayInfoDO != null) {
                List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoDO.getAttrValue(), DayDurationInfoDTO.class);
                BigDecimal days = BigDecimal.ZERO;
                BigDecimal hours = BigDecimal.ZERO;
                BigDecimal minutes = BigDecimal.ZERO;
                for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                    days = days.add(dayDurationInfoDTO.getDays());
                    hours = hours.add(dayDurationInfoDTO.getHours());
                    minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                }
                descCN = days + "天" + hours + "小时" + minutes + "分钟";
                descEN = days + "days" + hours + "hours" + minutes + "minutes";
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.LEAVE.getCode())) {
                HrmsApplicationFormAttrDO configIdDO = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.configID.getLowerCode());
                HrmsApplicationFormAttrDO leaveNameDO = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode());
                HrmsApplicationFormAttrDO leaveUnitDO = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveUnit.getLowerCode());
                HrmsApplicationFormAttrDO leaveStartDateDO = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
                HrmsApplicationFormAttrDO leaveEndDateDO = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode());

                if (leaveStartDateDO == null || leaveEndDateDO == null || leaveNameDO == null || leaveUnitDO == null) {
                    continue;
                }
                Date leaveStartDate = DateFormatUtils.parseDateTime(leaveStartDateDO.getAttrValue());
                Date leaveEndDate = DateFormatUtils.parseDateTime(leaveEndDateDO.getAttrValue());
                if (startDate.compareTo(leaveEndDate) > -1 || endDate.compareTo(leaveStartDate) < 1) {
                    //没有交集，没有冲突
                    continue;
                }
                ClashApplicationInfoDTO clashApplicationInfoDTO = new ClashApplicationInfoDTO();
                clashApplicationInfoDTO.setApplicationCode(formDO.getApplicationCode());
                clashApplicationInfoDTO.setApplicationFormId(formDO.getId());
                clashApplicationInfoDTO.setApprovalId(formDO.getApprovalId());
                clashApplicationInfoDTO.setFormType(formDO.getFormType());
                clashApplicationInfoDTO.setFormStatus(formDO.getFormStatus());
                clashApplicationInfoDTO.setConfigId(Objects.isNull(configIdDO) ? null : Long.parseLong(configIdDO.getAttrValue()));
                clashApplicationInfoDTO.setLeaveName(leaveNameDO.getAttrValue());
                clashApplicationInfoDTO.setLeaveUnit(leaveUnitDO.getAttrValue());
                clashApplicationInfoDTO.setStartDate(leaveStartDate);
                clashApplicationInfoDTO.setEndDate(leaveEndDate);
                clashApplicationInfoDTO.setExpectedTime(RequestInfoHolder.isChinese() ? descCN : descEN);
                clashApplicationInfoDTOList.add(clashApplicationInfoDTO);
                continue;
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode())) {
                HrmsApplicationFormAttrDO outOfOfficeStartDateDO = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode());
                HrmsApplicationFormAttrDO outOfOfficeEndDateDO = attrMap.get(HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode());
                if (outOfOfficeStartDateDO == null || outOfOfficeEndDateDO == null) {
                    continue;
                }
                Date outOfOfficeStartDate = DateFormatUtils.parseDateTime(outOfOfficeStartDateDO.getAttrValue());
                Date outOfOfficeEndDate = DateFormatUtils.parseDateTime(outOfOfficeEndDateDO.getAttrValue());
                if (startDate.compareTo(outOfOfficeEndDate) > -1 || endDate.compareTo(outOfOfficeStartDate) < 1) {
                    //没有交集，没有冲突
                    continue;
                }
                ClashApplicationInfoDTO clashApplicationInfoDTO = new ClashApplicationInfoDTO();
                clashApplicationInfoDTO.setApplicationCode(formDO.getApplicationCode());
                clashApplicationInfoDTO.setApplicationFormId(formDO.getId());
                clashApplicationInfoDTO.setApprovalId(formDO.getApprovalId());
                clashApplicationInfoDTO.setFormType(formDO.getFormType());
                clashApplicationInfoDTO.setFormStatus(formDO.getFormStatus());
                clashApplicationInfoDTO.setStartDate(outOfOfficeStartDate);
                clashApplicationInfoDTO.setEndDate(outOfOfficeEndDate);
                clashApplicationInfoDTO.setExpectedTime(RequestInfoHolder.isChinese() ? descCN : descEN);
                clashApplicationInfoDTOList.add(clashApplicationInfoDTO);
            }
        }
    }

    /**
     * 外勤/请假-每天时长计算明细
     * 需要获取请假开始时间前一天-请假结束时间后一天的所有排班
     */
    private void dayDurationInfoHandler(Long userId, Date startDate, Date endDate, HrmsCompanyLeaveConfigDO companyLeaveConfigDO, List<DayDurationInfoDTO> dayDurationInfoDTOList, BigDecimal dayAttendanceHours) {
        HrmsUserInfoDO userInfoDO = hrmsUserInfoManage.getUserInfoById(userId);
        if (userInfoDO == null || ObjectUtil.isEmpty(userInfoDO.getUserCode())) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }

        Long beforeDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(startDate, -1), "yyyyMMdd"));
        Long afterDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(endDate, 1), "yyyyMMdd"));
        // 获取打卡记录查询的开始时间和结束时间[这边查询的时间范围是前一天的开始时间和后一天的结束时间，存在跨天情况]
        DateTime punchRecordStartTime = DateUtil.beginOfDay(DateUtil.offsetDay(startDate, -1));
        DateTime punchRecordEndTime = DateUtil.endOfDay(DateUtil.offsetDay(endDate, 1));

        //查询用户的打卡规则范围，看是否当天免打卡
//        List<HrmsAttendancePunchConfigRangeDO> dayPunchConfigRangeDOS = hrmsAttendancePunchConfigRangeManage.selectUserAllConfigRangeByBizId(Arrays.asList(userId));
        List<HrmsAttendancePunchConfigRangeDO> dayPunchConfigRangeDOS = punchConfigManageAdapter.selectUserAllConfigRangeByBizId(Arrays.asList(userId));

        //查询用户的排班记录(前后各多加1天)  可以为空，没有排班
        List<HrmsAttendanceClassEmployeeConfigDO> classEmployeeConfigDOS = hrmsAttendanceClassEmployeeConfigManage.selectRecordByUserIdList(Arrays.asList(userId), beforeDayId, afterDayId);
        Map<Long, List<HrmsAttendanceClassEmployeeConfigDO>> employeeDayIdMap = classEmployeeConfigDOS.stream().collect(Collectors.groupingBy(HrmsAttendanceClassEmployeeConfigDO::getDayId));
        List<Long> employeeConfigIdList = classEmployeeConfigDOS.stream().map(item -> item.getId()).collect(Collectors.toList());

        //生成班次/明细  可能为OFF/PH排班 classId为空
        List<Long> punchClassIdList = classEmployeeConfigDOS.stream().filter(item -> item.getClassId() != null).map(item -> item.getClassId()).collect(Collectors.toList());
//        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = hrmsAttendancePunchClassConfigManage.selectClassByIdList(punchClassIdList);
        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = punchConfigManageAdapter.selectClassByIdList(punchClassIdList);
        Map<Long, List<HrmsAttendancePunchClassConfigDO>> classConfig = classConfigDOS.stream().collect(Collectors.groupingBy(HrmsAttendancePunchClassConfigDO::getId));
        List<Long> punchIdList = classConfigDOS.stream().map(item -> item.getPunchConfigId()).collect(Collectors.toList());
//        List<HrmsAttendancePunchConfigDO> punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByIdList(punchIdList);
        List<HrmsAttendancePunchConfigDO> punchConfigDOList = punchConfigManageAdapter.selectAttendancePunchByIdList(punchIdList);
        Map<Long, List<HrmsAttendancePunchConfigDO>> punchConfigMap = punchConfigDOList.stream().collect(Collectors.groupingBy(HrmsAttendancePunchConfigDO::getId));
//        List<HrmsAttendancePunchClassItemConfigDO> punchClassItemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(punchClassIdList);
        List<HrmsAttendancePunchClassItemConfigDO> punchClassItemConfigDOList = punchConfigManageAdapter.selectItemConfigByClassId(punchClassIdList);
        Map<Long, List<HrmsAttendancePunchClassItemConfigDO>> punchClassItemConfigMap = punchClassItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsAttendancePunchClassItemConfigDO::getPunchClassId));

        // 查询用户信息
        // 查询用户的打卡记录
        EmployeePunchCardRecordQuery query = new EmployeePunchCardRecordQuery();
        query.setStartTime(punchRecordStartTime);
        query.setEndTime(punchRecordEndTime);
        query.setUserCode(userInfoDO.getUserCode());
        List<EmployeePunchRecordDO> allPunchRecordList = punchRecordDao.listRecords(query);
        List<UserPunchRecordDTO> userPunchRecordDTOList = new ArrayList<>();
        for (EmployeePunchRecordDO employeePunchRecordDO : allPunchRecordList) {
            UserPunchRecordDTO userPunchRecordDTO = new UserPunchRecordDTO();
            userPunchRecordDTO.setId(employeePunchRecordDO.getId());
            userPunchRecordDTO.setUserCode(employeePunchRecordDO.getUserCode());
            userPunchRecordDTO.setFormId(employeePunchRecordDO.getFormId());
            String punchTimeString = DateUtil.format(employeePunchRecordDO.getPunchTime(), "yyyy-MM-dd HH:mm");
            userPunchRecordDTO.setFormatPunchTime(DateUtil.parse(punchTimeString + ":00", "yyyy-MM-dd HH:mm:ss"));
            userPunchRecordDTOList.add(userPunchRecordDTO);
        }

        Long tempDayId = beforeDayId;
        while (tempDayId <= afterDayId) {
            Long finalTempDayId = tempDayId;
            //后移一天
            tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));
            //查询该天的排班
            List<HrmsAttendanceClassEmployeeConfigDO> dayEmployeeList = employeeDayIdMap.get(finalTempDayId);

            //如果是请假，并且请假单位是天，特殊处理
//            if (companyLeaveConfigDO != null && StringUtils.equalsIgnoreCase(companyLeaveConfigDO.getLeaveUnit(), LeaveUnitEnum.DAYS.getCode())) {
//                if (finalTempDayId.equals(endDayId)) {
//                    continue;
//                }
//            }
            //当天的结束时间
            Date finalDateNow = DateUtil.endOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"));
            //当天免打卡
            List<HrmsAttendancePunchConfigRangeDO> noNeedDayPunchConfigRangeDOS = dayPunchConfigRangeDOS.stream()
                    .filter(item -> item.getEffectTime().compareTo(finalDateNow) < 1 && item.getExpireTime().compareTo(finalDateNow) > -1)
                    .filter(item -> item.getIsNeedPunch().equals(BusinessConstant.N)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noNeedDayPunchConfigRangeDOS)) {
                dayDurationNoNeedHandler(beforeDayId, afterDayId, finalTempDayId, startDate, endDate, dayDurationInfoDTOList);
                continue;
            }

            //找到循环当天得前一天的排班，需要验证是否跨天
            Long currentBeforeDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(finalTempDayId.toString()), -1), "yyyyMMdd"));
            List<HrmsAttendanceClassEmployeeConfigDO> dayBeforeEmployeeList = employeeDayIdMap.get(currentBeforeDayId);
            List<HrmsAttendancePunchClassItemConfigDO> beforeDayItemList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(dayBeforeEmployeeList) && Objects.nonNull(dayBeforeEmployeeList.get(0).getClassId())) {
                beforeDayItemList = punchClassItemConfigMap.get(dayBeforeEmployeeList.get(0).getClassId());
            }
            //当天么有排班
            if (CollectionUtils.isEmpty(dayEmployeeList)) {
                dayDurationNoShiftHandler(beforeDayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate, dayDurationInfoDTOList);
                continue;
            }

            //当天有排班
            //当天是PH/OFF
            if (dayEmployeeList.get(0).getClassId() == null) {
                dayDurationPhHandler(beforeDayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate
                        , dayEmployeeList.get(0).getDayPunchType(), companyLeaveConfigDO, dayDurationInfoDTOList);
                continue;
            }

            List<HrmsAttendancePunchConfigDO> dayPunchConfigList = punchConfigMap.get(dayEmployeeList.get(0).getPunchConfigId());
            if (CollectionUtils.isEmpty(dayPunchConfigList)) {
                continue;
            }
            List<HrmsAttendancePunchClassConfigDO> dayPunchClassList = classConfig.get(dayEmployeeList.get(0).getClassId());
            if (CollectionUtils.isEmpty(dayPunchClassList)) {
                continue;
            }
            List<HrmsAttendancePunchClassItemConfigDO> dayItemList = punchClassItemConfigMap.get(dayEmployeeList.get(0).getClassId());
            if (CollectionUtils.isEmpty(dayItemList)) {
                continue;
            }
            dayItemList = dayItemList.stream().sorted(Comparator.comparing(HrmsAttendancePunchClassItemConfigDO::getSortNo)).collect(Collectors.toList());
            //当天排班是自由打卡规则
            if (StringUtils.equalsIgnoreCase(dayPunchConfigList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())) {
                dayDurationFreeShiftHandler(dayPunchClassList.get(0), dayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate, dayDurationInfoDTOList);
                continue;
            }
            //当天排班是一次打卡规则
            if (StringUtils.equalsIgnoreCase(dayPunchConfigList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FIXED_WORK_ONCE.name())) {
                dayOnceNormalShiftHandler(dayPunchClassList.get(0), dayItemList, finalTempDayId, startDate, endDate, dayDurationInfoDTOList);
                continue;
            }
            //固定/班次
            dayDurationNormalShiftHandler(dayPunchClassList.get(0), dayItemList, beforeDayId, afterDayId, finalTempDayId, startDate, endDate, dayDurationInfoDTOList, userPunchRecordDTOList);
        }
    }

    private void dayDurationInfoDTOBuild(Long dayId, BigDecimal days, BigDecimal hours, BigDecimal minutes, BigDecimal legalWorkingHours, List<String> dayShiftInfoList, List<String> leaveInfoList, List<String> restInfoList, List<DayDurationInfoDTO> dayDurationInfoDTOList) {
        DayDurationInfoDTO dayDurationInfoDTO = new DayDurationInfoDTO();
        dayDurationInfoDTO.setDayId(dayId);
        dayDurationInfoDTO.setDays(days);
        dayDurationInfoDTO.setHours(hours);
        dayDurationInfoDTO.setMinutes(minutes);
        dayDurationInfoDTO.setLegalWorkingHours(legalWorkingHours);
        dayDurationInfoDTO.setDayShiftInfoList(dayShiftInfoList);
        dayDurationInfoDTO.setLeaveInfoList(leaveInfoList);
        dayDurationInfoDTO.setRestInfoList(restInfoList);
        dayDurationInfoDTOList.add(dayDurationInfoDTO);
    }

    private void dayDurationNoNeedHandler(Long beforeDayId, Long afterDayId, Long finalTempDayId, Date startDate, Date endDate, List<DayDurationInfoDTO> dayDurationInfoDTOList) {
        //是请假开始的前置一天/后置一天
        if (finalTempDayId.compareTo(beforeDayId) == 0 || finalTempDayId.compareTo(afterDayId) == 0) {
            //todo 这里暂时先不处理了，后续可以补上
            return;
        }
        Date actualBeginDayDate = DateUtil.beginOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"));
        Date actualEndDayDate = DateUtil.beginOfDay(DateUtil.offsetDay(actualBeginDayDate, 1));
        //没有交集
        int days = 0;
        int hours = 0;
        int minutes = 0;

        String dayShiftInfo = RequestInfoHolder.isChinese() ? "免打卡" : "No Punch Card";
        String leaveInfo = null;
        //自由打卡肯定是今天和明天之间的，跨天
        if (actualEndDayDate.compareTo(startDate) < 1) {
            return;
        }
        if (actualBeginDayDate.compareTo(endDate) > -1) {
            return;
        }
        //请假完全包含本次自由打卡的上下班
        if (actualBeginDayDate.compareTo(startDate) > -1 && actualEndDayDate.compareTo(endDate) < 1) {
            days = 1;
            leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //打卡上下班时间完全包含请假
        if (actualBeginDayDate.compareTo(startDate) < 1 && actualEndDayDate.compareTo(endDate) > -1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
            minutes = (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
            if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //左边交集
        if (actualBeginDayDate.compareTo(startDate) < 1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
            minutes = (int) DateUtil.between(startDate, actualEndDayDate, DateUnit.MINUTE);
            if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //右边交集
        leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
        minutes = (int) DateUtil.between(actualBeginDayDate, endDate, DateUnit.MINUTE);
        if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
            days = 1;
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //不足一天
        hours = minutes / (BusinessConstant.MINUTES.intValue());
        minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
    }

    private void dayDurationNoShiftHandler(List<HrmsAttendancePunchClassItemConfigDO> beforeDayItemList
            , Long beforeDayId, Long afterDayId, Long finalTempDayId
            , Date startDate, Date endDate
            , List<DayDurationInfoDTO> dayDurationInfoDTOList) {

        //是请假开始的前置一天/后置一天
        if (finalTempDayId.compareTo(beforeDayId) == 0 || finalTempDayId.compareTo(afterDayId) == 0) {
            //todo 这里暂时先不处理了，后续可以补上
            return;
        }
        Date actualBeginDayDate = DateUtil.beginOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"));
        Date actualEndDayDate = DateUtil.beginOfDay(DateUtil.offsetDay(actualBeginDayDate, 1));
        //查看前一天的班次是否跨天
        if (CollectionUtils.isNotEmpty(beforeDayItemList)) {
            beforeDayItemList = beforeDayItemList.stream().sorted(Comparator.comparing(HrmsAttendancePunchClassItemConfigDO::getSortNo)).collect(Collectors.toList());
            //查看前一天最后一个时段是否跨天,跨天则需要修改休息日当天的上班打卡时间为前一天最晚时段的下班时间
            HrmsAttendancePunchClassItemConfigDO lastItemConfig = beforeDayItemList.get(beforeDayItemList.size() - 1);
            if (lastItemConfig.getIsAcross() == 1) {
                Date punchOutTime = Objects.isNull(lastItemConfig.getPunchOutTime()) ? lastItemConfig.getLatestPunchOutTime() : lastItemConfig.getPunchOutTime();
                String punchOutTimeString = DateUtil.format(punchOutTime, "HH:mm:ss");
                String punchOutTimeDayString = DateUtil.format(actualBeginDayDate, "yyyy-MM-dd");
                actualBeginDayDate = DateUtil.parse(punchOutTimeDayString + " " + punchOutTimeString, "yyyy-MM-dd HH:mm:ss");
            }
        }
        //没有交集
        int days = 0;
        int hours = 0;
        int minutes = 0;

        String dayShiftInfo = RequestInfoHolder.isChinese() ? "未排班" : "Not scheduled";
        String leaveInfo = null;
        //自由打卡肯定是今天和明天之间的，跨天
        if (actualEndDayDate.compareTo(startDate) < 1) {
            return;
        }
        if (actualBeginDayDate.compareTo(endDate) > -1) {
            return;
        }
        //请假完全包含本次自由打卡的上下班
        if (actualBeginDayDate.compareTo(startDate) > -1 && actualEndDayDate.compareTo(endDate) < 1) {
            days = 1;
            leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //打卡上下班时间完全包含请假
        if (actualBeginDayDate.compareTo(startDate) < 1 && actualEndDayDate.compareTo(endDate) > -1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
            minutes = (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
            if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //左边交集
        if (actualBeginDayDate.compareTo(startDate) < 1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
            minutes = (int) DateUtil.between(startDate, actualEndDayDate, DateUnit.MINUTE);
            if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //右边交集
        leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
        minutes = (int) DateUtil.between(actualBeginDayDate, endDate, DateUnit.MINUTE);
        if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
            days = 1;
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //不足一天
        hours = minutes / (BusinessConstant.MINUTES.intValue());
        minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
    }

    private void dayDurationPhHandler(List<HrmsAttendancePunchClassItemConfigDO> beforeDayItemList
            , Long beforeDayId, Long afterDayId, Long finalTempDayId
            , Date startDate, Date endDate, String dayPunchType
            , HrmsCompanyLeaveConfigDO companyLeaveConfigDO
            , List<DayDurationInfoDTO> dayDurationInfoDTOList) {

        //是请假开始的前置一天/后置一天，表示不存在排班跨天的情况
        if (finalTempDayId.compareTo(beforeDayId) == 0 || finalTempDayId.compareTo(afterDayId) == 0) {
            return;
        }
        String dayShiftInfo = RequestInfoHolder.isChinese() ? "休息日/节假日" : "OFF/PH";
        String leaveInfo = null;
        Date actualBeginDayDate = DateUtil.beginOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"));
        Date actualEndDayDate = DateUtil.beginOfDay(DateUtil.offsetDay(actualBeginDayDate, 1));
        //查看前一天的班次是否跨天
        if (CollectionUtils.isNotEmpty(beforeDayItemList)) {
            beforeDayItemList = beforeDayItemList.stream().sorted(Comparator.comparing(HrmsAttendancePunchClassItemConfigDO::getSortNo)).collect(Collectors.toList());
            //查看前一天最后一个时段是否跨天,跨天则需要修改休息日当天的上班打卡时间为前一天最晚时段的下班时间
            HrmsAttendancePunchClassItemConfigDO lastItemConfig = beforeDayItemList.get(beforeDayItemList.size() - 1);
            if (lastItemConfig.getIsAcross() == 1) {
                Date punchOutTime = Objects.isNull(lastItemConfig.getPunchOutTime()) ? lastItemConfig.getLatestPunchOutTime() : lastItemConfig.getPunchOutTime();
                String punchOutTimeString = DateUtil.format(punchOutTime, "HH:mm:ss");
                String punchOutTimeDayString = DateUtil.format(actualBeginDayDate, "yyyy-MM-dd");
                actualBeginDayDate = DateUtil.parse(punchOutTimeDayString + " " + punchOutTimeString, "yyyy-MM-dd HH:mm:ss");
            }
        }
        //没有交集
        if (actualEndDayDate.compareTo(startDate) < 1) {
            return;
        }
        if (actualBeginDayDate.compareTo(endDate) > -1) {
            return;
        }
        int days = 0;
        int hours = 0;
        int minutes = 0;
        //请假完全包含本天
        if (actualBeginDayDate.compareTo(startDate) > -1 && actualEndDayDate.compareTo(endDate) < 1) {
            //外勤/请假(消耗假期)
            if (companyLeaveConfigDO != null && leaveConfigService.isConsumeLeave(companyLeaveConfigDO.getConsumeLeaveType(), dayPunchType)) {
                days = 1;
                leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO
                        , BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo)
                        , Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //请假(不消耗假期)
            leaveInfo = RequestInfoHolder.isChinese() ? "不消耗假期" : "Free leave";
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes)
                    , BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo)
                    , new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //改天上下班时间完全包含请假
        if (actualBeginDayDate.compareTo(startDate) < 1 && actualEndDayDate.compareTo(endDate) > -1) {
            //外勤/请假(消耗假期)
            if (companyLeaveConfigDO != null && leaveConfigService.isConsumeLeave(companyLeaveConfigDO.getConsumeLeaveType(), dayPunchType)) {
                leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
                minutes = (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                    days = 1;
                    dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                    return;
                }
                //不足一天
                hours = minutes / (BusinessConstant.MINUTES.intValue());
                minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //请假(不消耗假期)
            leaveInfo = RequestInfoHolder.isChinese() ? "不消耗假期" : "Free leave";
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //左边交集
        if (actualBeginDayDate.compareTo(startDate) < 1) {
            //外勤/请假(消耗假期)
            if (companyLeaveConfigDO != null && leaveConfigService.isConsumeLeave(companyLeaveConfigDO.getConsumeLeaveType(), dayPunchType)) {
                leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
                minutes = (int) DateUtil.between(startDate, actualEndDayDate, DateUnit.MINUTE);
                if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                    days = 1;
                    dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                    return;
                }
                //不足一天
                hours = minutes / (BusinessConstant.MINUTES.intValue());
                minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //请假(不消耗假期)
            leaveInfo = RequestInfoHolder.isChinese() ? "不消耗假期" : "Free leave";
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //右边交集
        //外勤/请假(消耗假期)
        if (companyLeaveConfigDO != null && leaveConfigService.isConsumeLeave(companyLeaveConfigDO.getConsumeLeaveType(), dayPunchType)) {
            leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
            minutes = (int) DateUtil.between(actualBeginDayDate, endDate, DateUnit.MINUTE);
            if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //请假(不消耗假期)
        leaveInfo = RequestInfoHolder.isChinese() ? "不消耗假期" : "Free leave";
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
    }

    private void dayDurationFreeShiftHandler(HrmsAttendancePunchClassConfigDO punchClassConfigDO, List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOS,
                                             Long beforeDayId, Long afterDayId, Long finalTempDayId, Date startDate, Date endDate, List<DayDurationInfoDTO> dayDurationInfoDTOList) {
        //获取最早打卡时间和最晚打卡时间
        String earliestPunchInTimeMinute = DateUtil.format(itemConfigDOS.get(0).getEarliestPunchInTime(), "HH:mm:ss");
        String earliestPunchInTimeDay = DateUtil.format(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"), "yyyy-MM-dd");
        Date earliestPunchInTime = DateUtil.parse(earliestPunchInTimeDay + " " + earliestPunchInTimeMinute, "yyyy-MM-dd HH:mm:ss");
        Date latestPunchOutTime = DateUtil.offsetDay(earliestPunchInTime, 1);

        //是请假开始的前置一天/后置一天
        if (finalTempDayId.compareTo(beforeDayId) == 0 || finalTempDayId.compareTo(afterDayId) == 0) {
            //todo 这里暂时先不处理了，后续可以补上
            return;
        }
        String dayShiftInfo = DateUtil.format(earliestPunchInTime, "HH:mm") + "-" + DateUtil.format(latestPunchOutTime, "HH:mm");
        String leaveInfo = null;
        //自由打卡肯定是今天和明天之间的，跨天
        if (latestPunchOutTime.compareTo(startDate) < 1) {
            return;
        }
        if (earliestPunchInTime.compareTo(endDate) > -1) {
            return;
        }
        int days = 0;
        int hours = 0;
        int minutes = 0;

        //请假完全包含本次自由打卡的上下班
        if (earliestPunchInTime.compareTo(startDate) > -1 && latestPunchOutTime.compareTo(endDate) < 1) {
            days = 1;
            leaveInfo = dayShiftInfo;
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //打卡上下班时间完全包含请假
        if (earliestPunchInTime.compareTo(startDate) < 1 && latestPunchOutTime.compareTo(endDate) > -1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
            minutes = (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
            if (minutes >= (punchClassConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //左边交集
        if (earliestPunchInTime.compareTo(startDate) < 1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(latestPunchOutTime, "HH:mm");
            minutes = (int) DateUtil.between(startDate, latestPunchOutTime, DateUnit.MINUTE);
            if (minutes >= (punchClassConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //右边交集
        leaveInfo = DateUtil.format(earliestPunchInTime, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
        minutes = (int) DateUtil.between(earliestPunchInTime, endDate, DateUnit.MINUTE);
        if (minutes >= (punchClassConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES).intValue())) {
            days = 1;
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //不足一天
        hours = minutes / (BusinessConstant.MINUTES.intValue());
        minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
    }

    private void dayDurationNormalShiftHandler(HrmsAttendancePunchClassConfigDO punchClassConfigDO, List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOS,
                                               Long beforeDayId, Long afterDayId, Long finalTempDayId, Date startDate, Date endDate, List<DayDurationInfoDTO> dayDurationInfoDTOList, List<UserPunchRecordDTO> userPunchRecordDTOList) {
//        //是请假开始的前置一天
//        if (finalTempDayId.compareTo(beforeDayId) == 0) {
//            //判断
//            return;
//        }
//        //是请假开始的后置一天
//        if (finalTempDayId.compareTo(afterDayId) == 0) {
//            return;
//        }

        int days = 0;
        int hours = 0;
        int minutes = 0;
        List<String> dayShiftInfoList = new ArrayList<>();
        List<String> leaveInfoList = new ArrayList<>();
        List<String> restInfoList = new ArrayList<>();
        List<Date> leaveStartList = new ArrayList<>();
        List<Date> leaveEndList = new ArrayList<>();

        //针对每个时刻进行处理
        for (HrmsAttendancePunchClassItemConfigDO itemConfigDO : itemConfigDOS) {
            String shiftInfo = DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm") + "-" + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm");
            dayShiftInfoList.add(shiftInfo);
            if (itemConfigDO.getRestStartTime() != null) {
                String restInfo = DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm") + "-" + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm");
                restInfoList.add(restInfo);
            }

            DayNormalPunchTimeDTO dayNormalPunchTimeDTO = hrmsAttendanceBaseService.getUserPunchClassNormalItemDayTime(finalTempDayId, itemConfigDO.getId(), itemConfigDOS);
            if (dayNormalPunchTimeDTO == null) {
                continue;
            }

            // 获取弹性时长
            BigDecimal elasticTime = itemConfigDO.getElasticTime();
            // 班次打卡以及固定打卡两种情况，一定会存在弹性时长，所以这边为了防止空指针出现，null的时候设置为0
            elasticTime = ObjectUtil.isNull(elasticTime) ? BigDecimal.ZERO : elasticTime;
            BigDecimal elasticMinutesTime = elasticTime.multiply(BusinessConstant.MINUTES);
            // 获取弹性时长的下班时间
            DateTime elasticPunchEndTime = DateUtil.offsetMinute(dayNormalPunchTimeDTO.getDayPunchEndTime(), elasticMinutesTime.intValue());
            // 获取上班时间
            Date punchInTime = itemConfigDO.getPunchInTime();
            // 获取最早上班时间
            Date earliestPunchInTime = itemConfigDO.getEarliestPunchInTime();
            // 获取最晚上班时间
            Date latestPunchInTime = itemConfigDO.getLatestPunchInTime();
            // 获取最早上班时间精确的年月日时分秒
            Date earliestPunchInStartTime = null;
            // 如果最早上班时间大于上班时间，说明最早时间跨天了
            if (earliestPunchInTime.compareTo(punchInTime) > 0) {
                String earliestPunchInTimeString = DateUtil.format(earliestPunchInTime, "HH:mm:ss");
                String earliestPunchInTimeDayString = DateUtil.format(DateUtil.offsetDay(dayNormalPunchTimeDTO.getDayPunchStartTime(), -1), "yyyy-MM-dd");
                earliestPunchInStartTime = DateUtil.parse(earliestPunchInTimeDayString + " " + earliestPunchInTimeString, "yyyy-MM-dd HH:mm:ss");
            } else {
                String earliestPunchInTimeString = DateUtil.format(earliestPunchInTime, "HH:mm:ss");
                String earliestPunchInTimeDayString = DateUtil.format(dayNormalPunchTimeDTO.getDayPunchStartTime(), "yyyy-MM-dd");
                earliestPunchInStartTime = DateUtil.parse(earliestPunchInTimeDayString + " " + earliestPunchInTimeString, "yyyy-MM-dd HH:mm:ss");
            }
            // 获取最晚上班时间精确的年月日时分秒：【这个时间的年月日一定是跟打卡时间同样的年月日】
            String latestPunchInTimeDayString = DateUtil.format(dayNormalPunchTimeDTO.getDayPunchStartTime(), "yyyy-MM-dd");
            String latestPunchInTimeString = DateUtil.format(latestPunchInTime, "HH:mm:ss");
            Date latestPunchInStartTime = DateUtil.parse(latestPunchInTimeDayString + " " + latestPunchInTimeString, "yyyy-MM-dd HH:mm:ss");

            // 获取最早上班时间->最晚上班时间之间的打卡数据
            Date finalEarliestPunchInStartTime = earliestPunchInStartTime;
            List<UserPunchRecordDTO> itemPunchRecordList = userPunchRecordDTOList.stream()
                    .filter(item -> item.getFormatPunchTime().compareTo(finalEarliestPunchInStartTime) > -1 && item.getFormatPunchTime().compareTo(latestPunchInStartTime) < 1)
                    .sorted(Comparator.comparing(UserPunchRecordDTO::getFormatPunchTime)).collect(Collectors.toList());
            // 获取最新的一个打卡记录
            UserPunchRecordDTO userPunchRecordDTO = null;
            long betweenMinutes = 0;
            if (CollUtil.isNotEmpty(itemPunchRecordList)) {
                userPunchRecordDTO = itemPunchRecordList.get(0);
                // 如果最近的一条打卡记录是大于等于上班时间，小于等于最晚上班时间也就是弹性之后的，那么需要根据打卡时间进行弹性处理
                if (userPunchRecordDTO.getFormatPunchTime().compareTo(dayNormalPunchTimeDTO.getDayPunchStartTime()) > -1 && userPunchRecordDTO.getFormatPunchTime().compareTo(latestPunchInStartTime) < 1) {
                    // 获取两个时间相差分钟数
                    betweenMinutes = DateUtil.between(userPunchRecordDTO.getFormatPunchTime(), dayNormalPunchTimeDTO.getDayPunchStartTime(), DateUnit.MINUTE);
                }
            }
            // 这边根据上班时间来设置下班时间是正常的班次时间还是弹性后的时间
            Date actualDayPunchEndTime = dayNormalPunchTimeDTO.getDayPunchEndTime();
            if (betweenMinutes != 0) {
                actualDayPunchEndTime = DateUtil.offsetMinute(dayNormalPunchTimeDTO.getDayPunchEndTime(), (int) betweenMinutes);
            }

            //该时刻和请假没有交集
            if (actualDayPunchEndTime.compareTo(startDate) < 1) {
                continue;
            }
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(endDate) > -1) {
                continue;
            }

            //请假完全包含本次时刻打卡的上下班
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) > -1 && dayNormalPunchTimeDTO.getDayPunchEndTime().compareTo(endDate) < 1) {
                minutes = minutes + ((int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), dayNormalPunchTimeDTO.getDayPunchEndTime(), DateUnit.MINUTE));
                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() != null) {
                    minutes = minutes - ((int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE));
                }
                leaveStartList.add(dayNormalPunchTimeDTO.getDayPunchStartTime());
                leaveEndList.add(dayNormalPunchTimeDTO.getDayPunchEndTime());
                continue;
            }
            //打卡上下班时间完全包含请假
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) < 1 && dayNormalPunchTimeDTO.getDayPunchEndTime().compareTo(endDate) > -1) {
                leaveStartList.add(startDate);
                leaveEndList.add(endDate);
                //注意休息时间没有的情况  后续情况都是有休息时间
                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    continue;
                }
                //特殊处理，注意请假时间和休息时间的交际
                if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1 || startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    continue;
                }
                //休息时间包含请假
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) > -1 && endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) < 1) {
                    continue;
                }
                //请假包含休息时间
                if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1 && startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                    continue;
                }
                //左边交集
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 0) {
                    minutes = minutes + (int) DateUtil.between(startDate, dayNormalPunchTimeDTO.getDayPunchRestStartTime(), DateUnit.MINUTE);
                    continue;
                }
                //右边交集
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestEndTime(), endDate, DateUnit.MINUTE);
                continue;
            }

            //左边交集 ： 班次开始时间小于等于请假的开始时间，则一定是【9:00 16:00 17:00 18:00】的情况：因为上面已经判断了请假时长完全包含上班时长、上班时长完全包含请假时长这两种情况
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) < 1) {
                Date actualLeaveTime = null;
                leaveStartList.add(startDate);
                // 需要修改这边的endTime，根据打卡时间来判断【背景：班次 9:00-17:00，早上打卡时间：9:15，晚上打卡时间：16:00.现在需要请假，请假时间16:00-18:00。这个时候请假需要计算上弹性时间，就是请假的endTime需要是16:00-17:15，而不是16:00-17:00，因为16:00-17:00生成当天考勤还是早退】
                //leaveEndList.add(dayNormalPunchTimeDTO.getDayPunchEndTime());
                // 如果请假结束时间小于等于弹性后的下班时间 ，则取请假结束时间，否则取弹性下班时间
                //if (endDate.compareTo(elasticPunchEndTime) < 1) {
                //    leaveEndList.add(endDate);
                //    actualLeaveTime = endDate;
                //} else {
                //    leaveEndList.add(elasticPunchEndTime);
                //    actualLeaveTime = elasticPunchEndTime;
                //}
                // 最新版弹性
                if (betweenMinutes == 0) {
                    // 无需弹性时间
                    actualLeaveTime = dayNormalPunchTimeDTO.getDayPunchEndTime();
                    leaveEndList.add(dayNormalPunchTimeDTO.getDayPunchEndTime());
                } else {
                    // 如果请假结束时间小于等于应该弹性后的下班时间 ，则取请假结束时间，否则取应该弹性下班时间。应该弹性下班时间：【比如9-18，弹性半小时，早上9:10打卡，则应弹性下班时间：18:10】
                    if (endDate.compareTo(actualDayPunchEndTime) < 1) {
                        leaveEndList.add(endDate);
                        actualLeaveTime = endDate;
                    } else {
                        leaveEndList.add(actualDayPunchEndTime);
                        actualLeaveTime = actualDayPunchEndTime;
                    }
                    // 需要弹性时间
                    //actualLeaveTime = DateUtil.offsetMinute(dayNormalPunchTimeDTO.getDayPunchEndTime(), (int) betweenMinutes);
                    //leaveEndList.add(actualLeaveTime);
                }
                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    continue;
                }
                //特殊处理，注意请假时间和休息时间的交际
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    continue;
                }
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    minutes = minutes - (int) DateUtil.between(startDate, dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                    continue;
                }
                minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                continue;
            }

            //右边交集：班次开始时间大于请假的开始时间，则一定是【8:00 9:00 16:00 18:00】的情况：因为上面已经判断了请假时长完全包含上班时长、上班时长完全包含请假时长、上班时长小于等于请假开始时间的这三种情况。
            // 这边无需做特殊处理了，因为请假开始时间如果小于班次时间，则一定是取班次时间作为请假开始时间
            leaveStartList.add(dayNormalPunchTimeDTO.getDayPunchStartTime());
            leaveEndList.add(endDate);
            if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            //特殊处理，注意请假时间和休息时间的交际
            if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) < 1) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
            minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
        }

        if (CollectionUtils.isNotEmpty(leaveStartList)) {
            Collections.sort(leaveStartList);
            Collections.sort(leaveEndList);
            String dayShiftInfo = DateUtil.format(leaveStartList.get(0), "HH:mm") + "-" + DateUtil.format(leaveEndList.get(leaveEndList.size() - 1), "HH:mm");
            leaveInfoList.add(dayShiftInfo);
        }
        if (minutes >= (punchClassConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES).intValue())) {
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), dayShiftInfoList, leaveInfoList, restInfoList, dayDurationInfoDTOList);
            return;
        }
        //不足一天
        hours = minutes / (BusinessConstant.MINUTES.intValue());
        minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), punchClassConfigDO.getLegalWorkingHours(), dayShiftInfoList, leaveInfoList, restInfoList, dayDurationInfoDTOList);

    }

    private void dayOnceNormalShiftHandler(HrmsAttendancePunchClassConfigDO punchClassConfigDO, List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOS,
                                           Long finalTempDayId, Date startDate, Date endDate, List<DayDurationInfoDTO> dayDurationInfoDTOList) {

        int days = 0;
        int hours = 0;
        int minutes = 0;
        List<String> dayShiftInfoList = new ArrayList<>();
        List<String> leaveInfoList = new ArrayList<>();
        List<String> restInfoList = new ArrayList<>();
        List<Date> leaveStartList = new ArrayList<>();
        List<Date> leaveEndList = new ArrayList<>();

        //针对每个时刻进行处理
        for (HrmsAttendancePunchClassItemConfigDO itemConfigDO : itemConfigDOS) {
            String shiftInfo = DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm") + "-" + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm");
            dayShiftInfoList.add(shiftInfo);
            if (itemConfigDO.getRestStartTime() != null) {
                String restInfo = DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm") + "-" + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm");
                restInfoList.add(restInfo);
            }

            DayNormalPunchTimeDTO dayNormalPunchTimeDTO = hrmsAttendanceBaseService.getUserPunchClassNormalItemDayTime(finalTempDayId, itemConfigDO.getId(), itemConfigDOS);
            if (dayNormalPunchTimeDTO == null) {
                continue;
            }
            // 这边根据上班时间来设置下班时间是正常的班次时间还是弹性后的时间
            Date actualDayPunchEndTime = dayNormalPunchTimeDTO.getDayPunchEndTime();

            //该时刻和请假没有交集
            if (actualDayPunchEndTime.compareTo(startDate) < 1) {
                continue;
            }
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(endDate) > -1) {
                continue;
            }

            //请假完全包含本次时刻打卡的上下班
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) > -1 && dayNormalPunchTimeDTO.getDayPunchEndTime().compareTo(endDate) < 1) {
                minutes = minutes + ((int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), dayNormalPunchTimeDTO.getDayPunchEndTime(), DateUnit.MINUTE));
                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() != null) {
                    minutes = minutes - ((int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE));
                }
                leaveStartList.add(dayNormalPunchTimeDTO.getDayPunchStartTime());
                leaveEndList.add(dayNormalPunchTimeDTO.getDayPunchEndTime());
                continue;
            }
            //打卡上下班时间完全包含请假
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) < 1 && dayNormalPunchTimeDTO.getDayPunchEndTime().compareTo(endDate) > -1) {
                leaveStartList.add(startDate);
                leaveEndList.add(endDate);
                //注意休息时间没有的情况  后续情况都是有休息时间
                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    continue;
                }
                //特殊处理，注意请假时间和休息时间的交际
                if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1 || startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    continue;
                }
                //休息时间包含请假
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) > -1 && endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) < 1) {
                    continue;
                }
                //请假包含休息时间
                if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1 && startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                    continue;
                }
                //左边交集
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 0) {
                    minutes = minutes + (int) DateUtil.between(startDate, dayNormalPunchTimeDTO.getDayPunchRestStartTime(), DateUnit.MINUTE);
                    continue;
                }
                //右边交集
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestEndTime(), endDate, DateUnit.MINUTE);
                continue;
            }

            //左边交集 ： 班次开始时间小于等于请假的开始时间，则一定是【9:00 16:00 17:00 18:00】的情况：因为上面已经判断了请假时长完全包含上班时长、上班时长完全包含请假时长这两种情况
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) < 1) {
                Date actualLeaveTime = null;
                leaveStartList.add(startDate);
                // 无需弹性时间
                actualLeaveTime = dayNormalPunchTimeDTO.getDayPunchEndTime();
                leaveEndList.add(dayNormalPunchTimeDTO.getDayPunchEndTime());

                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    continue;
                }
                //特殊处理，注意请假时间和休息时间的交际
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    continue;
                }
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    minutes = minutes - (int) DateUtil.between(startDate, dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                    continue;
                }
                minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                continue;
            }

            //右边交集：班次开始时间大于请假的开始时间，则一定是【8:00 9:00 16:00 18:00】的情况：因为上面已经判断了请假时长完全包含上班时长、上班时长完全包含请假时长、上班时长小于等于请假开始时间的这三种情况。
            // 这边无需做特殊处理了，因为请假开始时间如果小于班次时间，则一定是取班次时间作为请假开始时间
            leaveStartList.add(dayNormalPunchTimeDTO.getDayPunchStartTime());
            leaveEndList.add(endDate);
            if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            //特殊处理，注意请假时间和休息时间的交际
            if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) < 1) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
            minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
        }

        if (CollectionUtils.isNotEmpty(leaveStartList)) {
            Collections.sort(leaveStartList);
            Collections.sort(leaveEndList);
            String dayShiftInfo = DateUtil.format(leaveStartList.get(0), "HH:mm") + "-" + DateUtil.format(leaveEndList.get(leaveEndList.size() - 1), "HH:mm");
            leaveInfoList.add(dayShiftInfo);
        }
        if (minutes >= (punchClassConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES).intValue())) {
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), dayShiftInfoList, leaveInfoList, restInfoList, dayDurationInfoDTOList);
            return;
        }
        //不足一天
        hours = minutes / (BusinessConstant.MINUTES.intValue());
        minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), punchClassConfigDO.getLegalWorkingHours(), dayShiftInfoList, leaveInfoList, restInfoList, dayDurationInfoDTOList);

    }

    /**
     * 用户请假/外勤周期范围校验
     */
    private void leaveDataConfirmCycleCheck(Date nowDate, HrmsSalaryConfigDO hrmsSalaryConfigDO, Date startDate) {
        Integer leaveDataConfirmCycle = hrmsProperties.getApproval().getLeaveDataConfirmCycle();
        //获取请假可请周期
        AttendanceDayCycleDTO attendanceDayCycleDTO = hrmsAttendanceBaseService.getUserAttendanceDayCycle(Long.valueOf(DateUtil.format(nowDate, "yyyyMMdd")), hrmsSalaryConfigDO);
        if (attendanceDayCycleDTO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_SALARY_CONFIG.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_SALARY_CONFIG.getDesc()));
        }
        //请假最早可请的日期
        Long confirmDayId = Long.valueOf(DateUtil.format(attendanceDayCycleDTO.getAttendanceStartDate(), "yyyyMMdd"));
        Long nowDayId = Long.valueOf(DateUtil.format(nowDate, "yyyyMMdd"));
        Long checkDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(attendanceDayCycleDTO.getAttendanceStartDate(), leaveDataConfirmCycle), "yyyyMMdd"));
        //看看有没有超过确认周期
        if (nowDayId.compareTo(checkDayId) < 1) {
            //confirmDayId = Long.valueOf(DateUtil.format(DateUtil.offset(attendanceDayCycleDTO.getAttendanceStartDate(), DateField.MONTH, -1), "yyyyMMdd"));
            int offSetDay = -leaveDataConfirmCycle;
            confirmDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(attendanceDayCycleDTO.getAttendanceStartDate(), offSetDay), "yyyyMMdd"));
        }
        Long startDayId = Long.valueOf(DateUtil.format(startDate, "yyyyMMdd"));
        if (startDayId.compareTo(confirmDayId) < 0) {
            Date attendanceStartDate = attendanceDayCycleDTO.getAttendanceStartDate();
            Date attendanceEndDate = attendanceDayCycleDTO.getAttendanceEndDate();
            Long attendanceStartDay = Long.valueOf(DateUtil.format(attendanceStartDate, "yyyyMMdd"));
            Long attendanceEndDay = Long.valueOf(DateUtil.format(attendanceEndDate, "yyyyMMdd"));
            //做个特殊兜底，一年之内的假
            for (int i = 0; i < 12; i++) {
                attendanceStartDate = DateUtil.offset(attendanceStartDate, DateField.MONTH, -1);
                attendanceEndDate = DateUtil.offset(attendanceEndDate, DateField.MONTH, -1);
                attendanceStartDay = Long.valueOf(DateUtil.format(attendanceStartDate, "yyyyMMdd"));
                attendanceEndDay = Long.valueOf(DateUtil.format(attendanceEndDate, "yyyyMMdd"));
                if (startDayId.compareTo(attendanceStartDay) > -1 && startDayId.compareTo(attendanceEndDay) < 1) {
                    break;
                }
            }
            throw BusinessException.get(HrmsErrorCodeEnums.EXTENDED_LEAVE_PERIOD.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.EXTENDED_LEAVE_PERIOD.getDesc(), attendanceStartDay.toString(), attendanceEndDay.toString()));

           /* String attendanceStartDay = DateUtil.format(attendanceDayCycleDTO.getAttendanceStartDate(), "yyyyMMdd");
            String attendanceEndDay = DateUtil.format(attendanceDayCycleDTO.getAttendanceEndDate(), "yyyyMMdd");
            throw BusinessException.get(HrmsErrorCodeEnums.EXTENDED_LEAVE_PERIOD.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.EXTENDED_LEAVE_PERIOD.getDesc(), attendanceStartDay, attendanceEndDay));
       */
        }

       /* //请假最早可请的日期
        Date confirmDate = attendanceDayCycleDTO.getAttendanceStartDate();
        //看看有没有超过确认周期
        if (DateUtil.offsetDay(confirmDate, leaveDataConfirmCycle - 1).compareTo(nowDate) > -1) {
            confirmDate = DateUtil.offset(confirmDate, DateField.MONTH, -1);
        }
        //比较天，无需小时
        if (DateUtil.compare(DateUtil.beginOfDay(startDate), DateUtil.beginOfDay(confirmDate)) < 0) {
            String attendanceStartDay = DateUtil.format(attendanceDayCycleDTO.getAttendanceStartDate(), "yyyyMMdd");
            String attendanceEndDay = DateUtil.format(attendanceDayCycleDTO.getAttendanceEndDate(), "yyyyMMdd");
            throw BusinessException.get(HrmsErrorCodeEnums.EXTENDED_LEAVE_PERIOD.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.EXTENDED_LEAVE_PERIOD.getDesc(), attendanceStartDay, attendanceEndDay));
        }*/
    }

    /**
     * 确认周期校验
     *
     * @param nowDate                     当前时间
     * @param hrmsAttendanceCycleConfigDO 考勤周期配置
     * @param specificTime                指定日期
     * @return Boolean
     */
    private void confirmCycleCheck(Date nowDate, HrmsAttendanceCycleConfigDO hrmsAttendanceCycleConfigDO, Date specificTime) {
        // 区分月维度、周维度,获取请假可请范围
        Long specificTimeDayId = Long.valueOf(DateUtil.format(specificTime, "yyyyMMdd"));
        Long nowDayId = Long.valueOf(DateUtil.format(nowDate, "yyyyMMdd"));

        Integer cycleType = hrmsAttendanceCycleConfigDO.getCycleType();
        String cycleEnd = hrmsAttendanceCycleConfigDO.getCycleEnd();
        Integer abnormalExpired = hrmsAttendanceCycleConfigDO.getAbnormalExpired();
        //获取请假可请周期
        AttendanceDayCycleDTO attendanceDayCycleDTO = hrmsAttendanceBaseService.getUserAttendanceCycleConfigDay(Long.valueOf(DateUtil.format(nowDate, "yyyyMMdd")), hrmsAttendanceCycleConfigDO);
        AttendanceDayCycleDTO attendanceDayCycle = hrmsAttendanceBaseService.getUserAttendanceCycleConfigDay(specificTimeDayId, hrmsAttendanceCycleConfigDO);
        if (ObjectUtil.isNull(attendanceDayCycleDTO) || ObjectUtil.isNull(attendanceDayCycle)) {
            //throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_SALARY_CONFIG.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_SALARY_CONFIG.getDesc()));
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ATTENDANCE_CYCLE_CONFIG_USER_NOT_HAVE);
        }
        log.info("confirmCycleCheck attendanceDayCycleDTO:{}", JSON.toJSONString(attendanceDayCycleDTO));
        String oldStartDate = DateUtil.format(attendanceDayCycle.getAttendanceStartDate(), DatePattern.NORM_DATE_PATTERN);
        String oldEndDate = DateUtil.format(attendanceDayCycle.getAttendanceEndDate(), DatePattern.NORM_DATE_PATTERN);

        CycleTypeEnum cycleTypeEnum = null;
        if (ObjectUtil.equal(cycleType, AttendanceCycleTypeEnum.WEEK.getType())) {
            cycleTypeEnum = CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.WEEK.name());
            abnormalExpired = -abnormalExpired + 1;
        } else {
            cycleTypeEnum = CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.MONTH.name());
            abnormalExpired = -abnormalExpired;
        }

        if (ObjectUtil.isNull(cycleTypeEnum)) {
            log.info("cycle type is null");
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ATTENDANCE_CYCLE_CONFIG_GET_CYCLE_TYPE_ERROR);
        }
        log.info("confirmCycleCheck cycleTypeEnum:{}", JSON.toJSONString(cycleTypeEnum));

        // 周期偏移日期
        Date offsetCycleEndDate = cycleTypeEnum.getCycleDate(nowDate, cycleEnd, abnormalExpired);
        Long offsetCycleEndDayId = Long.valueOf(DateUtil.format(offsetCycleEndDate, "yyyyMMdd"));

        log.info("confirmCycleCheck specificTimeDayId:{} offsetCycleEndDayId:{}", specificTimeDayId, offsetCycleEndDayId);

        // 指定时间小于等于周期偏移日期。需要处理掉
        if (specificTimeDayId.compareTo(offsetCycleEndDayId) <= 0) {
            throw BusinessException.get(HrmsErrorCodeEnums.EXTENDED_LEAVE_PERIOD.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.EXTENDED_LEAVE_PERIOD.getDesc(), oldStartDate, oldEndDate));
        }

    }


    private void outOfOfficeDataAddBuild(OutOfOfficeAddParam param, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList, HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        hrmsApplicationFormDO.setId(IdWorkerUtil.getId());
        hrmsApplicationFormDO.setApplyUserId(param.getApplyUserId());
        hrmsApplicationFormDO.setUserId(param.getUserId());
        hrmsApplicationFormDO.setUserCode(param.getUserCode());
        hrmsApplicationFormDO.setUserName(param.getUserName());
        hrmsApplicationFormDO.setDeptId(param.getDeptId());
        hrmsApplicationFormDO.setPostId(param.getPostId());
        hrmsApplicationFormDO.setCountry(param.getCountry());
        hrmsApplicationFormDO.setOriginCountry(param.getOriginCountry());
        hrmsApplicationFormDO.setIsWarehouseStaff(param.getIsWarehouseStaff());
        hrmsApplicationFormDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.OUT_OF_OFFICE));
        hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode());
        if (StringUtils.isBlank(hrmsApplicationFormDO.getFormStatus())) {
            //为暂存
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);


        if (param.getOutOfOfficeStartDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode(), DateUtil.format(param.getOutOfOfficeStartDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getOutOfOfficeEndDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode(), DateUtil.format(param.getOutOfOfficeEndDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
        }
        if (CollectionUtils.isNotEmpty(param.getDayDurationInfoDTOList())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode(), JSON.toJSONString(param.getDayDurationInfoDTOList())));
        }

        //默认是没有被撤销
        hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));

        //把异常ID也关联下
        if (param.getAbnormalId() != null) {
            HrmsApplicationFormRelationDO relationDO = new HrmsApplicationFormRelationDO();
            relationDO.setId(IdWorkerUtil.getId());
            relationDO.setFormId(hrmsApplicationFormDO.getId());
            relationDO.setRelationId(param.getAbnormalId());
            relationDO.setRelationType(ApplicationRelationTypeEnum.ABNORMAL.getCode());
            BaseDOUtil.fillDOInsert(relationDO);
            hrmsApplicationFormRelationDOList.add(relationDO);

            hrmsEmployeeAbnormalOperationRecordDO.setId(IdWorkerUtil.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setFormId(hrmsApplicationFormDO.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setAbnormalId(param.getAbnormalId());
            hrmsEmployeeAbnormalOperationRecordDO.setOperationType(AttendanceAbnormalOperationTypeEnum.OUT_OF_OFFICE.getCode());
            BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);
        }

        if (abnormalAttendanceDO != null) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }
    }

    private void outOfOfficeDataUpdateBuild(OutOfOfficeAddParam param, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList, HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        hrmsApplicationFormDO.setApplyUserId(param.getApplyUserId());
        hrmsApplicationFormDO.setUserId(param.getUserId());
        hrmsApplicationFormDO.setUserCode(param.getUserCode());
        hrmsApplicationFormDO.setUserName(param.getUserName());
        hrmsApplicationFormDO.setDeptId(param.getDeptId());
        hrmsApplicationFormDO.setPostId(param.getPostId());
        hrmsApplicationFormDO.setCountry(param.getCountry());
        hrmsApplicationFormDO.setOriginCountry(param.getOriginCountry());
        hrmsApplicationFormDO.setIsWarehouseStaff(param.getIsWarehouseStaff());
        hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode());
        if (StringUtils.isBlank(hrmsApplicationFormDO.getFormStatus())) {
            //为暂存
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOUpdate(hrmsApplicationFormDO);

        if (param.getOutOfOfficeStartDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode(), DateUtil.format(param.getOutOfOfficeStartDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getOutOfOfficeEndDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode(), DateUtil.format(param.getOutOfOfficeEndDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
        }
        if (CollectionUtils.isNotEmpty(param.getDayDurationInfoDTOList())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode(), JSON.toJSONString(param.getDayDurationInfoDTOList())));
        }

        //默认是没有被撤销
        hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));


        //把异常ID也关联下
        if (param.getAbnormalId() != null) {
            HrmsApplicationFormRelationDO relationDO = new HrmsApplicationFormRelationDO();
            relationDO.setId(IdWorkerUtil.getId());
            relationDO.setFormId(hrmsApplicationFormDO.getId());
            relationDO.setRelationId(param.getAbnormalId());
            relationDO.setRelationType(ApplicationRelationTypeEnum.ABNORMAL.getCode());
            BaseDOUtil.fillDOInsert(relationDO);
            hrmsApplicationFormRelationDOList.add(relationDO);

            hrmsEmployeeAbnormalOperationRecordDO.setId(IdWorkerUtil.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setFormId(hrmsApplicationFormDO.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setAbnormalId(param.getAbnormalId());
            hrmsEmployeeAbnormalOperationRecordDO.setOperationType(AttendanceAbnormalOperationTypeEnum.OUT_OF_OFFICE.getCode());
            BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);
        }

        if (abnormalAttendanceDO != null) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }
    }

    private void leaveRevokeDataAddBuild(LeaveRevokeAddParam param, HrmsApplicationFormDetailBO formDetailBO, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOS, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        HrmsApplicationFormDO leavFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOS();
        hrmsApplicationFormDO.setId(IdWorkerUtil.getId());
        hrmsApplicationFormDO.setApplyUserId(param.getApplyUserId());
        hrmsApplicationFormDO.setUserId(leavFormDO.getUserId());
        hrmsApplicationFormDO.setUserCode(leavFormDO.getUserCode());
        hrmsApplicationFormDO.setUserName(leavFormDO.getUserName());
        hrmsApplicationFormDO.setDeptId(leavFormDO.getDeptId());
        hrmsApplicationFormDO.setPostId(leavFormDO.getPostId());
        hrmsApplicationFormDO.setCountry(leavFormDO.getCountry());
        hrmsApplicationFormDO.setOriginCountry(leavFormDO.getOriginCountry());
        hrmsApplicationFormDO.setIsWarehouseStaff(leavFormDO.getIsWarehouseStaff());
        hrmsApplicationFormDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.LEAVE_REVOKE));
        hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.LEAVE_REVOKE.getCode());
        hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode());
        BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);

        for (HrmsApplicationFormAttrDO leaveAttrDO : leaveAttrDOS) {
            HrmsApplicationFormAttrDO revokeAttrDO = new HrmsApplicationFormAttrDO();
            revokeAttrDO.setId(IdWorkerUtil.getId());
            revokeAttrDO.setFormId(hrmsApplicationFormDO.getId());
            revokeAttrDO.setAttrKey(leaveAttrDO.getAttrKey());
            revokeAttrDO.setAttrValue(leaveAttrDO.getAttrValue());
            this.fillDOInsert(revokeAttrDO);
            hrmsApplicationFormAttrDOArrayList.add(revokeAttrDO);
        }
        if (StringUtils.isNotBlank(param.getRevokeReason())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.revokeReason.getLowerCode(), param.getRevokeReason()));
        }

        HrmsApplicationFormRelationDO relationDO = new HrmsApplicationFormRelationDO();
        relationDO.setId(IdWorkerUtil.getId());
        relationDO.setFormId(hrmsApplicationFormDO.getId());
        relationDO.setRelationId(leavFormDO.getId());
        relationDO.setRelationType(ApplicationRelationTypeEnum.APPLICATION_FORM.getCode());
        BaseDOUtil.fillDOInsert(relationDO);
        hrmsApplicationFormRelationDOS.add(relationDO);

    }

    private void outOfOfficeRevokeDataAddBuild(OutOfOfficeRevokeAddParam param, HrmsApplicationFormDetailBO formDetailBO, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOS, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        HrmsApplicationFormDO outOfOfficeFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> outOfOfficeAttrDOS = formDetailBO.getAttrDOS();
        hrmsApplicationFormDO.setId(IdWorkerUtil.getId());
        hrmsApplicationFormDO.setApplyUserId(param.getApplyUserId());
        hrmsApplicationFormDO.setUserId(outOfOfficeFormDO.getUserId());
        hrmsApplicationFormDO.setUserCode(outOfOfficeFormDO.getUserCode());
        hrmsApplicationFormDO.setUserName(outOfOfficeFormDO.getUserName());
        hrmsApplicationFormDO.setDeptId(outOfOfficeFormDO.getDeptId());
        hrmsApplicationFormDO.setPostId(outOfOfficeFormDO.getPostId());
        hrmsApplicationFormDO.setCountry(outOfOfficeFormDO.getCountry());
        hrmsApplicationFormDO.setOriginCountry(outOfOfficeFormDO.getOriginCountry());
        hrmsApplicationFormDO.setIsWarehouseStaff(outOfOfficeFormDO.getIsWarehouseStaff());
        hrmsApplicationFormDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.OUT_OF_OFFICE_REVOKE));
        hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE_REVOKE.getCode());
        hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode());
        BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);

        for (HrmsApplicationFormAttrDO outOfOfficeAttrDO : outOfOfficeAttrDOS) {
            HrmsApplicationFormAttrDO revokeAttrDO = new HrmsApplicationFormAttrDO();
            revokeAttrDO.setId(IdWorkerUtil.getId());
            revokeAttrDO.setFormId(hrmsApplicationFormDO.getId());
            revokeAttrDO.setAttrKey(outOfOfficeAttrDO.getAttrKey());
            revokeAttrDO.setAttrValue(outOfOfficeAttrDO.getAttrValue());
            this.fillDOInsert(revokeAttrDO);
            hrmsApplicationFormAttrDOArrayList.add(revokeAttrDO);
        }
        if (StringUtils.isNotBlank(param.getRevokeReason())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.revokeReason.getLowerCode(), param.getRevokeReason()));
        }

        HrmsApplicationFormRelationDO relationDO = new HrmsApplicationFormRelationDO();
        relationDO.setId(IdWorkerUtil.getId());
        relationDO.setFormId(hrmsApplicationFormDO.getId());
        relationDO.setRelationId(outOfOfficeFormDO.getId());
        relationDO.setRelationType(ApplicationRelationTypeEnum.APPLICATION_FORM.getCode());
        BaseDOUtil.fillDOInsert(relationDO);
        hrmsApplicationFormRelationDOS.add(relationDO);
    }

    private void reissueCardRevokeDataAddBuild(ReissueCardRevokeAddParam param, HrmsApplicationFormDetailBO formDetailBO, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOS, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        HrmsApplicationFormDO outOfOfficeFormDO = formDetailBO.getFormDO();
        List<HrmsApplicationFormAttrDO> outOfOfficeAttrDOS = formDetailBO.getAttrDOS();
        hrmsApplicationFormDO.setId(IdWorkerUtil.getId());
        hrmsApplicationFormDO.setApplyUserId(param.getApplyUserId());
        hrmsApplicationFormDO.setUserId(outOfOfficeFormDO.getUserId());
        hrmsApplicationFormDO.setUserCode(outOfOfficeFormDO.getUserCode());
        hrmsApplicationFormDO.setUserName(outOfOfficeFormDO.getUserName());
        hrmsApplicationFormDO.setDeptId(outOfOfficeFormDO.getDeptId());
        hrmsApplicationFormDO.setPostId(outOfOfficeFormDO.getPostId());
        hrmsApplicationFormDO.setCountry(outOfOfficeFormDO.getCountry());
        hrmsApplicationFormDO.setOriginCountry(outOfOfficeFormDO.getOriginCountry());
        hrmsApplicationFormDO.setIsWarehouseStaff(outOfOfficeFormDO.getIsWarehouseStaff());
        hrmsApplicationFormDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.REISSUE_CARD_REVOKE));
        hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.REISSUE_CARD_REVOKE.getCode());
        hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode());
        BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);

        for (HrmsApplicationFormAttrDO outOfOfficeAttrDO : outOfOfficeAttrDOS) {
            HrmsApplicationFormAttrDO revokeAttrDO = new HrmsApplicationFormAttrDO();
            revokeAttrDO.setId(IdWorkerUtil.getId());
            revokeAttrDO.setFormId(hrmsApplicationFormDO.getId());
            revokeAttrDO.setAttrKey(outOfOfficeAttrDO.getAttrKey());
            revokeAttrDO.setAttrValue(outOfOfficeAttrDO.getAttrValue());
            this.fillDOInsert(revokeAttrDO);
            hrmsApplicationFormAttrDOArrayList.add(revokeAttrDO);
        }
        if (StringUtils.isNotBlank(param.getRevokeReason())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.revokeReason.getLowerCode(), param.getRevokeReason()));
        }
        HrmsApplicationFormRelationDO relationDO = new HrmsApplicationFormRelationDO();
        relationDO.setId(IdWorkerUtil.getId());
        relationDO.setFormId(hrmsApplicationFormDO.getId());
        relationDO.setRelationId(outOfOfficeFormDO.getId());
        relationDO.setRelationType(ApplicationRelationTypeEnum.APPLICATION_FORM.getCode());
        BaseDOUtil.fillDOInsert(relationDO);
        hrmsApplicationFormRelationDOS.add(relationDO);
    }

    private void leaveDataAddBuild(LeaveAddParam param, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOArrayList, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList, HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        hrmsApplicationFormDO.setId(IdWorkerUtil.getId());
        hrmsApplicationFormDO.setApplyUserId(param.getApplyUserId());
        hrmsApplicationFormDO.setUserId(param.getUserId());
        hrmsApplicationFormDO.setUserCode(param.getUserCode());
        hrmsApplicationFormDO.setUserName(param.getUserName());
        hrmsApplicationFormDO.setDeptId(param.getDeptId());
        hrmsApplicationFormDO.setPostId(param.getPostId());
        hrmsApplicationFormDO.setCountry(param.getCountry());
        hrmsApplicationFormDO.setOriginCountry(param.getOriginCountry());
        hrmsApplicationFormDO.setIsWarehouseStaff(param.getIsWarehouseStaff());
        hrmsApplicationFormDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.LEAVE));
        hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.LEAVE.getCode());
        if (StringUtils.isBlank(hrmsApplicationFormDO.getFormStatus())) {
            //为暂存
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);
        if (Objects.nonNull(param.getConfigId())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.configID.getLowerCode(), String.valueOf(param.getConfigId())));
        }
        if (StringUtils.isNotBlank(param.getLeaveName())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode(), param.getLeaveName()));
        }
        if (StringUtils.isNotBlank(param.getLeaveShortName())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveShortName.getLowerCode(), param.getLeaveShortName()));
        }
        if (StringUtils.isNotBlank(param.getLeaveUnit())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveUnit.getLowerCode(), param.getLeaveUnit()));
        }
        if (param.getLeaveStartDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode(), DateUtil.format(param.getLeaveStartDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getLeaveEndDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode(), DateUtil.format(param.getLeaveEndDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getLeaveResidueMinutes() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveResidueMinutes.getLowerCode(), param.getLeaveResidueMinutes().toString()));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
        }
        if (CollectionUtils.isNotEmpty(param.getDayDurationInfoDTOList())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode(), JSON.toJSONString(param.getDayDurationInfoDTOList())));
        }

        //默认是没有被撤销
        hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));

        //把异常ID也关联下
        if (param.getAbnormalId() != null) {
            HrmsApplicationFormRelationDO relationDO = new HrmsApplicationFormRelationDO();
            relationDO.setId(IdWorkerUtil.getId());
            relationDO.setFormId(hrmsApplicationFormDO.getId());
            relationDO.setRelationId(param.getAbnormalId());
            relationDO.setRelationType(ApplicationRelationTypeEnum.ABNORMAL.getCode());
            BaseDOUtil.fillDOInsert(relationDO);
            hrmsApplicationFormRelationDOArrayList.add(relationDO);

            hrmsEmployeeAbnormalOperationRecordDO.setId(IdWorkerUtil.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setFormId(hrmsApplicationFormDO.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setAbnormalId(param.getAbnormalId());
            hrmsEmployeeAbnormalOperationRecordDO.setOperationType(AttendanceAbnormalOperationTypeEnum.LEAVE.getCode());
            BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);
        }

        if (abnormalAttendanceDO != null) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }

    }

    private void leaveDataUpdateBuild(LeaveAddParam param, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOArrayList, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList, HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        hrmsApplicationFormDO.setApplyUserId(param.getApplyUserId());
        hrmsApplicationFormDO.setUserId(param.getUserId());
        hrmsApplicationFormDO.setUserCode(param.getUserCode());
        hrmsApplicationFormDO.setUserName(param.getUserName());
        hrmsApplicationFormDO.setDeptId(param.getDeptId());
        hrmsApplicationFormDO.setPostId(param.getPostId());
        hrmsApplicationFormDO.setCountry(param.getCountry());
        hrmsApplicationFormDO.setOriginCountry(param.getOriginCountry());
        hrmsApplicationFormDO.setIsWarehouseStaff(param.getIsWarehouseStaff());
        hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.LEAVE.getCode());
        if (StringUtils.isBlank(hrmsApplicationFormDO.getFormStatus())) {
            //为暂存
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOUpdate(hrmsApplicationFormDO);

        if (StringUtils.isNotBlank(param.getLeaveName())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode(), param.getLeaveName()));
        }
        if (Objects.nonNull(param.getConfigId())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.configID.getLowerCode(), String.valueOf(param.getConfigId())));
        }
        if (StringUtils.isNotBlank(param.getLeaveShortName())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveShortName.getLowerCode(), param.getLeaveShortName()));
        }
        if (StringUtils.isNotBlank(param.getLeaveUnit())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveUnit.getLowerCode(), param.getLeaveUnit()));
        }
        if (param.getLeaveResidueMinutes() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveResidueMinutes.getLowerCode(), param.getLeaveResidueMinutes().toString()));
        }
        if (param.getLeaveStartDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode(), DateUtil.format(param.getLeaveStartDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getLeaveEndDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode(), DateUtil.format(param.getLeaveEndDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
        }
        if (CollectionUtils.isNotEmpty(param.getDayDurationInfoDTOList())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode(), JSON.toJSONString(param.getDayDurationInfoDTOList())));
        }
        //默认是没有被撤销
        hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));

        //把异常ID也关联下
        if (param.getAbnormalId() != null) {
            HrmsApplicationFormRelationDO relationDO = new HrmsApplicationFormRelationDO();
            relationDO.setId(IdWorkerUtil.getId());
            relationDO.setFormId(hrmsApplicationFormDO.getId());
            relationDO.setRelationId(param.getAbnormalId());
            relationDO.setRelationType(ApplicationRelationTypeEnum.ABNORMAL.getCode());
            BaseDOUtil.fillDOInsert(relationDO);
            hrmsApplicationFormRelationDOArrayList.add(relationDO);

            hrmsEmployeeAbnormalOperationRecordDO.setId(IdWorkerUtil.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setFormId(hrmsApplicationFormDO.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setAbnormalId(param.getAbnormalId());
            hrmsEmployeeAbnormalOperationRecordDO.setOperationType(AttendanceAbnormalOperationTypeEnum.LEAVE.getCode());
            BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);
        }

        if (abnormalAttendanceDO != null) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }
    }

    private void reissueCardDataAddBuild(ReissueCardAddParam param, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList, HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        hrmsApplicationFormDO.setId(IdWorkerUtil.getId());
        hrmsApplicationFormDO.setApplyUserId(param.getApplyUserId());
        hrmsApplicationFormDO.setUserId(param.getUserId());
        hrmsApplicationFormDO.setUserCode(param.getUserCode());
        hrmsApplicationFormDO.setUserName(param.getUserName());
        hrmsApplicationFormDO.setDeptId(param.getDeptId());
        hrmsApplicationFormDO.setPostId(param.getPostId());
        hrmsApplicationFormDO.setCountry(param.getCountry());
        hrmsApplicationFormDO.setOriginCountry(param.getOriginCountry());
        hrmsApplicationFormDO.setIsWarehouseStaff(param.getIsWarehouseStaff());
        hrmsApplicationFormDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.REISSUE_CARD));
        hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.REISSUE_CARD.getCode());
        if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(param.getCountry())
                && ObjectUtil.equal(param.getIsWarehouseStaff(), BusinessConstant.Y)) {
            // 如果是仓内
            hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode());
        }
        if (StringUtils.isBlank(hrmsApplicationFormDO.getFormStatus())) {
            //为暂存
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);

        if (param.getReissueCardDayId() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode(), param.getReissueCardDayId().toString()));
        }
        if (StringUtils.isNotBlank(param.getReissueCardType())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardType.getLowerCode(), param.getReissueCardType()));
        }
        if (param.getResidueReissueCardCount() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.residueReissueCardCount.getLowerCode(), param.getResidueReissueCardCount().toString()));
        }
        if (param.getAttendanceStartDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.attendanceStartDate.getLowerCode(), DateUtil.format(param.getAttendanceStartDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getAttendanceEndDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.attendanceEndDate.getLowerCode(), DateUtil.format(param.getAttendanceEndDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getActualPunchTime() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.actualPunchTime.getLowerCode(), DateUtil.format(param.getActualPunchTime(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getCorrectPunchTime() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode(), DateUtil.format(param.getCorrectPunchTime(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (StringUtils.isNotBlank(param.getPunchConfigClassItemInfo())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.punchConfigClassItemInfo.getLowerCode(), param.getPunchConfigClassItemInfo()));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
        }
        if (Objects.nonNull(param.getEarlyPunchTime())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.earlyPunchTime.getLowerCode(), DateUtil.format(param.getEarlyPunchTime(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (Objects.nonNull(param.getLatePunchTime())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.latePunchTime.getLowerCode(), DateUtil.format(param.getLatePunchTime(), "yyyy-MM-dd HH:mm:ss")));
        }

        //默认是没有被撤销
        hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));

        //把异常ID也关联下
        if (param.getAbnormalId() != null) {
            HrmsApplicationFormRelationDO relationDO = new HrmsApplicationFormRelationDO();
            relationDO.setId(IdWorkerUtil.getId());
            relationDO.setFormId(hrmsApplicationFormDO.getId());
            relationDO.setRelationId(param.getAbnormalId());
            relationDO.setRelationType(ApplicationRelationTypeEnum.ABNORMAL.getCode());
            BaseDOUtil.fillDOInsert(relationDO);
            hrmsApplicationFormRelationDOList.add(relationDO);

            hrmsEmployeeAbnormalOperationRecordDO.setId(IdWorkerUtil.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setFormId(hrmsApplicationFormDO.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setAbnormalId(param.getAbnormalId());
            hrmsEmployeeAbnormalOperationRecordDO.setOperationType(AttendanceAbnormalOperationTypeEnum.REISSUE_CARD.getCode());
            BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);
        }

        if (abnormalAttendanceDO != null && param.getOperationType() == 1) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }

        //暂存不需要扣次数
        if (param.getOperationType() == 0) {
            return;
        }
        HrmsAttendanceUserCardConfigDO userCardConfigDO = param.getUserCardConfigDO();
        userCardConfigDO.setUsedCardCount(userCardConfigDO.getUsedCardCount() + 1);
        BaseDOUtil.fillDOUpdate(userCardConfigDO);
    }

    private void reissueCardDataUpdateBuild(ReissueCardAddParam param, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList, HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        hrmsApplicationFormDO.setApplyUserId(param.getApplyUserId());
        hrmsApplicationFormDO.setUserId(param.getUserId());
        hrmsApplicationFormDO.setUserCode(param.getUserCode());
        hrmsApplicationFormDO.setUserName(param.getUserName());
        hrmsApplicationFormDO.setDeptId(param.getDeptId());
        hrmsApplicationFormDO.setPostId(param.getPostId());
        hrmsApplicationFormDO.setCountry(param.getCountry());
        hrmsApplicationFormDO.setOriginCountry(param.getOriginCountry());
        hrmsApplicationFormDO.setIsWarehouseStaff(param.getIsWarehouseStaff());
        hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.REISSUE_CARD.getCode());
        if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(param.getCountry())
                && ObjectUtil.equal(param.getIsWarehouseStaff(), BusinessConstant.Y)) {
            // 如果是仓内
            hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode());
        }
        if (StringUtils.isBlank(hrmsApplicationFormDO.getFormStatus())) {
            //为暂存
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOUpdate(hrmsApplicationFormDO);

        if (param.getReissueCardDayId() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode(), param.getReissueCardDayId().toString()));
        }
        if (StringUtils.isNotBlank(param.getReissueCardType())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardType.getLowerCode(), param.getReissueCardType()));
        }
        if (param.getResidueReissueCardCount() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.residueReissueCardCount.getLowerCode(), param.getResidueReissueCardCount().toString()));
        }
        if (param.getAttendanceStartDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.attendanceStartDate.getLowerCode(), DateUtil.format(param.getAttendanceStartDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getAttendanceEndDate() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.attendanceEndDate.getLowerCode(), DateUtil.format(param.getAttendanceEndDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getActualPunchTime() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.actualPunchTime.getLowerCode(), DateUtil.format(param.getActualPunchTime(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getCorrectPunchTime() != null) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode(), DateUtil.format(param.getCorrectPunchTime(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (StringUtils.isNotBlank(param.getPunchConfigClassItemInfo())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.punchConfigClassItemInfo.getLowerCode(), param.getPunchConfigClassItemInfo()));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
        }
        if (Objects.nonNull(param.getEarlyPunchTime())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.earlyPunchTime.getLowerCode(), JSON.toJSONString(param.getEarlyPunchTime())));
        }
        if (Objects.nonNull(param.getLatePunchTime())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.latePunchTime.getLowerCode(), JSON.toJSONString(param.getLatePunchTime())));
        }

        //默认是没有被撤销
        hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));


        //把异常ID也关联下
        if (param.getAbnormalId() != null) {
            HrmsApplicationFormRelationDO relationDO = new HrmsApplicationFormRelationDO();
            relationDO.setId(IdWorkerUtil.getId());
            relationDO.setFormId(hrmsApplicationFormDO.getId());
            relationDO.setRelationId(param.getAbnormalId());
            relationDO.setRelationType(ApplicationRelationTypeEnum.ABNORMAL.getCode());
            BaseDOUtil.fillDOInsert(relationDO);
            hrmsApplicationFormRelationDOList.add(relationDO);

            hrmsEmployeeAbnormalOperationRecordDO.setId(IdWorkerUtil.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setFormId(hrmsApplicationFormDO.getId());
            hrmsEmployeeAbnormalOperationRecordDO.setAbnormalId(param.getAbnormalId());
            hrmsEmployeeAbnormalOperationRecordDO.setOperationType(AttendanceAbnormalOperationTypeEnum.REISSUE_CARD.getCode());
            BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);
        }

        if (abnormalAttendanceDO != null && param.getOperationType() == 1) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }

        //暂存不需要扣次数
        if (param.getOperationType() == 0) {
            return;
        }
        HrmsAttendanceUserCardConfigDO userCardConfigDO = param.getUserCardConfigDO();
        userCardConfigDO.setUsedCardCount(userCardConfigDO.getUsedCardCount() + 1);
        BaseDOUtil.fillDOUpdate(userCardConfigDO);
    }

    private void addDurationDateBuild(AddDurationParam param, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> hrmsApplicationFormRelationDOList, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList, HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        hrmsApplicationFormDO.setId(IdWorkerUtil.getId());
        hrmsApplicationFormDO.setApplyUserId(param.getApplyUserId());
        hrmsApplicationFormDO.setUserId(param.getUserId());
        hrmsApplicationFormDO.setUserCode(param.getUserCode());
        hrmsApplicationFormDO.setUserName(param.getUserName());
        hrmsApplicationFormDO.setDeptId(param.getDeptId());
        hrmsApplicationFormDO.setPostId(param.getPostId());
        hrmsApplicationFormDO.setCountry(param.getCountry());
        hrmsApplicationFormDO.setOriginCountry(param.getOriginCountry());
        hrmsApplicationFormDO.setIsWarehouseStaff(param.getIsWarehouseStaff());
        hrmsApplicationFormDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.ADD_DURATION));
        hrmsApplicationFormDO.setFormType(HrAttendanceApplicationFormTypeEnum.ADD_DURATION.getCode());
        if (StringUtils.isBlank(hrmsApplicationFormDO.getFormStatus())) {
            //为暂存
            hrmsApplicationFormDO.setFormStatus(HrAttendanceApplicationFormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);

        hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.abnormalType.getLowerCode(), AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode()));
        hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.abnormalDate.getLowerCode(), DateUtil.formatDate(abnormalAttendanceDO.getDate())));

        if (Objects.nonNull(param.getActualAttendanceTime())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.actualAttendanceTime.getLowerCode(), param.getActualAttendanceTime().toString()));
        }
        if (Objects.nonNull(param.getActualWorkingHours())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.actualWorkingHours.getLowerCode(), param.getActualWorkingHours().toString()));
        }
        if (Objects.nonNull(param.getNewActualAttendanceTime())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.newActualAttendanceTime.getLowerCode(), param.getNewActualAttendanceTime().toString()));
        }
        if (Objects.nonNull(param.getNewActualWorkingHours())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.newActualWorkingHours.getLowerCode(), param.getNewActualWorkingHours().toString()));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
        }

        //默认是没有被撤销
        hrmsApplicationFormAttrDOArrayList.add(insertAttrDOBuild(hrmsApplicationFormDO.getId(), HrAttendanceApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));

        //把异常ID也关联下
        HrmsApplicationFormRelationDO relationDO = new HrmsApplicationFormRelationDO();
        relationDO.setId(IdWorkerUtil.getId());
        relationDO.setFormId(hrmsApplicationFormDO.getId());
        relationDO.setRelationId(param.getAbnormalId());
        relationDO.setRelationType(ApplicationRelationTypeEnum.ABNORMAL.getCode());
        BaseDOUtil.fillDOInsert(relationDO);
        hrmsApplicationFormRelationDOList.add(relationDO);

        hrmsEmployeeAbnormalOperationRecordDO.setId(IdWorkerUtil.getId());
        hrmsEmployeeAbnormalOperationRecordDO.setFormId(hrmsApplicationFormDO.getId());
        hrmsEmployeeAbnormalOperationRecordDO.setAbnormalId(param.getAbnormalId());
        hrmsEmployeeAbnormalOperationRecordDO.setOperationType(AttendanceAbnormalOperationTypeEnum.ADD_DURATION.getCode());
        BaseDOUtil.fillDOInsert(hrmsApplicationFormDO);

        if (param.getOperationType() == 1) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }
    }


    private void leaveAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        initInfoApiDTO.setBizId(hrmsApplicationFormDO.getId().toString());
        initInfoApiDTO.setApprovalType(hrmsApplicationFormDO.getFormType());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setCountry(hrmsApplicationFormDO.getCountry());
        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
        initInfoApiDTO.setApplyUserCode(hrmsApplicationFormDO.getUserCode());
        initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        initInfoApiDTO.setAppointApprovalCode(hrmsApplicationFormDO.getApplicationCode());


        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        //被申请人姓名
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_NAME.getCode(), hrmsApplicationFormDO.getUserName(), null);
        //被申请人编码
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_CODE.getCode(), hrmsApplicationFormDO.getUserCode(), null);
        //被申请人部门
        HrmsEntDeptDO hrmsEntDeptDO = hrmsDeptManage.selectById(hrmsApplicationFormDO.getDeptId());
        if (hrmsEntDeptDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc()));
        }
        Map<String, String> deptMap = new HashMap<>();
        deptMap.put(LanguageTypeEnum.zh_CN.getCode(), hrmsEntDeptDO.getDeptNameCn());
        deptMap.put(LanguageTypeEnum.en_US.getCode(), hrmsEntDeptDO.getDeptNameEn());
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DEPT_NAME.getCode(), hrmsEntDeptDO.getDeptNameEn(), deptMap);
        HrmsUserInfoDO userInfo = hrmsUserInfoService.getByUserId(hrmsApplicationFormDO.getUserId());
        // 仓内外包人员不存在岗位,所以针对仓内外包人员不设置岗位
        if (!Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(hrmsApplicationFormDO.getCountry())
                || ObjectUtil.notEqual(hrmsApplicationFormDO.getIsWarehouseStaff(), 1)
                || ObjectUtil.notEqual(userInfo.getEmployeeType(), EmploymentTypeEnum.OS_FIXED_SALARY.getCode())) {
            //被申请人岗位
            HrmsEntPostDO hrmsEntPostDO = hrmsEntPostDao.getById(hrmsApplicationFormDO.getPostId());
            if (hrmsEntPostDO == null) {
                throw BusinessException.get(HrmsErrorCodeEnums.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.POST_NOT_EXITS.getDesc()));
            }
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.POST_NAME.getCode(), hrmsEntPostDO.getPostNameEn(), null);
        }
        //假期规则主键
        List<HrmsApplicationFormAttrDO> configId = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.configID.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.CONFIG_ID.getCode(), CollectionUtils.isNotEmpty(configId) ? configId.get(0).getAttrValue() : null, null);

        //假期类型
        String leaveTypeByLang = null;
        Map<String, String> leaveTypeMap = new HashMap();
        List<HrmsApplicationFormAttrDO> leaveType = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
        //设置异常类型中英文内容多语
        if (CollectionUtils.isNotEmpty(leaveType)) {
            leaveTypeByLang = leaveType.get(0).getAttrValue();
            Map<String, Map<String, DictVO>> allLangByTypeCodes = dictService.getAllLangByTypeCodes(Arrays.asList(BusinessConstant.SysDictDataTypeConstant.HRMS_ATTENDANCE_LEAVE_TYPE));
            Map<String, DictVO> leaveTypeEnumMap = allLangByTypeCodes.get(BusinessConstant.SysDictDataTypeConstant.HRMS_ATTENDANCE_LEAVE_TYPE);
            Map<String, DictVO> lowerleaveTypeEnumMap = leaveTypeEnumMap.entrySet().stream().collect(Collectors.toMap(item -> item.getKey().toLowerCase(), Map.Entry::getValue));
            DictVO dictVO = lowerleaveTypeEnumMap.get(leaveTypeByLang.toLowerCase());
            if (Objects.nonNull(dictVO)) {
                leaveTypeByLang = dictVO.getDataValue();
                leaveTypeMap.put(LanguageTypeEnum.zh_CN.getCode(), dictVO.getDataValueCn());
                leaveTypeMap.put(LanguageTypeEnum.en_US.getCode(), dictVO.getDataValueEn());
            }
        }
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_TYPE.getCode(), leaveTypeByLang, leaveTypeMap);

        //假期简称
        List<HrmsApplicationFormAttrDO> leaveShortName = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveShortName.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_SHORT_NAME.getCode(), CollectionUtils.isNotEmpty(leaveShortName) ? leaveShortName.get(0).getAttrValue() : null, null);

        //假期可用余额
        List<HrmsApplicationFormAttrDO> leaveResidue = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveResidueMinutes.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveResidue)) {
            BigDecimal leaveResidueMinutes = new BigDecimal(leaveResidue.get(0).getAttrValue());
            String days = BigDecimal.valueOf(leaveResidueMinutes.longValue() / BusinessConstant.MINUTES.longValue()).divide(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).setScale(2, RoundingMode.DOWN).toString();
            String descCN = days + "天";
            String descEN = days + "days";
            Map<String, String> leaveResidueMinutesMap = new HashMap<>();
            leaveResidueMinutesMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
            leaveResidueMinutesMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_RESIDUAL.getCode(), descEN, leaveResidueMinutesMap);
        }

        List<HrmsApplicationFormAttrDO> leaveUnitDOList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveUnit.getLowerCode())).collect(Collectors.toList());

        //请假开始时间
        List<HrmsApplicationFormAttrDO> leaveStartDateDOList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveStartDateDOList) && CollectionUtils.isNotEmpty(leaveUnitDOList)) {
            HrmsApplicationFormAttrDO leaveStartDateDO = leaveStartDateDOList.get(0);
            String leaveStartDateString = leaveStartDateDO.getAttrValue();
            Date leaveStartDate = DateUtil.parse(leaveStartDateString, "yyyy-MM-dd HH:mm:ss");
            HrmsApplicationFormAttrDO leaveUnitDO = leaveUnitDOList.get(0);
            leaveStartDateString = DateUtil.format(leaveStartDate, "yyyy-MM-dd HH:mm");
//            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.DAYS.getCode())) {
//                leaveStartDateString = DateUtil.format(leaveStartDate, "yyyy-MM-dd");
//            }
//            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.HOURS.getCode())) {
//                leaveStartDateString = DateUtil.format(leaveStartDate, "yyyy-MM-dd HH:mm");
//            }
//            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.MINUTES.getCode())) {
//                leaveStartDateString = DateUtil.format(leaveStartDate, "yyyy-MM-dd HH:mm");
//            }
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_START_DATE.getCode(), leaveStartDateString, null);
        }

        //请假结束时间
        List<HrmsApplicationFormAttrDO> leaveEndDateDOList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveEndDateDOList) && CollectionUtils.isNotEmpty(leaveUnitDOList)) {
            HrmsApplicationFormAttrDO leaveEndDateDO = leaveEndDateDOList.get(0);
            String leaveEndDateString = leaveEndDateDO.getAttrValue();
            Date leaveEndDate = DateUtil.parse(leaveEndDateString, "yyyy-MM-dd HH:mm:ss");
            HrmsApplicationFormAttrDO leaveUnitDO = leaveUnitDOList.get(0);
            leaveEndDateString = DateUtil.format(leaveEndDate, "yyyy-MM-dd HH:mm");
//            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.DAYS.getCode())) {
//                leaveEndDateString = DateUtil.format(DateUtil.offsetDay(leaveEndDate, -1), "yyyy-MM-dd");
//            }
//            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.HOURS.getCode())) {
//                leaveEndDateString = DateUtil.format(leaveEndDate, "yyyy-MM-dd HH:mm");
//            }
//            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.MINUTES.getCode())) {
//                leaveEndDateString = DateUtil.format(leaveEndDate, "yyyy-MM-dd HH:mm");
//            }
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_END_DATE.getCode(), leaveEndDateString, null);
        }

        //备注
        List<HrmsApplicationFormAttrDO> remark = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //附件
        List<HrmsApplicationFormAttrDO> attachment = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachment)) {
            List<AttachmentDTO> attachmentList = JSON.parseArray(attachment.get(0).getAttrValue(), AttachmentDTO.class);
            List<FileTemplateApiDTO> fileTemplateApiDTOList = new ArrayList<>();
            for (AttachmentDTO attachmentDTO : attachmentList) {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(attachmentDTO.getAttachmentName());
                apiDTO.setFileType(attachmentDTO.getAttachmentType());
                apiDTO.setFileUrl(attachmentDTO.getUrlPath());
                fileTemplateApiDTOList.add(apiDTO);
            }
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.ATTACHMENT.getCode(), JSON.toJSONString(fileTemplateApiDTOList), null);
        }

        List<HrmsApplicationFormAttrDO> dayInfoList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
        List<String> dayIdList = new ArrayList<>();
        List<String> daysList = new ArrayList<>();
        List<String> hoursList = new ArrayList<>();
        List<String> minutesList = new ArrayList<>();
        List<String> legalWorkingHoursList = new ArrayList<>();
        List<String> dayShiftInfoList = new ArrayList<>();
        List<String> leaveInfoList = new ArrayList<>();
        List<String> restInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dayInfoList)) {
            List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoList.get(0).getAttrValue(), DayDurationInfoDTO.class);

            BigDecimal days = BigDecimal.ZERO;
            BigDecimal hours = BigDecimal.ZERO;
            BigDecimal minutes = BigDecimal.ZERO;

            for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                days = days.add(dayDurationInfoDTO.getDays());
                hours = hours.add(dayDurationInfoDTO.getHours());
                minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                dayIdList.add(dayDurationInfoDTO.getDayId().toString());
                daysList.add(dayDurationInfoDTO.getDays().toString());
                hoursList.add(dayDurationInfoDTO.getHours().toString());
                minutesList.add(dayDurationInfoDTO.getMinutes().toString());
                legalWorkingHoursList.add(dayDurationInfoDTO.getLegalWorkingHours().toString());
                dayShiftInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getDayShiftInfoList()));
                leaveInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getLeaveInfoList()));
                restInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getRestInfoList()));
            }
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DAY_ID_LIST.getCode(), JSON.toJSONString(dayIdList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DAYS_LIST.getCode(), JSON.toJSONString(daysList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.HOURS_LIST.getCode(), JSON.toJSONString(hoursList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.MINUTES_LIST.getCode(), JSON.toJSONString(minutesList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEGAL_WORKING_HOURS_LIST.getCode(), JSON.toJSONString(legalWorkingHoursList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DAY_SHIFT_INFO_LIST.getCode(), JSON.toJSONString(dayShiftInfoList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_INFO_LIST.getCode(), JSON.toJSONString(leaveInfoList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.REST_INFO_LIST.getCode(), JSON.toJSONString(restInfoList), null);

            String descCN = days + "天" + hours + "小时" + minutes + "分钟";
            String descEN = days + "days" + hours + "hours" + minutes + "minutes";
            Map<String, String> expectedLeaveTimeMap = new HashMap<>();
            expectedLeaveTimeMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
            expectedLeaveTimeMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.EXPECTED_LEAVE_TIME.getCode(), descEN, expectedLeaveTimeMap);
        }

        //被审批人ID
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), hrmsApplicationFormDO.getUserId() != null ? hrmsApplicationFormDO.getUserId().toString() : null, null);

        //被申请人部门ID
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DEPT_ID.getCode(), hrmsApplicationFormDO.getDeptId() != null ? hrmsApplicationFormDO.getDeptId().toString() : null, null);

        //被申请人所在国
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_COUNTRY.getCode(), hrmsApplicationFormDO.getCountry(), null);

        //被申请人结算国
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_ORIGIN_COUNTRY.getCode(), hrmsApplicationFormDO.getOriginCountry(), null);

        // 被申请人是否仓内
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.IS_WAREHOUSE_STAFF.getCode(), hrmsApplicationFormDO.getIsWarehouseStaff() != null ? hrmsApplicationFormDO.getIsWarehouseStaff().toString() : null, null);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("leaveAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }

    private void outOfOfficeAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        initInfoApiDTO.setBizId(hrmsApplicationFormDO.getId().toString());
        initInfoApiDTO.setApprovalType(hrmsApplicationFormDO.getFormType());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setCountry(hrmsApplicationFormDO.getCountry());
        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
        initInfoApiDTO.setApplyUserCode(hrmsApplicationFormDO.getUserCode());
        initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        initInfoApiDTO.setAppointApprovalCode(hrmsApplicationFormDO.getApplicationCode());


        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        //被申请人姓名
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_NAME.getCode(), hrmsApplicationFormDO.getUserName(), null);
        //被申请人编码
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_CODE.getCode(), hrmsApplicationFormDO.getUserCode(), null);
        //被申请人部门
        HrmsEntDeptDO hrmsEntDeptDO = hrmsDeptManage.selectById(hrmsApplicationFormDO.getDeptId());
        if (hrmsEntDeptDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc()));
        }
        Map<String, String> deptMap = new HashMap<>();
        deptMap.put(LanguageTypeEnum.zh_CN.getCode(), hrmsEntDeptDO.getDeptNameCn());
        deptMap.put(LanguageTypeEnum.en_US.getCode(), hrmsEntDeptDO.getDeptNameEn());
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DEPT_NAME.getCode(), hrmsEntDeptDO.getDeptNameEn(), deptMap);

        //被申请人岗位
        HrmsEntPostDO hrmsEntPostDO = hrmsEntPostDao.getById(hrmsApplicationFormDO.getPostId());
        if (hrmsEntPostDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.POST_NOT_EXITS.getDesc()));
        }
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.POST_NAME.getCode(), hrmsEntPostDO.getPostNameEn(), null);

        //外勤开始时间
        List<HrmsApplicationFormAttrDO> outOfOfficeStartDateDOList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outOfOfficeStartDateDOList)) {
            Date outOfOfficeStartDate = DateUtil.parse(outOfOfficeStartDateDOList.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
            String outOfOfficeStartDateString = DateUtil.format(outOfOfficeStartDate, "yyyy-MM-dd HH:mm");
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.OUT_OF_OFFICE_START_DATE.getCode(), outOfOfficeStartDateString, null);
        }

        //外勤结束时间
        List<HrmsApplicationFormAttrDO> outOfOfficeEndDateDOList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outOfOfficeEndDateDOList)) {
            Date outOfOfficeEndDate = DateUtil.parse(outOfOfficeEndDateDOList.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
            String outOfOfficeEndDateString = DateUtil.format(outOfOfficeEndDate, "yyyy-MM-dd HH:mm");
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.OUT_OF_OFFICE_END_DATE.getCode(), outOfOfficeEndDateString, null);
        }

        //备注
        List<HrmsApplicationFormAttrDO> remark = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //附件
        List<HrmsApplicationFormAttrDO> attachment = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachment)) {
            List<AttachmentDTO> attachmentList = JSON.parseArray(attachment.get(0).getAttrValue(), AttachmentDTO.class);
            List<FileTemplateApiDTO> fileTemplateApiDTOList = new ArrayList<>();
            for (AttachmentDTO attachmentDTO : attachmentList) {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(attachmentDTO.getAttachmentName());
                apiDTO.setFileType(attachmentDTO.getAttachmentType());
                apiDTO.setFileUrl(attachmentDTO.getUrlPath());
                fileTemplateApiDTOList.add(apiDTO);
            }
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.ATTACHMENT.getCode(), JSON.toJSONString(fileTemplateApiDTOList), null);
        }

        List<HrmsApplicationFormAttrDO> dayInfoList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
        List<String> dayIdList = new ArrayList<>();
        List<String> daysList = new ArrayList<>();
        List<String> hoursList = new ArrayList<>();
        List<String> minutesList = new ArrayList<>();
        List<String> legalWorkingHoursList = new ArrayList<>();
        List<String> dayShiftInfoList = new ArrayList<>();
        List<String> leaveInfoList = new ArrayList<>();
        List<String> restInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dayInfoList)) {
            List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoList.get(0).getAttrValue(), DayDurationInfoDTO.class);

            BigDecimal days = BigDecimal.ZERO;
            BigDecimal hours = BigDecimal.ZERO;
            BigDecimal minutes = BigDecimal.ZERO;

            for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                days = days.add(dayDurationInfoDTO.getDays());
                hours = hours.add(dayDurationInfoDTO.getHours());
                minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                dayIdList.add(dayDurationInfoDTO.getDayId().toString());
                daysList.add(dayDurationInfoDTO.getDays().toString());
                hoursList.add(dayDurationInfoDTO.getHours().toString());
                minutesList.add(dayDurationInfoDTO.getMinutes().toString());
                legalWorkingHoursList.add(dayDurationInfoDTO.getLegalWorkingHours().toString());
                dayShiftInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getDayShiftInfoList()));
                leaveInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getLeaveInfoList()));
                restInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getRestInfoList()));
            }
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DAY_ID_LIST.getCode(), JSON.toJSONString(dayIdList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DAYS_LIST.getCode(), JSON.toJSONString(daysList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.HOURS_LIST.getCode(), JSON.toJSONString(hoursList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.MINUTES_LIST.getCode(), JSON.toJSONString(minutesList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.LEGAL_WORKING_HOURS_LIST.getCode(), JSON.toJSONString(legalWorkingHoursList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DAY_SHIFT_INFO_LIST.getCode(), JSON.toJSONString(dayShiftInfoList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.LEAVE_INFO_LIST.getCode(), JSON.toJSONString(leaveInfoList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.REST_INFO_LIST.getCode(), JSON.toJSONString(restInfoList), null);

            String descCN = days + "天" + hours + "小时" + minutes + "分钟";
            String descEN = days + "days" + hours + "hours" + minutes + "minutes";
            Map<String, String> expectedLeaveTimeMap = new HashMap<>();
            expectedLeaveTimeMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
            expectedLeaveTimeMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.EXPECTED_OUT_OF_OFFICE_TIME.getCode(), descEN, expectedLeaveTimeMap);
        }

        //被审批人ID
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), hrmsApplicationFormDO.getUserId() != null ? hrmsApplicationFormDO.getUserId().toString() : null, null);

        //被申请人部门ID
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DEPT_ID.getCode(), hrmsApplicationFormDO.getDeptId() != null ? hrmsApplicationFormDO.getDeptId().toString() : null, null);

        //被申请人所在国
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_COUNTRY.getCode(), hrmsApplicationFormDO.getCountry(), null);

        //被申请人结算国
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_ORIGIN_COUNTRY.getCode(), hrmsApplicationFormDO.getOriginCountry(), null);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("outOfOfficeAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));
    }

    private void reissueCardAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        initInfoApiDTO.setBizId(hrmsApplicationFormDO.getId().toString());
        initInfoApiDTO.setApprovalType(hrmsApplicationFormDO.getFormType());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setCountry(hrmsApplicationFormDO.getCountry());
        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
        initInfoApiDTO.setApplyUserCode(hrmsApplicationFormDO.getUserCode());
        initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        initInfoApiDTO.setAppointApprovalCode(hrmsApplicationFormDO.getApplicationCode());


        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        //被申请人姓名
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_NAME.getCode(), hrmsApplicationFormDO.getUserName(), null);
        //被申请人编码
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_CODE.getCode(), hrmsApplicationFormDO.getUserCode(), null);
        //被申请人部门
        HrmsEntDeptDO hrmsEntDeptDO = hrmsDeptManage.selectById(hrmsApplicationFormDO.getDeptId());
        if (hrmsEntDeptDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc()));
        }
        Map<String, String> deptMap = new HashMap<>();
        deptMap.put(LanguageTypeEnum.zh_CN.getCode(), hrmsEntDeptDO.getDeptNameCn());
        deptMap.put(LanguageTypeEnum.en_US.getCode(), hrmsEntDeptDO.getDeptNameEn());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.DEPT_NAME.getCode(), hrmsEntDeptDO.getDeptNameEn(), deptMap);

        // 仓内外包人员不存在岗位,所以针对仓内外包人员不设置岗位：仓内补卡是新的流程，新流程不需要岗位字段。所以这边直接拦截了
        if (ObjectUtil.notEqual(hrmsApplicationFormDO.getFormType(), HrAttendanceApplicationFormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode())) {
            //被申请人岗位
            HrmsEntPostDO hrmsEntPostDO = hrmsEntPostDao.getById(hrmsApplicationFormDO.getPostId());
            if (hrmsEntPostDO == null) {
                throw BusinessException.get(HrmsErrorCodeEnums.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.POST_NOT_EXITS.getDesc()));
            }
            customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.POST_NAME.getCode(), hrmsEntPostDO.getPostNameEn(), null);
        }

        //补卡日期
        List<HrmsApplicationFormAttrDO> reissueCardDate = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REISSUE_CARD_DATE.getCode(), CollectionUtils.isNotEmpty(reissueCardDate) ? reissueCardDate.get(0).getAttrValue() : null, null);

        // mex仓内：用工类型获取
        HrmsUserInfoDO userInfo = hrmsUserInfoService.getByUserId(hrmsApplicationFormDO.getUserId());
        if (ObjectUtil.isNotNull(userInfo)) {
            EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(userInfo.getEmployeeType());
            Map<String, String> employmentTypeMap = new HashMap<>();
            employmentTypeMap.put(LanguageTypeEnum.zh_CN.getCode(), employmentTypeEnum.getDesc());
            employmentTypeMap.put(LanguageTypeEnum.en_US.getCode(), employmentTypeEnum.getDescEn());
            customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.EMPLOYMENT_TYPE.getCode(), employmentTypeEnum.getDescEn(), employmentTypeMap);
        }

        // mex仓内考勤特有字段：常驻国是mex，并且是仓内
        if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(hrmsApplicationFormDO.getCountry())
                && ObjectUtil.equal(hrmsApplicationFormDO.getIsWarehouseStaff(), BusinessConstant.Y)) {
            WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
            warehouseDetailParam.setUserCodeList(Collections.singletonList(hrmsApplicationFormDO.getUserCode()));
            String reissueCardDateStr = CollectionUtils.isNotEmpty(reissueCardDate) ? reissueCardDate.get(0).getAttrValue() : null;
            if (StringUtils.isNotEmpty(reissueCardDateStr)) {
                Date reissueDate = DateUtil.parse(reissueCardDateStr, DateFormatterUtil.FORMAT_YYYYMMDD);
                warehouseDetailParam.setStartTime(reissueDate);
                warehouseDetailParam.setEndTime(reissueDate);
            }
            List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByCondition(warehouseDetailParam);
            if (CollectionUtils.isNotEmpty(warehouseDetailDOS)) {
                HrmsWarehouseDetailDO warehouseDetailDO = warehouseDetailDOS.get(0);
                HrmsEntDeptDO deptDO = hrmsDeptManage.selectById(warehouseDetailDO.getOcId());
                Map<String, String> vendorNameMap = warehouseSupplierService.getSupplierByCodes(Collections.singletonList(warehouseDetailDO.getVendorCode()));
                customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.WORK_OC_NAME.getCode(), deptDO.getDeptNameEn(), null);
                customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.WORK_VENDOR_NAME.getCode(), vendorNameMap.get(warehouseDetailDO.getVendorCode()), null);
            }
        }

        //补卡异常类型
        List<HrmsApplicationFormAttrDO> reissueCardType = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardType.getLowerCode())).collect(Collectors.toList());
        String type = null;
        String typeDetail = null;
        Map<String, String> typeMap = new HashMap<>();
        Map<String, String> typeDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(reissueCardType)) {
            type = reissueCardType.get(0).getAttrValue();
            AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(type);
            if (Objects.nonNull(abnormalTypeEnum)) {
                //设置异常类型中英文内容多语
                type = abnormalTypeEnum.getDescEn();
                typeMap.put(LanguageTypeEnum.zh_CN.getCode(), abnormalTypeEnum.getDesc());
                typeMap.put(LanguageTypeEnum.en_US.getCode(), abnormalTypeEnum.getDescEn());
                //设置异常描述中英文内容多语
                typeDetail = abnormalTypeEnum.getDetailEn();
                typeDetailMap.put(LanguageTypeEnum.zh_CN.getCode(), abnormalTypeEnum.getDetail());
                typeDetailMap.put(LanguageTypeEnum.en_US.getCode(), abnormalTypeEnum.getDetailEn());
            }
        }
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REISSUE_CARD_TYPE.getCode(), type, typeMap);
        //补卡异常类型描述
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REISSUE_CARD_TYPE_DESC.getCode(), typeDetail, typeDetailMap);

        //剩余可用补卡次数
        List<HrmsApplicationFormAttrDO> residueReissueCardCount = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.residueReissueCardCount.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.RESIDUE_REISSUE_CARD_COUNT.getCode(), CollectionUtils.isNotEmpty(residueReissueCardCount) ? residueReissueCardCount.get(0).getAttrValue() : null, null);

        //当前补卡日期对应的考勤周期起始时间
        List<HrmsApplicationFormAttrDO> attendanceStartDate = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attendanceStartDate.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ATTENDANCE_START_DATE.getCode(), CollectionUtils.isNotEmpty(attendanceStartDate) ? attendanceStartDate.get(0).getAttrValue() : null, null);

        //当前补卡日期对应的考勤周期截止时间
        List<HrmsApplicationFormAttrDO> attendanceEndDate = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attendanceEndDate.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ATTENDANCE_END_DATE.getCode(), CollectionUtils.isNotEmpty(attendanceEndDate) ? attendanceEndDate.get(0).getAttrValue() : null, null);

        //打卡规则对应的班次的所有的时刻信息
        List<HrmsApplicationFormAttrDO> punchConfigClassItemInfo = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.punchConfigClassItemInfo.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.PUNCH_CONFIG_CLASS_ITEM_INFO.getCode(), CollectionUtils.isNotEmpty(punchConfigClassItemInfo) ? punchConfigClassItemInfo.get(0).getAttrValue() : null, null);

        //实际打卡时间(没有就为空)，有多个取离上下班最近的一个
        List<HrmsApplicationFormAttrDO> actualPunchTime = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.actualPunchTime.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ACTUAL_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(actualPunchTime) ? actualPunchTime.get(0).getAttrValue() : null, null);

        //补卡后的时间(根据时刻时间来补)
        List<HrmsApplicationFormAttrDO> correctPunchTime = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.CORRECT_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(correctPunchTime) ? correctPunchTime.get(0).getAttrValue() : null, null);

        //备注
        List<HrmsApplicationFormAttrDO> remark = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //实际最早打卡时间
        List<HrmsApplicationFormAttrDO> earlyPunchTime = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.earlyPunchTime.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.EARLY_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(earlyPunchTime) ? earlyPunchTime.get(0).getAttrValue() : null, null);

        //实际最晚打卡时间
        List<HrmsApplicationFormAttrDO> latePunchTime = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.latePunchTime.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.LATE_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(latePunchTime) ? latePunchTime.get(0).getAttrValue() : null, null);

        //附件
        List<HrmsApplicationFormAttrDO> attachment = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachment)) {
            List<AttachmentDTO> attachmentList = JSON.parseArray(attachment.get(0).getAttrValue(), AttachmentDTO.class);
            List<FileTemplateApiDTO> fileTemplateApiDTOList = new ArrayList<>();
            for (AttachmentDTO attachmentDTO : attachmentList) {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(attachmentDTO.getAttachmentName());
                apiDTO.setFileType(attachmentDTO.getAttachmentType());
                apiDTO.setFileUrl(attachmentDTO.getUrlPath());
                fileTemplateApiDTOList.add(apiDTO);
            }
            customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ATTACHMENT.getCode(), JSON.toJSONString(fileTemplateApiDTOList), null);
        }

        //被审批人ID
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), hrmsApplicationFormDO.getUserId() != null ? hrmsApplicationFormDO.getUserId().toString() : null, null);

        //被申请人部门ID
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.DEPT_ID.getCode(), hrmsApplicationFormDO.getDeptId() != null ? hrmsApplicationFormDO.getDeptId().toString() : null, null);

        //被申请人所在国
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_COUNTRY.getCode(), hrmsApplicationFormDO.getCountry(), null);

        //被申请人结算国
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_ORIGIN_COUNTRY.getCode(), hrmsApplicationFormDO.getOriginCountry(), null);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("reissueCardAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }

    private void leaveRevokeAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO, Long leaveApprovalId, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        initInfoApiDTO.setBizId(hrmsApplicationFormDO.getId().toString());
        initInfoApiDTO.setApprovalType(hrmsApplicationFormDO.getFormType());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setCountry(hrmsApplicationFormDO.getCountry());
        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
        initInfoApiDTO.setApplyUserCode(hrmsApplicationFormDO.getUserCode());
        initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        initInfoApiDTO.setAppointApprovalCode(hrmsApplicationFormDO.getApplicationCode());
        //关联请假单据的审批ID
        if (leaveApprovalId != null) {
            initInfoApiDTO.setRelationApprovalIdList(Arrays.asList(leaveApprovalId));
        }

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        //被申请人姓名
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_NAME.getCode(), hrmsApplicationFormDO.getUserName(), null);
        //被申请人编码
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_CODE.getCode(), hrmsApplicationFormDO.getUserCode(), null);
        //被申请人部门
        HrmsEntDeptDO hrmsEntDeptDO = hrmsDeptManage.selectById(hrmsApplicationFormDO.getDeptId());
        if (hrmsEntDeptDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc()));
        }
        Map<String, String> deptMap = new HashMap<>();
        deptMap.put(LanguageTypeEnum.zh_CN.getCode(), hrmsEntDeptDO.getDeptNameCn());
        deptMap.put(LanguageTypeEnum.en_US.getCode(), hrmsEntDeptDO.getDeptNameEn());
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DEPT_NAME.getCode(), hrmsEntDeptDO.getDeptNameEn(), deptMap);

        //被申请人岗位
        HrmsEntPostDO hrmsEntPostDO = hrmsEntPostDao.getById(hrmsApplicationFormDO.getPostId());
        if (hrmsEntPostDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.POST_NOT_EXITS.getDesc()));
        }
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.POST_NAME.getCode(), hrmsEntPostDO.getPostNameEn(), null);

        //假期规则主键
        List<HrmsApplicationFormAttrDO> configId = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.configID.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.CONFIG_ID.getCode(), CollectionUtils.isNotEmpty(configId) ? configId.get(0).getAttrValue() : null, null);

        //假期类型
        String leaveTypeByLang = null;
        Map<String, String> leaveTypeMap = new HashMap();
        List<HrmsApplicationFormAttrDO> leaveType = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
        //设置异常类型中英文内容多语
        if (CollectionUtils.isNotEmpty(leaveType)) {
            leaveTypeByLang = leaveType.get(0).getAttrValue();
            Map<String, Map<String, DictVO>> allLangByTypeCodes = dictService.getAllLangByTypeCodes(Arrays.asList(BusinessConstant.SysDictDataTypeConstant.HRMS_ATTENDANCE_LEAVE_TYPE));
            Map<String, DictVO> leaveTypeEnumMap = allLangByTypeCodes.get(BusinessConstant.SysDictDataTypeConstant.HRMS_ATTENDANCE_LEAVE_TYPE);
            Map<String, DictVO> lowerleaveTypeEnumMap = leaveTypeEnumMap.entrySet().stream().collect(Collectors.toMap(item -> item.getKey().toLowerCase(), Map.Entry::getValue));
            DictVO dictVO = lowerleaveTypeEnumMap.get(leaveTypeByLang.toLowerCase());
            if (Objects.nonNull(dictVO)) {
                leaveTypeByLang = dictVO.getDataValue();
                leaveTypeMap.put(LanguageTypeEnum.zh_CN.getCode(), dictVO.getDataValueCn());
                leaveTypeMap.put(LanguageTypeEnum.en_US.getCode(), dictVO.getDataValueEn());
            }
        }
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_TYPE.getCode(), leaveTypeByLang, leaveTypeMap);

        //假期简称
        List<HrmsApplicationFormAttrDO> leaveShortName = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveShortName.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_SHORT_NAME.getCode(), CollectionUtils.isNotEmpty(leaveShortName) ? leaveShortName.get(0).getAttrValue() : null, null);

        //假期可用余额
        List<HrmsApplicationFormAttrDO> leaveResidue = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveResidueMinutes.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveResidue)) {
            BigDecimal leaveResidueMinutes = new BigDecimal(leaveResidue.get(0).getAttrValue());
            Long days = BigDecimal.valueOf(leaveResidueMinutes.longValue() % BusinessConstant.MINUTES.longValue()).divide(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).setScale(2, RoundingMode.DOWN).longValue();
            String descCN = days + "天";
            String descEN = days + "days";
            Map<String, String> leaveResidueMinutesMap = new HashMap<>();
            leaveResidueMinutesMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
            leaveResidueMinutesMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_RESIDUAL.getCode(), descEN, leaveResidueMinutesMap);
        }

        List<HrmsApplicationFormAttrDO> leaveUnitDOList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveUnit.getLowerCode())).collect(Collectors.toList());

        //请假开始时间
        List<HrmsApplicationFormAttrDO> leaveStartDateDOList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveStartDateDOList) && CollectionUtils.isNotEmpty(leaveUnitDOList)) {
            HrmsApplicationFormAttrDO leaveStartDateDO = leaveStartDateDOList.get(0);
            String leaveStartDateString = leaveStartDateDO.getAttrValue();
            Date leaveStartDate = DateUtil.parse(leaveStartDateString, "yyyy-MM-dd HH:mm:ss");
            HrmsApplicationFormAttrDO leaveUnitDO = leaveUnitDOList.get(0);
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.DAYS.getCode())) {
                leaveStartDateString = DateUtil.format(leaveStartDate, "yyyy-MM-dd");
            }
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.HOURS.getCode())) {
                leaveStartDateString = DateUtil.format(leaveStartDate, "yyyy-MM-dd HH:mm");
            }
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.MINUTES.getCode())) {
                leaveStartDateString = DateUtil.format(leaveStartDate, "yyyy-MM-dd HH:mm");
            }
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_START_DATE.getCode(), leaveStartDateString, null);
        }

        //请假结束时间
        List<HrmsApplicationFormAttrDO> leaveEndDateDOList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveEndDateDOList) && CollectionUtils.isNotEmpty(leaveUnitDOList)) {
            HrmsApplicationFormAttrDO leaveEndDateDO = leaveEndDateDOList.get(0);
            String leaveEndDateString = leaveEndDateDO.getAttrValue();
            Date leaveEndDate = DateUtil.parse(leaveEndDateString, "yyyy-MM-dd HH:mm:ss");
            HrmsApplicationFormAttrDO leaveUnitDO = leaveUnitDOList.get(0);
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.DAYS.getCode())) {
                leaveEndDateString = DateUtil.format(DateUtil.offsetDay(leaveEndDate, -1), "yyyy-MM-dd");
            }
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.HOURS.getCode())) {
                leaveEndDateString = DateUtil.format(leaveEndDate, "yyyy-MM-dd HH:mm");
            }
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.MINUTES.getCode())) {
                leaveEndDateString = DateUtil.format(leaveEndDate, "yyyy-MM-dd HH:mm");
            }
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_END_DATE.getCode(), leaveEndDateString, null);
        }

        //备注
        List<HrmsApplicationFormAttrDO> remark = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //撤销原因
        List<HrmsApplicationFormAttrDO> revokeReason = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.revokeReason.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.REVOKE_REASON.getCode(), CollectionUtils.isNotEmpty(revokeReason) ? revokeReason.get(0).getAttrValue() : null, null);

        //附件
        List<HrmsApplicationFormAttrDO> attachment = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachment)) {
            List<AttachmentDTO> attachmentList = JSON.parseArray(attachment.get(0).getAttrValue(), AttachmentDTO.class);
            List<FileTemplateApiDTO> fileTemplateApiDTOList = new ArrayList<>();
            for (AttachmentDTO attachmentDTO : attachmentList) {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(attachmentDTO.getAttachmentName());
                apiDTO.setFileType(attachmentDTO.getAttachmentType());
                apiDTO.setFileUrl(attachmentDTO.getUrlPath());
                fileTemplateApiDTOList.add(apiDTO);
            }
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.ATTACHMENT.getCode(), JSON.toJSONString(fileTemplateApiDTOList), null);
        }

        List<HrmsApplicationFormAttrDO> dayInfoList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
        List<String> dayIdList = new ArrayList<>();
        List<String> daysList = new ArrayList<>();
        List<String> hoursList = new ArrayList<>();
        List<String> minutesList = new ArrayList<>();
        List<String> legalWorkingHoursList = new ArrayList<>();
        List<String> dayShiftInfoList = new ArrayList<>();
        List<String> leaveInfoList = new ArrayList<>();
        List<String> restInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dayInfoList)) {
            List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoList.get(0).getAttrValue(), DayDurationInfoDTO.class);

            BigDecimal days = BigDecimal.ZERO;
            BigDecimal hours = BigDecimal.ZERO;
            BigDecimal minutes = BigDecimal.ZERO;

            for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                days = days.add(dayDurationInfoDTO.getDays());
                hours = hours.add(dayDurationInfoDTO.getHours());
                minutes = minutes.add(dayDurationInfoDTO.getMinutes());

                dayIdList.add(dayDurationInfoDTO.getDayId().toString());
                daysList.add(dayDurationInfoDTO.getDays().toString());
                hoursList.add(dayDurationInfoDTO.getHours().toString());
                minutesList.add(dayDurationInfoDTO.getMinutes().toString());
                legalWorkingHoursList.add(dayDurationInfoDTO.getLegalWorkingHours().toString());
                dayShiftInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getDayShiftInfoList()));
                leaveInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getLeaveInfoList()));
                restInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getRestInfoList()));
            }
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DAY_ID_LIST.getCode(), JSON.toJSONString(dayIdList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DAYS_LIST.getCode(), JSON.toJSONString(daysList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.HOURS_LIST.getCode(), JSON.toJSONString(hoursList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.MINUTES_LIST.getCode(), JSON.toJSONString(minutesList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEGAL_WORKING_HOURS_LIST.getCode(), JSON.toJSONString(legalWorkingHoursList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DAY_SHIFT_INFO_LIST.getCode(), JSON.toJSONString(dayShiftInfoList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_INFO_LIST.getCode(), JSON.toJSONString(leaveInfoList), null);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.REST_INFO_LIST.getCode(), JSON.toJSONString(restInfoList), null);

            String descCN = days + "天" + hours + "小时" + minutes + "分钟";
            String descEN = days + "days" + hours + "hours" + minutes + "minutes";
            Map<String, String> expectedLeaveTimeMap = new HashMap<>();
            expectedLeaveTimeMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
            expectedLeaveTimeMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
            customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.EXPECTED_LEAVE_TIME.getCode(), descEN, expectedLeaveTimeMap);
        }

        //被审批人ID
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), hrmsApplicationFormDO.getUserId() != null ? hrmsApplicationFormDO.getUserId().toString() : null, null);

        //被申请人部门ID
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DEPT_ID.getCode(), hrmsApplicationFormDO.getDeptId() != null ? hrmsApplicationFormDO.getDeptId().toString() : null, null);

        //被申请人所在国
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_COUNTRY.getCode(), hrmsApplicationFormDO.getCountry(), null);

        //被申请人所在国
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_ORIGIN_COUNTRY.getCode(), hrmsApplicationFormDO.getOriginCountry(), null);

        // 被申请人是否仓内
        customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.IS_WAREHOUSE_STAFF.getCode(), hrmsApplicationFormDO.getIsWarehouseStaff() != null ? hrmsApplicationFormDO.getIsWarehouseStaff().toString() : null, null);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("leaveRevokeAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }

    private void addDurationApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO,
                                              HrmsApplicationFormDO hrmsApplicationFormDO,
                                              List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList,
                                              HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        initInfoApiDTO.setBizId(hrmsApplicationFormDO.getId().toString());
        initInfoApiDTO.setApprovalType(hrmsApplicationFormDO.getFormType());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setCountry(hrmsApplicationFormDO.getCountry());
        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
        initInfoApiDTO.setApplyUserCode(hrmsApplicationFormDO.getUserCode());
        initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        initInfoApiDTO.setAppointApprovalCode(hrmsApplicationFormDO.getApplicationCode());

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        //被申请人姓名
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.USER_NAME.getCode(), hrmsApplicationFormDO.getUserName(), null);
        //被申请人编码
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.USER_CODE.getCode(), hrmsApplicationFormDO.getUserCode(), null);
        //被申请人部门
        HrmsEntDeptDO hrmsEntDeptDO = hrmsDeptManage.selectById(hrmsApplicationFormDO.getDeptId());
        if (hrmsEntDeptDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc()));
        }
        Map<String, String> deptMap = new HashMap<>();
        deptMap.put(LanguageTypeEnum.zh_CN.getCode(), hrmsEntDeptDO.getDeptNameCn());
        deptMap.put(LanguageTypeEnum.en_US.getCode(), hrmsEntDeptDO.getDeptNameEn());
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.DEPT_NAME.getCode(), hrmsEntDeptDO.getDeptNameEn(), deptMap);

        // 用工类型
        HrmsUserInfoDO userInfo = hrmsUserInfoService.getByUserId(hrmsApplicationFormDO.getUserId());
        if (ObjectUtil.isNotNull(userInfo)) {
            EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(userInfo.getEmployeeType());
            Map<String, String> employmentTypeMap = new HashMap<>();
            employmentTypeMap.put(LanguageTypeEnum.zh_CN.getCode(), employmentTypeEnum.getDesc());
            employmentTypeMap.put(LanguageTypeEnum.en_US.getCode(), employmentTypeEnum.getDescEn());
            customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.EMPLOYMENT_TYPE.getCode(), employmentTypeEnum.getDescEn(), employmentTypeMap);
        }

        // 工作网点&工作供应商
        if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(hrmsApplicationFormDO.getCountry())
                && ObjectUtil.equal(hrmsApplicationFormDO.getIsWarehouseStaff(), BusinessConstant.Y)) {
            HrmsWarehouseDetailAbnormalDO warehouseDetailAbnormalDO = warehouseDetailAbnormalDao.selectByAbnormalId(abnormalAttendanceDO.getId());
            if (Objects.nonNull(warehouseDetailAbnormalDO)) {
                HrmsEntDeptDO deptDO = hrmsDeptManage.selectById(warehouseDetailAbnormalDO.getOcId());
                Map<String, String> deptNameMap = new HashMap<>();
                deptNameMap.put(LanguageTypeEnum.zh_CN.getCode(), deptDO.getDeptNameCn());
                deptNameMap.put(LanguageTypeEnum.en_US.getCode(), deptDO.getDeptNameEn());

                List<VendorInfoSimpleApiDTO> vendorInfoSimpleApiDTOS = vendorService.selectVendorList(Collections.singletonList(warehouseDetailAbnormalDO.getVendorId()));
                Map<Long, VendorInfoSimpleApiDTO> vendorInfoMap = vendorInfoSimpleApiDTOS.stream().collect(Collectors.toMap(VendorInfoSimpleApiDTO::getVendorId, o -> o, (v1, v2) -> v1));
                customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.WORK_OC_NAME.getCode(), deptDO.getDeptNameEn(), deptNameMap);
                if (Objects.nonNull(vendorInfoMap.get(warehouseDetailAbnormalDO.getVendorId()))) {
                    customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.WORK_VENDOR_NAME.getCode(), vendorInfoMap.get(warehouseDetailAbnormalDO.getVendorId()).getVendorName(), null);
                }
            }
        }

        //异常日期
        List<HrmsApplicationFormAttrDO> abnormalDate = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.abnormalDate.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.ABNORMAL_DATE.getCode(), CollectionUtils.isNotEmpty(abnormalDate) ? abnormalDate.get(0).getAttrValue() : null, null);

        //班次
        if (Objects.nonNull(abnormalAttendanceDO.getPunchClassConfigId())) {
//            List<HrmsAttendancePunchClassConfigDO> attendancePunchClassConfigDOS = attendancePunchClassConfigDao.selectClassByIdList(Collections.singletonList(abnormalAttendanceDO.getPunchClassConfigId()));
            List<HrmsAttendancePunchClassConfigDO> attendancePunchClassConfigDOS = punchConfigDaoFacade.getClassConfigAdapter().selectClassByIdList(
                    Collections.singletonList(abnormalAttendanceDO.getPunchClassConfigId()));
            customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.CLASS_NAME.getCode(), CollectionUtils.isNotEmpty(attendancePunchClassConfigDOS) ? attendancePunchClassConfigDOS.get(0).getClassName() : null, null);
        }

        //补时长异常类型
        List<HrmsApplicationFormAttrDO> addDurationType = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.abnormalType.getLowerCode())).collect(Collectors.toList());
        String type = null;
        String typeDetail = null;
        Map<String, String> typeMap = new HashMap<>();
        Map<String, String> typeDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(addDurationType)) {
            type = addDurationType.get(0).getAttrValue();
            AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(type);
            if (Objects.nonNull(abnormalTypeEnum)) {
                //设置异常类型中英文内容多语
                type = abnormalTypeEnum.getDescEn();
                typeMap.put(LanguageTypeEnum.zh_CN.getCode(), abnormalTypeEnum.getDesc());
                typeMap.put(LanguageTypeEnum.en_US.getCode(), abnormalTypeEnum.getDescEn());
                //设置异常描述中英文内容多语
                typeDetail = abnormalTypeEnum.getDetailEn();
                typeDetailMap.put(LanguageTypeEnum.zh_CN.getCode(), abnormalTypeEnum.getDetail());
                typeDetailMap.put(LanguageTypeEnum.en_US.getCode(), abnormalTypeEnum.getDetailEn());
            }
        }
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.ABNORMAL_TYPE.getCode(), type, typeMap);
        //补时长异常类型描述
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.ABNORMAL_TYPE_DESC.getCode(), typeDetail, typeDetailMap);

        //实出勤时长
        List<HrmsApplicationFormAttrDO> actualAttendanceTime = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.actualAttendanceTime.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.ACTUAL_ATTENDANCE_TIME.getCode(), CollectionUtils.isNotEmpty(actualAttendanceTime) ? actualAttendanceTime.get(0).getAttrValue() : null, null);

        //更新后的实出勤时长
        List<HrmsApplicationFormAttrDO> newActualAttendanceTime = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.newActualAttendanceTime.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.NEW_ACTUAL_ATTENDANCE_TIME.getCode(), CollectionUtils.isNotEmpty(newActualAttendanceTime) ? newActualAttendanceTime.get(0).getAttrValue() : null, null);

        //实工作时长
        List<HrmsApplicationFormAttrDO> actualWorkingHours = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.actualWorkingHours.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.ACTUAL_WORKING_HOURS.getCode(), CollectionUtils.isNotEmpty(actualWorkingHours) ? actualWorkingHours.get(0).getAttrValue() : null, null);

        //更新后的实工作时长
        List<HrmsApplicationFormAttrDO> newActualWorkingHours = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.newActualWorkingHours.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.NEW_ACTUAL_WORKING_HOURS.getCode(), CollectionUtils.isNotEmpty(newActualWorkingHours) ? newActualWorkingHours.get(0).getAttrValue() : null, null);

        //备注
        List<HrmsApplicationFormAttrDO> remark = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //附件
        List<HrmsApplicationFormAttrDO> attachment = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachment)) {
            List<AttachmentDTO> attachmentList = JSON.parseArray(attachment.get(0).getAttrValue(), AttachmentDTO.class);
            List<FileTemplateApiDTO> fileTemplateApiDTOList = new ArrayList<>();
            for (AttachmentDTO attachmentDTO : attachmentList) {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(attachmentDTO.getAttachmentName());
                apiDTO.setFileType(attachmentDTO.getAttachmentType());
                apiDTO.setFileUrl(attachmentDTO.getUrlPath());
                fileTemplateApiDTOList.add(apiDTO);
            }
            customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.ATTACHMENT.getCode(), JSON.toJSONString(fileTemplateApiDTOList), null);
        }

        //被审批人ID
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), hrmsApplicationFormDO.getUserId() != null ? hrmsApplicationFormDO.getUserId().toString() : null, null);

        //被申请人部门ID
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.DEPT_ID.getCode(), hrmsApplicationFormDO.getDeptId() != null ? hrmsApplicationFormDO.getDeptId().toString() : null, null);

        //被申请人所在国
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.USER_COUNTRY.getCode(), hrmsApplicationFormDO.getCountry(), null);

        //被申请人结算国
        customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.USER_ORIGIN_COUNTRY.getCode(), hrmsApplicationFormDO.getOriginCountry(), null);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("durationAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }


    private void outOfOfficeRevokeAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO, Long outOfOfficeApprovalId, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        initInfoApiDTO.setBizId(hrmsApplicationFormDO.getId().toString());
        initInfoApiDTO.setApprovalType(hrmsApplicationFormDO.getFormType());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setCountry(hrmsApplicationFormDO.getCountry());
        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
        initInfoApiDTO.setApplyUserCode(hrmsApplicationFormDO.getUserCode());
        initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        initInfoApiDTO.setAppointApprovalCode(hrmsApplicationFormDO.getApplicationCode());

        //关联请假单据的审批ID
        if (outOfOfficeApprovalId != null) {
            initInfoApiDTO.setRelationApprovalIdList(Arrays.asList(outOfOfficeApprovalId));
        }

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        //被申请人姓名
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_NAME.getCode(), hrmsApplicationFormDO.getUserName(), null);
        //被申请人编码
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_CODE.getCode(), hrmsApplicationFormDO.getUserCode(), null);
        //被申请人部门
        HrmsEntDeptDO hrmsEntDeptDO = hrmsDeptManage.selectById(hrmsApplicationFormDO.getDeptId());
        if (hrmsEntDeptDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc()));
        }
        Map<String, String> deptMap = new HashMap<>();
        deptMap.put(LanguageTypeEnum.zh_CN.getCode(), hrmsEntDeptDO.getDeptNameCn());
        deptMap.put(LanguageTypeEnum.en_US.getCode(), hrmsEntDeptDO.getDeptNameEn());
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DEPT_NAME.getCode(), hrmsEntDeptDO.getDeptNameEn(), deptMap);

        //被申请人岗位
        HrmsEntPostDO hrmsEntPostDO = hrmsEntPostDao.getById(hrmsApplicationFormDO.getPostId());
        if (hrmsEntPostDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.POST_NOT_EXITS.getDesc()));
        }
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.POST_NAME.getCode(), hrmsEntPostDO.getPostNameEn(), null);

        //外勤开始时间
        List<HrmsApplicationFormAttrDO> outOfOfficeStartDateDOList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outOfOfficeStartDateDOList)) {
            Date outOfOfficeStartDate = DateUtil.parse(outOfOfficeStartDateDOList.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
            String outOfOfficeStartDateString = DateUtil.format(outOfOfficeStartDate, "yyyy-MM-dd HH:mm");
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.OUT_OF_OFFICE_START_DATE.getCode(), outOfOfficeStartDateString, null);
        }

        //外勤结束时间
        List<HrmsApplicationFormAttrDO> outOfOfficeEndDateDOList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outOfOfficeEndDateDOList)) {
            Date outOfOfficeEndDate = DateUtil.parse(outOfOfficeEndDateDOList.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
            String outOfOfficeEndDateString = DateUtil.format(outOfOfficeEndDate, "yyyy-MM-dd HH:mm");
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.OUT_OF_OFFICE_END_DATE.getCode(), outOfOfficeEndDateString, null);
        }

        //备注
        List<HrmsApplicationFormAttrDO> remark = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //附件
        List<HrmsApplicationFormAttrDO> attachment = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachment)) {
            List<AttachmentDTO> attachmentList = JSON.parseArray(attachment.get(0).getAttrValue(), AttachmentDTO.class);
            List<FileTemplateApiDTO> fileTemplateApiDTOList = new ArrayList<>();
            for (AttachmentDTO attachmentDTO : attachmentList) {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(attachmentDTO.getAttachmentName());
                apiDTO.setFileType(attachmentDTO.getAttachmentType());
                apiDTO.setFileUrl(attachmentDTO.getUrlPath());
                fileTemplateApiDTOList.add(apiDTO);
            }
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.ATTACHMENT.getCode(), JSON.toJSONString(fileTemplateApiDTOList), null);
        }

        //撤销原因
        List<HrmsApplicationFormAttrDO> revokeReason = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.revokeReason.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.REVOKE_REASON.getCode(), CollectionUtils.isNotEmpty(revokeReason) ? revokeReason.get(0).getAttrValue() : null, null);

        List<HrmsApplicationFormAttrDO> dayInfoList = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
        List<String> dayIdList = new ArrayList<>();
        List<String> daysList = new ArrayList<>();
        List<String> hoursList = new ArrayList<>();
        List<String> minutesList = new ArrayList<>();
        List<String> legalWorkingHoursList = new ArrayList<>();
        List<String> dayShiftInfoList = new ArrayList<>();
        List<String> leaveInfoList = new ArrayList<>();
        List<String> restInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dayInfoList)) {
            List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoList.get(0).getAttrValue(), DayDurationInfoDTO.class);

            BigDecimal days = BigDecimal.ZERO;
            BigDecimal hours = BigDecimal.ZERO;
            BigDecimal minutes = BigDecimal.ZERO;

            for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                days = days.add(dayDurationInfoDTO.getDays());
                hours = hours.add(dayDurationInfoDTO.getHours());
                minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                dayIdList.add(dayDurationInfoDTO.getDayId().toString());
                daysList.add(dayDurationInfoDTO.getDays().toString());
                hoursList.add(dayDurationInfoDTO.getHours().toString());
                minutesList.add(dayDurationInfoDTO.getMinutes().toString());
                legalWorkingHoursList.add(dayDurationInfoDTO.getLegalWorkingHours().toString());
                dayShiftInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getDayShiftInfoList()));
                leaveInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getLeaveInfoList()));
                restInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getRestInfoList()));
            }
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DAY_ID_LIST.getCode(), JSON.toJSONString(dayIdList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DAYS_LIST.getCode(), JSON.toJSONString(daysList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.HOURS_LIST.getCode(), JSON.toJSONString(hoursList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.MINUTES_LIST.getCode(), JSON.toJSONString(minutesList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.LEGAL_WORKING_HOURS_LIST.getCode(), JSON.toJSONString(legalWorkingHoursList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DAY_SHIFT_INFO_LIST.getCode(), JSON.toJSONString(dayShiftInfoList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.LEAVE_INFO_LIST.getCode(), JSON.toJSONString(leaveInfoList), null);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.REST_INFO_LIST.getCode(), JSON.toJSONString(restInfoList), null);

            String descCN = days + "天" + hours + "小时" + minutes + "分钟";
            String descEN = days + "days" + hours + "hours" + minutes + "minutes";
            Map<String, String> expectedLeaveTimeMap = new HashMap<>();
            expectedLeaveTimeMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
            expectedLeaveTimeMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
            customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.EXPECTED_OUT_OF_OFFICE_TIME.getCode(), descEN, expectedLeaveTimeMap);
        }
        //被审批人ID
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), hrmsApplicationFormDO.getUserId() != null ? hrmsApplicationFormDO.getUserId().toString() : null, null);

        //被申请人部门ID
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DEPT_ID.getCode(), hrmsApplicationFormDO.getDeptId() != null ? hrmsApplicationFormDO.getDeptId().toString() : null, null);

        //被申请人所在国
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_COUNTRY.getCode(), hrmsApplicationFormDO.getCountry(), null);

        //被申请人所在国
        customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_ORIGIN_COUNTRY.getCode(), hrmsApplicationFormDO.getOriginCountry(), null);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("outOfOfficeRevokeAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }

    private void reissueCardRevokeAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO, Long outOfOfficeApprovalId, HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        initInfoApiDTO.setBizId(hrmsApplicationFormDO.getId().toString());
        initInfoApiDTO.setApprovalType(hrmsApplicationFormDO.getFormType());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setCountry(hrmsApplicationFormDO.getCountry());
        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
        initInfoApiDTO.setApplyUserCode(hrmsApplicationFormDO.getUserCode());
        initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        initInfoApiDTO.setAppointApprovalCode(hrmsApplicationFormDO.getApplicationCode());

        //关联请假单据的审批ID
        if (outOfOfficeApprovalId != null) {
            initInfoApiDTO.setRelationApprovalIdList(Arrays.asList(outOfOfficeApprovalId));
        }

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        //被申请人姓名
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_NAME.getCode(), hrmsApplicationFormDO.getUserName(), null);
        //被申请人编码
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_CODE.getCode(), hrmsApplicationFormDO.getUserCode(), null);
        //被申请人部门
        HrmsEntDeptDO hrmsEntDeptDO = hrmsDeptManage.selectById(hrmsApplicationFormDO.getDeptId());
        if (hrmsEntDeptDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc()));
        }
        Map<String, String> deptMap = new HashMap<>();
        deptMap.put(LanguageTypeEnum.zh_CN.getCode(), hrmsEntDeptDO.getDeptNameCn());
        deptMap.put(LanguageTypeEnum.en_US.getCode(), hrmsEntDeptDO.getDeptNameEn());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.DEPT_NAME.getCode(), hrmsEntDeptDO.getDeptNameEn(), deptMap);

        //被申请人岗位
        HrmsEntPostDO hrmsEntPostDO = hrmsEntPostDao.getById(hrmsApplicationFormDO.getPostId());
        if (hrmsEntPostDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.POST_NOT_EXITS.getDesc()));
        }
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.POST_NAME.getCode(), hrmsEntPostDO.getPostNameEn(), null);

        //撤销原因
        List<HrmsApplicationFormAttrDO> revokeReason = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.revokeReason.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REVOKE_REASON.getCode(), CollectionUtils.isNotEmpty(revokeReason) ? revokeReason.get(0).getAttrValue() : null, null);

        //补卡日期
        List<HrmsApplicationFormAttrDO> reissueCardDate = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REISSUE_CARD_DATE.getCode(), CollectionUtils.isNotEmpty(reissueCardDate) ? reissueCardDate.get(0).getAttrValue() : null, null);

        //补卡异常类型
        List<HrmsApplicationFormAttrDO> reissueCardType = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardType.getLowerCode())).collect(Collectors.toList());
        String type = null;
        String typeDetail = null;
        Map<String, String> typeMap = new HashMap<>();
        Map<String, String> typeDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(reissueCardType)) {
            type = reissueCardType.get(0).getAttrValue();
            AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(type);
            if (Objects.nonNull(abnormalTypeEnum)) {
                //设置异常类型中英文内容多语
                type = abnormalTypeEnum.getDescEn();
                typeMap.put(LanguageTypeEnum.zh_CN.getCode(), abnormalTypeEnum.getDesc());
                typeMap.put(LanguageTypeEnum.en_US.getCode(), abnormalTypeEnum.getDescEn());
                //设置异常描述中英文内容多语
                typeDetail = abnormalTypeEnum.getDetailEn();
                typeDetailMap.put(LanguageTypeEnum.zh_CN.getCode(), abnormalTypeEnum.getDetail());
                typeDetailMap.put(LanguageTypeEnum.en_US.getCode(), abnormalTypeEnum.getDetailEn());
            }
        }
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REISSUE_CARD_TYPE.getCode(), type, typeMap);
        //补卡异常类型描述
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REISSUE_CARD_TYPE_DESC.getCode(), typeDetail, typeDetailMap);

        //剩余可用补卡次数
        List<HrmsApplicationFormAttrDO> residueReissueCardCount = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.residueReissueCardCount.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.RESIDUE_REISSUE_CARD_COUNT.getCode(), CollectionUtils.isNotEmpty(residueReissueCardCount) ? residueReissueCardCount.get(0).getAttrValue() : null, null);

        //当前补卡日期对应的考勤周期起始时间
        List<HrmsApplicationFormAttrDO> attendanceStartDate = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attendanceStartDate.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ATTENDANCE_START_DATE.getCode(), CollectionUtils.isNotEmpty(attendanceStartDate) ? attendanceStartDate.get(0).getAttrValue() : null, null);

        //当前补卡日期对应的考勤周期截止时间
        List<HrmsApplicationFormAttrDO> attendanceEndDate = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attendanceEndDate.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ATTENDANCE_END_DATE.getCode(), CollectionUtils.isNotEmpty(attendanceEndDate) ? attendanceEndDate.get(0).getAttrValue() : null, null);

        //打卡规则对应的班次的所有的时刻信息
        List<HrmsApplicationFormAttrDO> punchConfigClassItemInfo = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.punchConfigClassItemInfo.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.PUNCH_CONFIG_CLASS_ITEM_INFO.getCode(), CollectionUtils.isNotEmpty(punchConfigClassItemInfo) ? punchConfigClassItemInfo.get(0).getAttrValue() : null, null);

        //实际打卡时间(没有就为空)，有多个取离上下班最近的一个
        List<HrmsApplicationFormAttrDO> actualPunchTime = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.actualPunchTime.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ACTUAL_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(actualPunchTime) ? actualPunchTime.get(0).getAttrValue() : null, null);

        //补卡后的时间(根据时刻时间来补)
        List<HrmsApplicationFormAttrDO> correctPunchTime = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.CORRECT_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(correctPunchTime) ? correctPunchTime.get(0).getAttrValue() : null, null);

        //备注
        List<HrmsApplicationFormAttrDO> remark = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //实际最早打卡时间
        List<HrmsApplicationFormAttrDO> earlyPunchTime = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.earlyPunchTime.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.EARLY_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(earlyPunchTime) ? earlyPunchTime.get(0).getAttrValue() : null, null);

        //实际最晚打卡时间
        List<HrmsApplicationFormAttrDO> latePunchTime = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.latePunchTime.getLowerCode())).collect(Collectors.toList());
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.LATE_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(latePunchTime) ? latePunchTime.get(0).getAttrValue() : null, null);

        //附件
        List<HrmsApplicationFormAttrDO> attachment = hrmsApplicationFormAttrDOArrayList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachment)) {
            List<AttachmentDTO> attachmentList = JSON.parseArray(attachment.get(0).getAttrValue(), AttachmentDTO.class);
            List<FileTemplateApiDTO> fileTemplateApiDTOList = new ArrayList<>();
            for (AttachmentDTO attachmentDTO : attachmentList) {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(attachmentDTO.getAttachmentName());
                apiDTO.setFileType(attachmentDTO.getAttachmentType());
                apiDTO.setFileUrl(attachmentDTO.getUrlPath());
                fileTemplateApiDTOList.add(apiDTO);
            }
            customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ATTACHMENT.getCode(), JSON.toJSONString(fileTemplateApiDTOList), null);
        }

        //被审批人ID
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), hrmsApplicationFormDO.getUserId() != null ? hrmsApplicationFormDO.getUserId().toString() : null, null);

        //被申请人部门ID
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.DEPT_ID.getCode(), hrmsApplicationFormDO.getDeptId() != null ? hrmsApplicationFormDO.getDeptId().toString() : null, null);

        //被申请人所在国
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_COUNTRY.getCode(), hrmsApplicationFormDO.getCountry(), null);

        //被申请人结算国
        customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_ORIGIN_COUNTRY.getCode(), hrmsApplicationFormDO.getOriginCountry(), null);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("reissueCardRevokeAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }

    private void userBaseInfoBuild(LeaveAddParam leaveAddParam, OutOfOfficeAddParam outOfOfficeAddParam, ReissueCardAddParam reissueCardAddParam) {
        Long userId = null;
        if (leaveAddParam != null) {
            userId = leaveAddParam.getUserId();
        }
        if (outOfOfficeAddParam != null) {
            userId = outOfOfficeAddParam.getUserId();
        }
        if (reissueCardAddParam != null) {
            userId = reissueCardAddParam.getUserId();
        }
        if (userId == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(userId);
        if (hrmsUserInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        if (leaveAddParam != null) {
            leaveAddParam.setUserCode(hrmsUserInfoDO.getUserCode());
            leaveAddParam.setUserName(hrmsUserInfoDO.getUserName());
            leaveAddParam.setDeptId(hrmsUserInfoDO.getDeptId());
            leaveAddParam.setPostId(hrmsUserInfoDO.getPostId());
            leaveAddParam.setCountry(hrmsUserInfoDO.getLocationCountry());
            leaveAddParam.setOriginCountry(hrmsUserInfoDO.getOriginCountry());
            leaveAddParam.setIsWarehouseStaff(hrmsUserInfoDO.getIsWarehouseStaff());
        }
        if (outOfOfficeAddParam != null) {
            outOfOfficeAddParam.setUserCode(hrmsUserInfoDO.getUserCode());
            outOfOfficeAddParam.setUserName(hrmsUserInfoDO.getUserName());
            outOfOfficeAddParam.setDeptId(hrmsUserInfoDO.getDeptId());
            outOfOfficeAddParam.setPostId(hrmsUserInfoDO.getPostId());
            outOfOfficeAddParam.setCountry(hrmsUserInfoDO.getLocationCountry());
            outOfOfficeAddParam.setOriginCountry(hrmsUserInfoDO.getOriginCountry());
            outOfOfficeAddParam.setIsWarehouseStaff(hrmsUserInfoDO.getIsWarehouseStaff());
        }
        if (reissueCardAddParam != null) {
            reissueCardAddParam.setUserCode(hrmsUserInfoDO.getUserCode());
            reissueCardAddParam.setUserName(hrmsUserInfoDO.getUserName());
            reissueCardAddParam.setDeptId(hrmsUserInfoDO.getDeptId());
            reissueCardAddParam.setPostId(hrmsUserInfoDO.getPostId());
            reissueCardAddParam.setCountry(hrmsUserInfoDO.getLocationCountry());
            reissueCardAddParam.setOriginCountry(hrmsUserInfoDO.getOriginCountry());
            reissueCardAddParam.setIsWarehouseStaff(hrmsUserInfoDO.getIsWarehouseStaff());
        }
    }

    private void userDayReissueAbnormalDTOBuild(HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO, List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOList, UserDayReissueInfoVO userDayReissueInfoVO, List<UserDayReissueAbnormalDTO> userDayReissueAbnormalDTOList) {
        UserDayReissueAbnormalDTO abnormalDTO = new UserDayReissueAbnormalDTO();
        userDayReissueAbnormalDTOList.add(abnormalDTO);
        abnormalDTO.setAbnormalId(abnormalAttendanceDO.getId());
        abnormalDTO.setReissueCardType(abnormalAttendanceDO.getAbnormalType());
        abnormalDTO.setReissueCardTypeDesc(abnormalAttendanceDO.getAbnormalType());
        AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(abnormalAttendanceDO.getAbnormalType());
        if (abnormalTypeEnum != null) {
            abnormalDTO.setReissueCardTypeDesc(RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDesc() : abnormalTypeEnum.getDescEn());
            abnormalDTO.setReissueCardTypeDetail(RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDetail() : abnormalTypeEnum.getDetailEn());
        }
        String configClassItemInfo = getConfigClassItemInfo(itemConfigDOList);
        if (StringUtils.isNotBlank(configClassItemInfo)) {
            userDayReissueInfoVO.setPunchConfigClassItemInfo(configClassItemInfo);
        }
        if (StringUtils.isNotBlank(abnormalAttendanceDO.getExtend())) {
            AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
            abnormalDTO.setCorrectPunchTime(abnormalExtendDTO.getCorrectPunchTime());
            abnormalDTO.setActualPunchTime(abnormalExtendDTO.getActualPunchTime());
        }
        userDayReissueInfoVO.setPunchConfigId(abnormalAttendanceDO.getPunchConfigId());
        userDayReissueInfoVO.setPunchClassId(abnormalAttendanceDO.getPunchClassConfigId());
    }

    /**
     * 构建审批流程DTO
     *
     * @param recordApiDTOList 审批流程DTO
     * @param resultDTOList    结果DTO
     * @param userCode         申请人用户编码
     */
    @Override
    public void previewDTOBuildContainsErrors(List<ApprovalEmptyRecordApiDTO> recordApiDTOList, List<ApprovalDetailStepRecordDTO> resultDTOList, String userCode) {
        //获取所有国家的HR信息
        List<String> hrUserCodeList = new ArrayList<>();
        countryMainPocMap.forEach((k, v) -> hrUserCodeList.add(v));
        List<UserDTO> allHrUserDTOS = hrmsUserInfoService.attendanceListUserInfoByUserCode(hrUserCodeList);
        //List<UserDTO> allHrUserDTOS = hrmsUserInfoService.listUserInfoByUserCode(hrUserCodeList);
        List<UserInfoDetailApiDTO> allHrUserInfoList = HrmsCollectionUtils.convert(allHrUserDTOS, UserInfoDetailApiDTO.class);
        Map<String, List<UserInfoDetailApiDTO>> hrUserCodeMap = allHrUserInfoList.stream().collect(Collectors.groupingBy(UserInfoDetailApiDTO::getUserCode));

        //获取所有国家信息
        List<CountryConfigDTO> countryConfigDTOList = countryService.queryAllCountryConfigList();
        List<CountryDTO> countryDTOList = BeanUtils.convert(CountryDTO.class, countryConfigDTOList);
        Map<String, List<CountryDTO>> countryMap = countryDTOList.stream().collect(Collectors.groupingBy(CountryDTO::getCountryName));

        //获取所有人员(角色和指定人员总和)
        List<String> allAppointUserCodeList = new ArrayList<>();
        List<String> allRoleUserCodeList = new ArrayList<>();
        List<String> allUserCodeList = new ArrayList<>();
        for (ApprovalEmptyRecordApiDTO approvalEmptyRecordApiDTO : recordApiDTOList) {
            ApprovalCreateUserApiDTO approvalCreateUserApiDTO = approvalEmptyRecordApiDTO.getApprovalCreateUserApiDTO();
            if (approvalCreateUserApiDTO == null) {
                approvalCreateUserApiDTO = new ApprovalCreateUserApiDTO();
            }
            //所有指定人员
            List<ApprovalUserInfoApiDTO> appointUserList = approvalCreateUserApiDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(appointUserList)) {
                appointUserList = new ArrayList<>();
            }
            List<String> appointUserCodeList = appointUserList.stream().map(ApprovalUserInfoApiDTO::getUserCode).collect(Collectors.toList());
            allAppointUserCodeList.addAll(appointUserCodeList);

            //所有角色对应人员
            List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserList = approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
            if (CollectionUtils.isEmpty(approvalCreateRoleUserList)) {
                approvalCreateRoleUserList = new ArrayList<>();
            }
            for (ApprovalCreateRoleUserApiDTO roleUserDTO : approvalCreateRoleUserList) {
                List<ApprovalUserInfoApiDTO> roleUserInfos = roleUserDTO.getApprovalUserInfos();
                if (CollectionUtils.isEmpty(roleUserInfos)) {
                    continue;
                }
                List<String> roleUserCodeList = roleUserInfos.stream().map(ApprovalUserInfoApiDTO::getUserCode).collect(Collectors.toList());
                allRoleUserCodeList.addAll(roleUserCodeList);
            }
        }
        allUserCodeList.addAll(allAppointUserCodeList);
        allUserCodeList.addAll(allRoleUserCodeList);
        allUserCodeList.add(userCode);
        //调用HR接口，获取所有人员信息
        List<UserDTO> users = hrmsUserInfoService.listUserInfoByUserCode(allUserCodeList);
        List<UserInfoDetailApiDTO> allUserInfoList = HrmsCollectionUtils.convert(users, UserInfoDetailApiDTO.class);
        Map<String, List<UserInfoDetailApiDTO>> userCodeMap = allUserInfoList.stream().collect(Collectors.groupingBy(UserInfoDetailApiDTO::getUserCode));


        int temp = 1;
        for (ApprovalEmptyRecordApiDTO approvalEmptyRecordApiDTO : recordApiDTOList) {
            //发起人节点
            if (temp == 1) {
                ApprovalDetailStepRecordDTO createApprovalUserDTO = new ApprovalDetailStepRecordDTO();
                createApprovalUserDTO.setApprovalRecordType("approval");
                createApprovalUserDTO.setRecordStatus(-1);
                createApprovalUserDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                createApprovalUserDTO.setStepName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                createApprovalUserDTO.setRecordStatusUpdateDate(new Date());
                createApprovalUserDTO.setStepId("APPLY");
                List<ApprovalUserInfoDTO> approvalUserDTOList = BeanUtils.convert(ApprovalUserInfoDTO.class, approvalEmptyRecordApiDTO.getApprovalUserInfoDTOS());
                createApprovalUserDTO.setApprovalUserInfoDTOS(approvalUserDTOList);
                resultDTOList.add(createApprovalUserDTO);
                temp++;
                continue;
            }
            //非发起人节点
            ApprovalDetailStepRecordDTO stepRecordDTO = BeanUtils.convert(approvalEmptyRecordApiDTO, ApprovalDetailStepRecordDTO.class);
            // 这边使用bpm返回的ApprovalRecordType，主要是为了区分抄送，前端可以把抄送过滤掉
            //stepRecordDTO.setApprovalRecordType("approval");
            stepRecordDTO.setRecordStatus(1);
            stepRecordDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "审批中" : "In review");
            //指定人员
            List<ApprovalUserInfoDTO> approvalUserDTOList = BeanUtils.convert(ApprovalUserInfoDTO.class, approvalEmptyRecordApiDTO.getApprovalUserInfoDTOS());
            approvalUserDTOList.forEach(item -> {
                item.setApprovalOperation("APPROVING");
            });
            stepRecordDTO.setApprovalUserInfoDTOS(approvalUserDTOList);

            //角色/指定人员处理
            //整体流程主节点信息
            List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList = new ArrayList<>();
            List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList = new ArrayList<>();
            //所有查询到的用户
            List<ApprovalUserInfoDTO> findUserList = stepRecordDTO.getApprovalUserInfoDTOS();

            ApprovalCreateUserApiDTO approvalCreateUserApiDTO = approvalEmptyRecordApiDTO.getApprovalCreateUserApiDTO();
            if (approvalCreateUserApiDTO == null) {
                approvalCreateUserApiDTO = new ApprovalCreateUserApiDTO();
            }

            ApprovalCreateUserDTO approvalCreateUserDTO = new ApprovalCreateUserDTO();
            stepRecordDTO.setApprovalCreateUserDTO(approvalCreateUserDTO);
            List<ApprovalUserInfoApiDTO> approvalUserInfoApiDTOS = approvalCreateUserApiDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(approvalUserInfoApiDTOS)) {
                approvalUserInfoApiDTOS = new ArrayList<>();
            }
            approvalCreateUserDTO.setApprovalUserInfos(BeanUtils.convert(ApprovalUserInfoDTO.class, approvalUserInfoApiDTOS));

            List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = new ArrayList<>();
            approvalCreateUserDTO.setApprovalCreateRoleUserDTOS(approvalCreateRoleUserDTOS);
            List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserApiDTOS = approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
            if (CollectionUtils.isEmpty(approvalCreateRoleUserApiDTOS)) {
                approvalCreateRoleUserApiDTOS = new ArrayList<>();
            }
            for (ApprovalCreateRoleUserApiDTO roleUserApiDTO : approvalCreateRoleUserApiDTOS) {
                ApprovalCreateRoleUserDTO roleUserDTO = new ApprovalCreateRoleUserDTO();
                roleUserDTO.setApprovalRole(roleUserApiDTO.getApprovalRole());
                roleUserDTO.setApprovalRoleName(roleUserApiDTO.getApprovalRoleName());
                roleUserDTO.setFetchObject(roleUserApiDTO.getFetchObject());
                roleUserDTO.setFetchObjectName(roleUserApiDTO.getFetchObjectName());
                roleUserDTO.setFetchObjectValue(roleUserApiDTO.getFetchObjectValue());
                roleUserDTO.setFetchObjectValueName(roleUserApiDTO.getFetchObjectValueName());
                roleUserDTO.setApprovalUserCodes(roleUserApiDTO.getApprovalUserCodes());
                if (CollectionUtils.isNotEmpty(roleUserApiDTO.getApprovalUserInfos())) {
                    roleUserDTO.setApprovalUserInfos(BeanUtils.convert(ApprovalUserInfoDTO.class, roleUserApiDTO.getApprovalUserInfos()));
                }
                approvalCreateRoleUserDTOS.add(roleUserDTO);
            }

            previewUserInfoFind(userCode, countryMap, hrUserCodeMap, userCodeMap, findUserList, approvalCreateUserDTO, approvalPreviewErrorUserDTOList, approvalPreviewSuccessUserDTOList);

            approvalCreateUserDTO.setApprovalPreviewErrorUserDTOList(approvalPreviewErrorUserDTOList);
            approvalCreateUserDTO.setApprovalPreviewSuccessUserDTOList(approvalPreviewSuccessUserDTOList);
            resultDTOList.add(stepRecordDTO);
            temp++;
        }
    }

    /**
     * 预览审批人员信息查询
     *
     * @param applyUserCode                     申请人
     * @param countryMap                        国家信息
     * @param hrUserCodeMap                     HR信息
     * @param userCodeMap                       用户信息
     * @param approvalUserInfoDTOS              所有审批人员
     * @param approvalCreateUserDTO             流程信息
     * @param approvalPreviewErrorUserDTOList   错误信息
     * @param approvalPreviewSuccessUserDTOList 正确信息
     */
    private void previewUserInfoFind(String applyUserCode, Map<String, List<CountryDTO>> countryMap, Map<String, List<UserInfoDetailApiDTO>> hrUserCodeMap,
                                     Map<String, List<UserInfoDetailApiDTO>> userCodeMap, List<ApprovalUserInfoDTO> approvalUserInfoDTOS, ApprovalCreateUserDTO approvalCreateUserDTO,
                                     List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList, List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList) {
        List<String> findUserCodeList = approvalUserInfoDTOS.stream().map(item -> item.getUserCode()).collect(Collectors.toList());
        //所有指定人员
        List<ApprovalUserInfoDTO> appointUserList = approvalCreateUserDTO.getApprovalUserInfos();
        if (CollectionUtils.isEmpty(appointUserList)) {
            appointUserList = new ArrayList<>();
        }
        for (ApprovalUserInfoDTO userInfoDTO : appointUserList) {
            List<UserInfoDetailApiDTO> userInfoDetailApiDTOList = userCodeMap.get(userInfoDTO.getUserCode());
            if (CollectionUtils.isEmpty(userInfoDetailApiDTOList)) {
                continue;
            }
            //获取HR信息
            String hrUserCode = countryMainPocMap.get(userInfoDetailApiDTOList.get(0).getOriginCountry());
            String userHrMessage = hrUserCode;
            if (StringUtils.isNotBlank(hrUserCode)) {
                List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                    userHrMessage = hrUserDetail.get(0).getUserName();
                }
            }
            if (findUserCodeList.contains(userInfoDTO.getUserCode())) {
                //指定人员不存在
                if (StringUtils.equalsIgnoreCase(userInfoDetailApiDTOList.get(0).getWorkStatus(), "DIMISSION")) {
                    ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? "指定员工  " + userInfoDetailApiDTOList.get(0).getUserName() + " 已经离职" : "The fixed approver " + userInfoDetailApiDTOList.get(0).getUserName() + " has resigned");
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    errorUserDTO.setTemp(1);
                    approvalPreviewErrorUserDTOList.add(errorUserDTO);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(userInfoDetailApiDTOList.get(0).getStatus(), "DISABLED")) {
                    ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? "指定员工 " + userInfoDetailApiDTOList.get(0).getUserName() + " 已经冻结" : "The fixed approver " + userInfoDetailApiDTOList.get(0).getUserName() + " has been disabled");
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    errorUserDTO.setTemp(1);
                    approvalPreviewErrorUserDTOList.add(errorUserDTO);
                    continue;
                }
                //指定人员存在
                ApprovalPreviewSuccessUserDTO successUserDTO = new ApprovalPreviewSuccessUserDTO();
                successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userInfoDetailApiDTOList.get(0).getUserName() + " 是指定员工" : userInfoDetailApiDTOList.get(0).getUserName() + " is fixed in process");
                successUserDTO.setUserHrMessage(userHrMessage);
                successUserDTO.setTemp(1);
                approvalPreviewSuccessUserDTOList.add(successUserDTO);
                //continue;
            }
        }

        //所有角色对应人员
        List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = approvalCreateUserDTO.getApprovalCreateRoleUserDTOS();
        if (CollectionUtils.isEmpty(approvalCreateRoleUserDTOS)) {
            approvalCreateRoleUserDTOS = new ArrayList<>();
        }
        //申请人HR相关信息
        //获取HR信息
        List<UserInfoDetailApiDTO> applyUserInfoDetailApiDTOList = userCodeMap.get(applyUserCode);
        if (CollectionUtils.isEmpty(applyUserInfoDetailApiDTOList)) {
            return;
        }

        for (ApprovalCreateRoleUserDTO roleUserDTO : approvalCreateRoleUserDTOS) {
            List<ApprovalUserInfoDTO> roleUserInfos = roleUserDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(roleUserInfos)) {
                //这个角色没有找到人
                ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                errorUserDTO.setApprovalRole(roleUserDTO.getApprovalRole());
                errorUserDTO.setFetchObject(roleUserDTO.getFetchObject());
                errorUserDTO.setFetchObjectValue(roleUserDTO.getFetchObjectValue());
                errorUserDTO.setTemp(2);
                approvalPreviewErrorUserDTOList.add(errorUserDTO);
                ApprovalRoleValueEnum approvalRoleValueEnum = ApprovalRoleValueEnum.getInstanceByCode(roleUserDTO.getFetchObject());
                ApprovalRoleEnum approvalRoleEnum = ApprovalRoleEnum.getInstanceByCode(roleUserDTO.getApprovalRole());
                if (approvalRoleValueEnum == null || approvalRoleEnum == null) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.BE_APPROVERED.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.APPLY_USER.getCode())) {
                    String userMessageCn = applyUserInfoDetailApiDTOList.get(0).getUserName() + " - " + approvalRoleEnum.getDescCN() + " 不存在";
                    // String userMessageEn = applyUserInfoDetailApiDTOList.get(0).getUserName() + " - " + approvalRoleEnum.getDescUS() + " not found";
                    // 由于枚举是bpm定义的，改不了，所以直接先写死汇报上级的英文
                    String userMessageEn = applyUserInfoDetailApiDTOList.get(0).getUserName() + " - " + "Line Manager" + " not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(applyUserInfoDetailApiDTOList.get(0).getOriginCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPT.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.STATION.getCode())) {
                    List<HrmsEntDeptDO> deptApiDTOList = hrmsDeptManage.selectDeptByIds(Collections.singletonList(Long.valueOf(roleUserDTO.getFetchObjectValue())));
                    if (CollectionUtils.isEmpty(deptApiDTOList)) {
                        continue;
                    }
                    List<CountryDTO> countryDTOList = countryMap.get(deptApiDTOList.get(0).getCountry());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameCn() + " - " + approvalRoleEnum.getDescCN() + " 不存在";
                    String userMessageEn = countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameEn() + " - " + approvalRoleEnum.getDescCN() + " not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(deptApiDTOList.get(0).getCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.CLEARING_THEME.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPARTURE_COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DESTINATION_COUNTRY.getCode())) {
                    List<CountryDTO> countryDTOList = countryMap.get(roleUserDTO.getFetchObjectValue());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN() + "不存在";
                    String userMessageEn = countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN() + "not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(countryDTOList.get(0).getCountryName());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                continue;
            }

            for (ApprovalUserInfoDTO userInfoDTO : roleUserInfos) {
                List<UserInfoDetailApiDTO> userInfoDetailApiDTOList = userCodeMap.get(userInfoDTO.getUserCode());
                if (CollectionUtils.isEmpty(userInfoDetailApiDTOList)) {
                    continue;
                }
                //这个角色找到人
                ApprovalPreviewSuccessUserDTO successUserDTO = new ApprovalPreviewSuccessUserDTO();
                successUserDTO.setApprovalRole(roleUserDTO.getApprovalRole());
                successUserDTO.setFetchObject(roleUserDTO.getFetchObject());
                successUserDTO.setFetchObjectValue(roleUserDTO.getFetchObjectValue());
                successUserDTO.setUserCode(userInfoDetailApiDTOList.get(0).getUserCode());
                successUserDTO.setTemp(2);
                approvalPreviewSuccessUserDTOList.add(successUserDTO);
                ApprovalRoleValueEnum approvalRoleValueEnum = ApprovalRoleValueEnum.getInstanceByCode(roleUserDTO.getFetchObject());
                ApprovalRoleEnum approvalRoleEnum = ApprovalRoleEnum.getInstanceByCode(roleUserDTO.getApprovalRole());
                if (approvalRoleValueEnum == null || approvalRoleEnum == null) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.BE_APPROVERED.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.APPLY_USER.getCode())) {
                    String userMessageCn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + approvalRoleEnum.getDescCN();
                    String userMessageEn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(applyUserInfoDetailApiDTOList.get(0).getOriginCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPT.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.STATION.getCode())) {
                    List<HrmsEntDeptDO> deptApiDTOList = hrmsDeptManage.selectDeptByIds(Collections.singletonList(Long.valueOf(roleUserDTO.getFetchObjectValue())));
                    if (CollectionUtils.isEmpty(deptApiDTOList)) {
                        continue;
                    }
                    List<CountryDTO> countryDTOList = countryMap.get(deptApiDTOList.get(0).getCountry());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameCn() + approvalRoleEnum.getDescCN();
                    String userMessageEn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameEn() + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(deptApiDTOList.get(0).getCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);

                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.CLEARING_THEME.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPARTURE_COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DESTINATION_COUNTRY.getCode())) {
                    List<CountryDTO> countryDTOList = countryMap.get(roleUserDTO.getFetchObjectValue());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN();
                    String userMessageEn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息

                    String hrUserCode = countryMainPocMap.get(countryDTOList.get(0).getCountryName());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);
                }
            }
        }
    }

    /**
     * 审批预览构建
     */
    private void previewDTOBuild(List<ApprovalEmptyRecordApiDTO> recordApiDTOList, List<ApprovalDetailStepRecordDTO> resultDTOList) {
        int temp = 1;
        for (ApprovalEmptyRecordApiDTO approvalEmptyRecordApiDTO : recordApiDTOList) {
            //发起人节点
            if (temp == 1) {
                ApprovalDetailStepRecordDTO createApprovalUserDTO = new ApprovalDetailStepRecordDTO();
                createApprovalUserDTO.setApprovalRecordType("approval");
                createApprovalUserDTO.setRecordStatus(-1);
                createApprovalUserDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                createApprovalUserDTO.setStepName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                createApprovalUserDTO.setRecordStatusUpdateDate(new Date());
                createApprovalUserDTO.setStepId("APPLY");
                List<ApprovalUserInfoDTO> approvalUserDTOList = BeanUtils.convert(ApprovalUserInfoDTO.class, approvalEmptyRecordApiDTO.getApprovalUserInfoDTOS());
                createApprovalUserDTO.setApprovalUserInfoDTOS(approvalUserDTOList);
                resultDTOList.add(createApprovalUserDTO);
                temp++;
                continue;
            }
            //非发起人节点
            ApprovalDetailStepRecordDTO stepRecordDTO = BeanUtils.convert(approvalEmptyRecordApiDTO, ApprovalDetailStepRecordDTO.class);
            stepRecordDTO.setApprovalRecordType("approval");
            stepRecordDTO.setRecordStatus(1);
            stepRecordDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "审批中" : "In review");
            //指定人员
            List<ApprovalUserInfoDTO> approvalUserDTOList = BeanUtils.convert(ApprovalUserInfoDTO.class, approvalEmptyRecordApiDTO.getApprovalUserInfoDTOS());
            approvalUserDTOList.forEach(item -> {
                item.setApprovalOperation("APPROVING");
            });
            stepRecordDTO.setApprovalUserInfoDTOS(approvalUserDTOList);

            ApprovalCreateUserApiDTO approvalCreateUserApiDTO = approvalEmptyRecordApiDTO.getApprovalCreateUserApiDTO();
            if (approvalCreateUserApiDTO == null) {
                approvalCreateUserApiDTO = new ApprovalCreateUserApiDTO();
            }

            ApprovalCreateUserDTO approvalCreateUserDTO = new ApprovalCreateUserDTO();
            stepRecordDTO.setApprovalCreateUserDTO(approvalCreateUserDTO);
            List<ApprovalUserInfoApiDTO> approvalUserInfoApiDTOS = approvalCreateUserApiDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(approvalUserInfoApiDTOS)) {
                approvalUserInfoApiDTOS = new ArrayList<>();
            }
            approvalCreateUserDTO.setApprovalUserInfos(BeanUtils.convert(ApprovalUserInfoDTO.class, approvalUserInfoApiDTOS));

            List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = new ArrayList<>();
            approvalCreateUserDTO.setApprovalCreateRoleUserDTOS(approvalCreateRoleUserDTOS);
            List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserApiDTOS = approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
            if (CollectionUtils.isEmpty(approvalCreateRoleUserApiDTOS)) {
                approvalCreateRoleUserApiDTOS = new ArrayList<>();
            }
            for (ApprovalCreateRoleUserApiDTO roleUserApiDTO : approvalCreateRoleUserApiDTOS) {
                ApprovalCreateRoleUserDTO roleUserDTO = new ApprovalCreateRoleUserDTO();
                roleUserDTO.setApprovalRole(roleUserApiDTO.getApprovalRole());
                roleUserDTO.setApprovalRoleName(roleUserApiDTO.getApprovalRoleName());
                roleUserDTO.setFetchObject(roleUserApiDTO.getFetchObject());
                roleUserDTO.setFetchObjectName(roleUserApiDTO.getFetchObjectName());
                roleUserDTO.setFetchObjectValue(roleUserApiDTO.getFetchObjectValue());
                roleUserDTO.setFetchObjectValueName(roleUserApiDTO.getFetchObjectValueName());
                roleUserDTO.setApprovalUserCodes(roleUserApiDTO.getApprovalUserCodes());
                if (CollectionUtils.isNotEmpty(roleUserApiDTO.getApprovalUserInfos())) {
                    roleUserDTO.setApprovalUserInfos(BeanUtils.convert(ApprovalUserInfoDTO.class, roleUserApiDTO.getApprovalUserInfos()));
                }
                approvalCreateRoleUserDTOS.add(roleUserDTO);
            }
            resultDTOList.add(stepRecordDTO);
            temp++;
        }
    }

    private void repeatRevokeCheck(Long formId) {
        List<Long> revokeFormIdList = hrmsApplicationFormRelationManage.selectRelationByRelationIdList(Arrays.asList(formId)).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.APPLICATION_FORM.getCode())).map(item -> item.getFormId()).collect(Collectors.toList());
        List<HrmsApplicationFormDO> revokeFormList = hrmsApplicationFormManage.selectByIdList(revokeFormIdList).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getFormStatus(), HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode()) || StringUtils.equalsIgnoreCase(item.getFormStatus(), HrAttendanceApplicationFormStatusEnum.PASS.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(revokeFormList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.NOT_REVOKE_REPEAT.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.NOT_REVOKE_REPEAT.getDesc()));
        }
    }


    private HrmsApplicationFormAttrDO insertAttrDOBuild(Long formId, String attrKey, String attrValue) {
        HrmsApplicationFormAttrDO applicationFormAttrDO = new HrmsApplicationFormAttrDO();
        applicationFormAttrDO.setId(IdWorkerUtil.getId());
        applicationFormAttrDO.setFormId(formId);
        applicationFormAttrDO.setAttrKey(attrKey);
        applicationFormAttrDO.setAttrValue(attrValue);
        BaseDOUtil.fillDOInsert(applicationFormAttrDO);
        return applicationFormAttrDO;
    }

    private void customFieldBuild(List<ApprovalTypeFieldApiDTO> fieldApiDTOList, String fieldType, String fieldValue, Map<String, String> fieldValueMap) {
        ApprovalTypeFieldApiDTO fieldApiDTO = new ApprovalTypeFieldApiDTO();
        fieldApiDTO.setFieldType(fieldType);
        fieldApiDTO.setFieldValue(fieldValue);
        fieldApiDTO.setFieldValueMap(fieldValueMap);
        fieldApiDTOList.add(fieldApiDTO);
    }

    private String getConfigClassItemInfo(List<HrmsAttendancePunchClassItemConfigDO> attendancePunchClassItemConfig) {
        StringBuilder stringBuilder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(attendancePunchClassItemConfig)) {
            for (HrmsAttendancePunchClassItemConfigDO itemConfigDO : attendancePunchClassItemConfig) {
                if (itemConfigDO.getPunchInTime() != null) {
                    stringBuilder.append(DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm")).append("-")
                            .append(DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm")).append("&");
                    continue;
                }
                stringBuilder.append(DateUtil.format(itemConfigDO.getEarliestPunchInTime(), "HH:mm")).append("-")
                        .append(DateUtil.format(itemConfigDO.getLatestPunchOutTime(), "HH:mm")).append("&");
            }
            if (stringBuilder.toString().length() > 0) {
                return stringBuilder.toString().substring(0, stringBuilder.toString().length() - 1);
            }
        }
        return null;
    }

    private void setEarAndLatePunchTime(Long punchClassId, Long dayId, Long userId
            , List<HrmsAttendancePunchClassItemConfigDO> attendancePunchClassItemConfig
            , UserDayReissueInfoVO userDayReissueInfoVO) {
        //设置最早最晚打卡时间
        List<Date> earliestPunchInTimeList = attendancePunchClassItemConfig.stream()
                .filter(item -> Objects.nonNull(item.getEarliestPunchInTime()))
                .map(HrmsAttendancePunchClassItemConfigDO::getEarliestPunchInTime)
                .sorted()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(earliestPunchInTimeList)) {
            userDayReissueInfoVO.setEarliestPunchTime(earliestPunchInTimeList.get(0));
        }
        List<Date> latestPunchOutTimeList = attendancePunchClassItemConfig.stream()
                .filter(item -> Objects.nonNull(item.getLatestPunchOutTime()))
                .map(HrmsAttendancePunchClassItemConfigDO::getLatestPunchOutTime)
                .sorted()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(latestPunchOutTimeList)) {
            userDayReissueInfoVO.setLatestPunchTime(latestPunchOutTimeList.get(latestPunchOutTimeList.size() - 1));
        }
        //休息日，则需要查询前后两天的最早最晚下班时间是否跨天
        if (Objects.isNull(punchClassId)) {
            //获取当前时间-1day,+1day的班次配置
            Long preDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(dayId.toString()), -1), "yyyyMMdd"));
            Long nextDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(dayId.toString()), 1), "yyyyMMdd"));
            List<HrmsAttendanceClassEmployeeConfigDO> employeeConfigDOS = classEmployeeConfigDao.selectRecordByDayList(userId, Arrays.asList(preDayId, nextDayId));
            Map<Long, List<HrmsAttendanceClassEmployeeConfigDO>> employeeConfigMap = employeeConfigDOS.stream().collect(Collectors.groupingBy(HrmsAttendanceClassEmployeeConfigDO::getDayId));
            List<HrmsAttendanceClassEmployeeConfigDO> preDayEmployeeConfig = employeeConfigMap.get(preDayId);
            List<HrmsAttendanceClassEmployeeConfigDO> nextDayEmployeeConfig = employeeConfigMap.get(nextDayId);
            //设置休息日最早上班时间
            Date earliestPunchTime = DateUtils.str2Date(dayId + " 00:00:00", DateFormatterUtil.FORMAT_YYYYMMDD_HH_MM_SS);
            userDayReissueInfoVO.setEarliestPunchTime(earliestPunchTime);
            //设置休息日最晚下班时间
            Date latestPunchTime = DateUtils.str2Date(dayId + " 23:59:59", DateFormatterUtil.FORMAT_YYYYMMDD_HH_MM_SS);
            userDayReissueInfoVO.setLatestPunchTime(latestPunchTime);
            //设置前一天最晚打卡时间为休息日最早打卡时间
            if (CollectionUtils.isNotEmpty(preDayEmployeeConfig) && Objects.nonNull(preDayEmployeeConfig.get(0).getClassId())) {
//                List<HrmsAttendancePunchClassItemConfigDO> preItemConfigDOS = hrmsAttendancePunchClassItemConfigDao.selectItemConfigByClassId(Arrays.asList(preDayEmployeeConfig.get(0).getClassId()));
                List<HrmsAttendancePunchClassItemConfigDO> preItemConfigDOS = punchConfigDaoFacade.getClassItemConfigAdapter().selectItemConfigByClassId(Arrays.asList(preDayEmployeeConfig.get(0).getClassId()));
                hrmsAttendanceMobilePunchService.transferItemConfigTimeFormat(preItemConfigDOS, preDayId);
                List<Date> prelatestPunchOutTimeList = preItemConfigDOS.stream()
                        .filter(item -> Objects.nonNull(item.getLatestPunchOutTime()))
                        .map(HrmsAttendancePunchClassItemConfigDO::getLatestPunchOutTime)
                        .sorted()
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(prelatestPunchOutTimeList)) {
                    Date date = prelatestPunchOutTimeList.get(prelatestPunchOutTimeList.size() - 1);
                    //如果上一天的最晚打卡时间和当前时间dayId一致，则取前一天最晚打卡时间为休息日最早打卡时间
                    if (dayId.equals(Long.valueOf(DateUtil.format(date, DateFormatterUtil.FORMAT_YYYYMMDD)))) {
                        userDayReissueInfoVO.setEarliestPunchTime(date);
                    }
                }
            }
            //设置后一天最早打卡时间为休息日最晚打卡时间
            if (CollectionUtils.isNotEmpty(nextDayEmployeeConfig) && Objects.nonNull(nextDayEmployeeConfig.get(0).getClassId())) {
//                List<HrmsAttendancePunchClassItemConfigDO> nextItemConfigDOS = hrmsAttendancePunchClassItemConfigDao.selectItemConfigByClassId(Arrays.asList(nextDayEmployeeConfig.get(0).getClassId()));
                List<HrmsAttendancePunchClassItemConfigDO> nextItemConfigDOS = punchConfigDaoFacade.getClassItemConfigAdapter().selectItemConfigByClassId(Arrays.asList(nextDayEmployeeConfig.get(0).getClassId()));
                hrmsAttendanceMobilePunchService.transferItemConfigTimeFormat(nextItemConfigDOS, nextDayId);
                List<Date> nextEarlistPunchOutTimeList = nextItemConfigDOS.stream()
                        .filter(item -> Objects.nonNull(item.getEarliestPunchInTime()))
                        .map(HrmsAttendancePunchClassItemConfigDO::getEarliestPunchInTime)
                        .sorted()
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(nextEarlistPunchOutTimeList)) {
                    Date date = nextEarlistPunchOutTimeList.get(0);
                    //如果上一天的最晚打卡时间和当前时间dayId一致，则取前一天最晚打卡时间为休息日最早打卡时间
                    if (dayId.equals(Long.valueOf(DateUtil.format(date, DateFormatterUtil.FORMAT_YYYYMMDD)))) {
                        userDayReissueInfoVO.setLatestPunchTime(date);
                    }
                }
            }
        }
    }

    private void filterUserLeaveCompanyConfig(List<HrmsCompanyLeaveConfigDO> companyLeaveConfigList, HrmsUserInfoDO userInfo) {
        // 根据请假规则筛选可以选择的假期类型
        HrmsUserEntryRecordDO userEntryRecordDO = userEntryRecordManage.getByUserId(userInfo.getId());
        List<UserDynamicInfoDTO> userDynamicInfo = userService.listUserDynamicInfo(Lists.newArrayList(userInfo.getUserCode())
                , Lists.newArrayList(UserDynamicFieldEnum.IS_PROBATION_PASS));
        Boolean isProbationPass;
        // 获取转正标识
        if (CollectionUtils.isNotEmpty(userDynamicInfo)) {
            String result = userDynamicInfo.get(0)
                    .getDynamicFieldMap()
                    .get(UserDynamicFieldEnum.IS_PROBATION_PASS.getKey());
            isProbationPass = StringUtils.isBlank(result) ? true : Boolean.parseBoolean(result);
        } else {
            isProbationPass = Boolean.TRUE;
        }
        // 通过请假条件筛选用户可选的假期
        if (Objects.isNull(userEntryRecordDO)) {
            return;
        }
        companyLeaveConfigList.removeIf(item -> {
            if (Objects.isNull(userEntryRecordDO.getConfirmDate())) {
                return false;
            }
            Integer restriction = item.getLeaveUsageRestrictions();
            if (Objects.nonNull(restriction) && restriction > BusinessConstant.ZERO) {
                AttendanceLeaveRestrictionEnum isInvalidEnum = AttendanceLeaveRestrictionEnum.getIsInvalidEnum(restriction);
                switch (isInvalidEnum) {
                    case ONE_YEAR:
                        return !DateFormatterUtil.compareOfYear(userEntryRecordDO.getConfirmDate(), Calendar.getInstance().getTime(), 1);
                    case TWO_YEAR:
                        return !DateFormatterUtil.compareOfYear(userEntryRecordDO.getConfirmDate(), Calendar.getInstance().getTime(), 2);
                    case REGULAR:
                        return !isProbationPass;
                    default:
                        return false;
                }
            }
            return false;
        });
    }
}
