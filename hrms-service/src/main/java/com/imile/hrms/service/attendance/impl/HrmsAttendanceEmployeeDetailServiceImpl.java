package com.imile.hrms.service.attendance.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.CertificateTypeEnum;
import com.imile.hrms.common.enums.DimissionStatusEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormAttrKeyEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormStatusEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormTypeEnum;
import com.imile.hrms.common.enums.attendance.*;
import com.imile.hrms.common.enums.leave.LeaveCodeAssociateEnum;
import com.imile.hrms.common.enums.punch.AttendancePunchTypeEnum;
import com.imile.hrms.common.enums.punch.PunchConfigTypeEnum;
import com.imile.hrms.common.enums.punch.PunchDayTypeEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.DateConvertUtils;
import com.imile.hrms.common.util.HrmsDateUtil;
import com.imile.hrms.dao.approval.model.HrmsApplicationFormAttrDO;
import com.imile.hrms.dao.approval.model.HrmsApplicationFormDO;
import com.imile.hrms.dao.approval.query.ApplicationFormQuery;
import com.imile.hrms.dao.attendance.bo.UserAttendanceBO;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceCycleConfigDao;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceEmployeeDetailDao;
import com.imile.hrms.dao.attendance.dto.*;
import com.imile.hrms.dao.attendance.mapper.HrmsAttendanceEmployeeDetailMapper;
import com.imile.hrms.dao.attendance.model.*;
import com.imile.hrms.dao.attendance.query.*;
import com.imile.hrms.dao.base.dao.HrmsBaseDayInfoDao;
import com.imile.hrms.dao.base.model.HrmsBaseDayInfoDO;
import com.imile.hrms.dao.organization.dao.HrmsEntCompanyDao;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.dao.HrmsEntPostDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsEntPostDO;
import com.imile.hrms.dao.punch.dao.*;
import com.imile.hrms.dao.punch.model.*;
import com.imile.hrms.dao.punch.query.AttendancePunchConfigRangeByDateQuery;
import com.imile.hrms.dao.punch.query.EmployeePunchCardRecordQuery;
import com.imile.hrms.dao.salary.dao.HrmsSalaryConfigDao;
import com.imile.hrms.dao.salary.dao.HrmsSalaryEmployeeConfigDao;
import com.imile.hrms.dao.user.dao.*;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import com.imile.hrms.dao.user.dto.UserInformationDTO;
import com.imile.hrms.dao.user.mapper.HrmsUserInfoMapper;
import com.imile.hrms.dao.user.model.*;
import com.imile.hrms.integration.dict.service.DictService;
import com.imile.hrms.integration.dict.vo.DictVO;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.hrms.manage.approval.HrmsApplicationFormAttrManage;
import com.imile.hrms.manage.approval.HrmsApplicationFormManage;
import com.imile.hrms.manage.attendance.HrmsAttendanceEmployeeDetailManage;
import com.imile.hrms.manage.attendance.HrmsEmployeeAbnormalAttendanceManage;
import com.imile.hrms.manage.attendance.HrmsEmployeeAbnormalItemManage;
import com.imile.hrms.manage.attendance.HrmsEmployeeAbnormalOperationRecordManage;
import com.imile.hrms.manage.company.HrmsCompanyLeaveConfigManage;
import com.imile.hrms.manage.newAttendance.calendar.adapter.CalendarManageAdapter;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigDaoFacade;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigManageAdapter;
import com.imile.hrms.manage.punch.HrmsAttendanceClassEmployeeConfigManage;
import com.imile.hrms.manage.resource.SystemResourceManage;
import com.imile.hrms.manage.salary.behavior.HandlerStrategyContext;
import com.imile.hrms.manage.user.*;
import com.imile.hrms.service.approval.dto.AbnormalExtendDTO;
import com.imile.hrms.service.approval.dto.DayDurationInfoDTO;
import com.imile.hrms.service.approval.dto.DayNormalPunchTimeDTO;
import com.imile.hrms.service.approval.dto.DayPunchTimeDTO;
import com.imile.hrms.service.attendance.HrmsAttendanceBaseService;
import com.imile.hrms.service.attendance.HrmsAttendanceEmployeeDetailService;
import com.imile.hrms.service.attendance.dto.*;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.organization.HrmsBaseDataService;
import com.imile.hrms.service.organization.result.HrmsSettlementCenterVO;
import com.imile.hrms.service.punch.HrmsAttendanceMobilePunchService;
import com.imile.hrms.service.user.HrmsUserLeaveService;
import com.imile.hrms.service.user.UserResourceService;
import com.imile.hrms.service.user.dto.UserAvailableLeaveDTO;
import com.imile.hrms.service.user.transaction.UserLeaveTransaction;
import com.imile.hrms.service.user.vo.PermissionCountryDeptVO;
import com.imile.oa.api.hrms.api.BizApplicationFormApi;
import com.imile.oa.api.hrms.dto.BusinessTravelFormApiDTO;
import com.imile.oa.api.hrms.query.ApplicationFormApiQuery;
import com.imile.util.BeanUtils;
import com.imile.util.date.DateUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 出勤service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HrmsAttendanceEmployeeDetailServiceImpl extends BaseService implements HrmsAttendanceEmployeeDetailService {

    @Autowired
    private SystemResourceManage systemResourceManage;
    @Autowired
    private HrmsAttendanceEmployeeDetailMapper hrmsAttendanceEmployeeDetailMapper;
    @Autowired
    private HrmsUserInfoMapper hrmsUserInfoMapper;
    @Autowired
    private HrmsAttendanceEmployeeDetailDao hrmsAttendanceEmployeeDetailDao;
    @Autowired
    private HrmsAttendanceEmployeeDetailManage hrmsAttendanceEmployeeDetailManage;
    @Autowired
    private CalendarManageAdapter calendarManageAdapter;
    @Autowired
    private HrmsBaseDayInfoDao hrmsBaseDayInfoDao;
    @Autowired
    private HrmsUserEntryRecordDao hrmsUserEntryRecordDao;
    @Autowired
    private HrmsUserTransferRecordDao hrmsUserTransferRecordDao;
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Autowired
    private HrmsEntDeptDao hrmsEntDeptDao;
    @Autowired
    private HrmsEntPostDao hrmsEntPostDao;
    @Autowired
    private HrmsEntCompanyDao hrmsEntCompanyDao;
    @Autowired
    private HrmsUserLeaveService hrmsUserLeaveService;
    @Autowired
    private HrmsUserLeaveRecordManage hrmsUserLeaveRecordManage;
    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Autowired
    private UserLeaveTransaction userLeaveTransaction;
    @Autowired
    private ImileRedisClient redissonClient;
    @Autowired
    private HrmsEmployeeAbnormalAttendanceManage hrmsEmployeeAbnormalAttendanceManage;
    @Autowired
    private HrmsUserLeaveDetailManage hrmsUserLeaveDetailManage;
    @Autowired
    private HrmsUserLeaveStageDetailManage userLeaveStageDetailManage;
    @Autowired
    private HrmsUserDimissionRecordManage userDimissionRecordManage;
    @Autowired
    private HandlerStrategyContext handlerStrategyContext;
    @Autowired
    private HrmsUserDimissionRecordDao hrmsUserDimissionRecordDao;
    @Autowired
    private HrmsUserEntryRecordManage hrmsUserEntryRecordManage;
    @Autowired
    private HrmsUserCertificateManage hrmsUserCertificateManage;
    @Autowired
    private HrmsEmployeeAbnormalItemManage hrmsEmployeeAbnormalItemManage;
    @Autowired
    private HrmsAttendanceClassEmployeeConfigDao classEmployeeConfigDao;
    @Autowired
    private HrmsAttendanceClassRecordDao hrmsAttendanceClassRecordDao;
    @Autowired
    private HrmsSalaryEmployeeConfigDao salaryEmployeeConfigDao;
    @Autowired
    private HrmsSalaryConfigDao salaryConfigDao;
    @Autowired
    private HrmsCountryConfigDao hrmsCountryConfigDao;
    @Autowired
    private DictService dictService;
    @Autowired
    private HrmsApplicationFormManage hrmsApplicationFormManage;
    @Autowired
    private HrmsApplicationFormAttrManage hrmsApplicationFormAttrManage;
    @Autowired
    private HrmsEmployeeAbnormalOperationRecordManage hrmsEmployeeAbnormalOperationRecordManage;
    @Autowired
    private HrmsAttendanceClassEmployeeConfigManage hrmsAttendanceClassEmployeeConfigManage;
    @Autowired
    private HrmsAttendanceBaseService hrmsAttendanceBaseService;
    @Autowired
    private EmployeePunchRecordDao punchRecordDao;
    @Autowired
    private UserResourceService userResourceService;
    @Autowired
    private HrmsCompanyLeaveConfigManage hrmsCompanyLeaveConfigManage;
    @Autowired
    private HrmsBaseDataService hrmsBaseDataService;
//    @Autowired
//    private HrmsAttendancePunchClassItemConfigDao hrmsAttendancePunchClassItemConfigDao;
    @Autowired
    private HrmsAttendanceMobilePunchService hrmsAttendanceMobilePunchService;
    @Autowired
    private HrmsWarehouseDetailDao warehouseDetailDao;
    @Autowired
    private HrmsWarehouseAttendanceConfigDao warehouseAttendanceConfigDao;
    @Resource
    private PunchConfigDaoFacade punchConfigDaoFacade;
    @Resource
    private PunchConfigManageAdapter punchConfigManageAdapter;

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private BizApplicationFormApi bizApplicationFormApi;

    private static final Long DEFAULT_MOUTH_RANGE = 2L;
    private static final String DEFAULT_DATE_FORMAT_STR = "yyyy/MM/dd";
    private static final String NEED_PRESENT = "PRESENT";
    private static final String NEED_LEAVE = "LEAVE";


    private static final Integer DECEMBER = 12;
    private static final Integer JANUARY = 1;
    private static final Integer IN = 1;
    private static final Integer OUT = 0;
    /**
     * 一天最多24小时，即出勤加上加班不能
     */
    private static final BigDecimal MAX_DAY_HOURS = new BigDecimal("24");
    private static final BigDecimal MAX_ATTENDANCE_HOURS = new BigDecimal("8");
    private static final BigDecimal HALF_ATTENDANCE_HOURS = new BigDecimal("4");
    private static final BigDecimal TOTAL_MONTH_DAYS = new BigDecimal("30");


    private static final String SYNC_LOCK = "HRMS:LOCK:ATTENDANCE_SYNC:";

    @Resource
    private UserTransferManage userTransferManage;

    @Autowired
    private HrmsAttendanceCycleConfigDao hrmsAttendanceCycleConfigDao;


    @Override
    public List<ConcreteTypeDetailDTO> selectConcreteType(AttendanceConcreteTypeDTO concreteTypeDTO) {
        HrmsUserInfoDO userInfoDO = hrmsUserInfoManage.getUserInfoById(concreteTypeDTO.getUserId());
        if (userInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        List<ConcreteTypeDetailDTO> resultList = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase(concreteTypeDTO.getUpdateType(), AttendanceUpdateTypeEnum.OFF.getCode())) {
            //OFF
            List<AttendanceConcreteTypeEnum> concreteTypeEnumList = AttendanceConcreteTypeEnum.getInstanceByType(AttendanceUpdateTypeEnum.OFF.getCode());
            for (AttendanceConcreteTypeEnum concreteTypeEnum : concreteTypeEnumList) {
                ConcreteTypeDetailDTO concreteTypeDetailDTO = new ConcreteTypeDetailDTO();
                concreteTypeDetailDTO.setUpdateType(concreteTypeEnum.getAttendanceType());
                concreteTypeDetailDTO.setConcreteType(concreteTypeEnum.getCode());
                concreteTypeDetailDTO.setIsShouldAttendanceHours(concreteTypeEnum.getIsShouldAttendanceHours());
                concreteTypeDetailDTO.setIsAttendanceHours(concreteTypeEnum.getIsAttendanceHours());
                concreteTypeDetailDTO.setIsOvertimeHours(concreteTypeEnum.getIsOvertimeHours());
                concreteTypeDetailDTO.setIsLeaveHours(concreteTypeEnum.getIsLeaveHours());
                concreteTypeDetailDTO.setIsMaterialDetail(concreteTypeEnum.getIsMaterialDetail());
                concreteTypeDetailDTO.setDesc(RequestInfoHolder.isChinese() ? concreteTypeEnum.getDesc() : concreteTypeEnum.getDescEn());
                resultList.add(concreteTypeDetailDTO);
            }
            return resultList;
        }
        if (StringUtils.equalsIgnoreCase(concreteTypeDTO.getUpdateType(), AttendanceUpdateTypeEnum.PH.getCode())) {
            //PH
            List<AttendanceConcreteTypeEnum> concreteTypeEnumList = AttendanceConcreteTypeEnum.getInstanceByType(AttendanceUpdateTypeEnum.PH.getCode());
            for (AttendanceConcreteTypeEnum concreteTypeEnum : concreteTypeEnumList) {
                ConcreteTypeDetailDTO concreteTypeDetailDTO = new ConcreteTypeDetailDTO();
                concreteTypeDetailDTO.setUpdateType(concreteTypeEnum.getAttendanceType());
                concreteTypeDetailDTO.setConcreteType(concreteTypeEnum.getCode());
                concreteTypeDetailDTO.setIsShouldAttendanceHours(concreteTypeEnum.getIsShouldAttendanceHours());
                concreteTypeDetailDTO.setIsAttendanceHours(concreteTypeEnum.getIsAttendanceHours());
                concreteTypeDetailDTO.setIsOvertimeHours(concreteTypeEnum.getIsOvertimeHours());
                concreteTypeDetailDTO.setIsLeaveHours(concreteTypeEnum.getIsLeaveHours());
                concreteTypeDetailDTO.setIsMaterialDetail(concreteTypeEnum.getIsMaterialDetail());
                concreteTypeDetailDTO.setDesc(RequestInfoHolder.isChinese() ? concreteTypeEnum.getDesc() : concreteTypeEnum.getDescEn());
                resultList.add(concreteTypeDetailDTO);
            }
            return resultList;
        }
        if (StringUtils.equalsIgnoreCase(concreteTypeDTO.getUpdateType(), AttendanceUpdateTypeEnum.PRESENT.getCode())) {
            //出勤
            List<AttendanceConcreteTypeEnum> concreteTypeEnumList = AttendanceConcreteTypeEnum.getInstanceByType(AttendanceUpdateTypeEnum.PRESENT.getCode());
            for (AttendanceConcreteTypeEnum concreteTypeEnum : concreteTypeEnumList) {
                ConcreteTypeDetailDTO concreteTypeDetailDTO = new ConcreteTypeDetailDTO();
                concreteTypeDetailDTO.setUpdateType(concreteTypeEnum.getAttendanceType());
                concreteTypeDetailDTO.setConcreteType(concreteTypeEnum.getCode());
                concreteTypeDetailDTO.setIsShouldAttendanceHours(concreteTypeEnum.getIsShouldAttendanceHours());
                concreteTypeDetailDTO.setIsAttendanceHours(concreteTypeEnum.getIsAttendanceHours());
                concreteTypeDetailDTO.setIsOvertimeHours(concreteTypeEnum.getIsOvertimeHours());
                concreteTypeDetailDTO.setIsLeaveHours(concreteTypeEnum.getIsLeaveHours());
                concreteTypeDetailDTO.setIsMaterialDetail(concreteTypeEnum.getIsMaterialDetail());
                concreteTypeDetailDTO.setDesc(RequestInfoHolder.isChinese() ? concreteTypeEnum.getDesc() : concreteTypeEnum.getDescEn());
                resultList.add(concreteTypeDetailDTO);
            }
            return resultList;
        }
        if (StringUtils.equalsIgnoreCase(concreteTypeDTO.getUpdateType(), AttendanceUpdateTypeEnum.ABSENT.getCode())) {
            //缺勤
            List<AttendanceConcreteTypeEnum> concreteTypeEnumList = AttendanceConcreteTypeEnum.getInstanceByType(AttendanceUpdateTypeEnum.ABSENT.getCode());
            for (AttendanceConcreteTypeEnum concreteTypeEnum : concreteTypeEnumList) {
                ConcreteTypeDetailDTO concreteTypeDetailDTO = new ConcreteTypeDetailDTO();
                concreteTypeDetailDTO.setUpdateType(concreteTypeEnum.getAttendanceType());
                concreteTypeDetailDTO.setConcreteType(concreteTypeEnum.getCode());
                concreteTypeDetailDTO.setIsShouldAttendanceHours(concreteTypeEnum.getIsShouldAttendanceHours());
                concreteTypeDetailDTO.setIsAttendanceHours(concreteTypeEnum.getIsAttendanceHours());
                concreteTypeDetailDTO.setIsOvertimeHours(concreteTypeEnum.getIsOvertimeHours());
                concreteTypeDetailDTO.setIsLeaveHours(concreteTypeEnum.getIsLeaveHours());
                concreteTypeDetailDTO.setIsMaterialDetail(concreteTypeEnum.getIsMaterialDetail());
                concreteTypeDetailDTO.setDesc(RequestInfoHolder.isChinese() ? concreteTypeEnum.getDesc() : concreteTypeEnum.getDescEn());
                resultList.add(concreteTypeDetailDTO);
            }
            return resultList;
        }
        if (StringUtils.equalsIgnoreCase(concreteTypeDTO.getUpdateType(), AttendanceUpdateTypeEnum.LEAVE.getCode())) {
            //请假
            //查询该员工还可以请的假期类型
            List<UserAvailableLeaveDTO> availableLeaveDTOList = hrmsUserLeaveService.selectUserOwnLeaveType(concreteTypeDTO.getUserId(), concreteTypeDTO.getUserCode());
            List<String> userLeaverTypeList = availableLeaveDTOList.stream().map(record -> record.getLeaveType()).collect(Collectors.toList());
            for (String s : userLeaverTypeList) {
                LeaveCodeAssociateEnum leaveCodeAssociateEnum = LeaveCodeAssociateEnum.getInstanceByFullCode(s);
                if (leaveCodeAssociateEnum != null) {
                    AttendanceConcreteTypeEnum concreteTypeEnum = AttendanceConcreteTypeEnum.getInstanceByCode(leaveCodeAssociateEnum.getShortCode());
                    ConcreteTypeDetailDTO concreteTypeDetailDTO = new ConcreteTypeDetailDTO();
                    concreteTypeDetailDTO.setUpdateType(concreteTypeEnum.getAttendanceType());
                    concreteTypeDetailDTO.setConcreteType(concreteTypeEnum.getCode());
                    concreteTypeDetailDTO.setIsShouldAttendanceHours(concreteTypeEnum.getIsShouldAttendanceHours());
                    concreteTypeDetailDTO.setIsAttendanceHours(concreteTypeEnum.getIsAttendanceHours());
                    concreteTypeDetailDTO.setIsOvertimeHours(concreteTypeEnum.getIsOvertimeHours());
                    concreteTypeDetailDTO.setIsLeaveHours(concreteTypeEnum.getIsLeaveHours());
                    concreteTypeDetailDTO.setIsMaterialDetail(concreteTypeEnum.getIsMaterialDetail());
                    concreteTypeDetailDTO.setDesc(RequestInfoHolder.isChinese() ? concreteTypeEnum.getDesc() : concreteTypeEnum.getDescEn());
                    resultList.add(concreteTypeDetailDTO);
                }
            }
            return resultList;
        }

        return resultList;
    }

    @Override
    public UserAttendanceDTO detailUserAttendanceDTO(AttendanceEmployeeDetailQuery query) {
        //userId为空则直接返回
        if (query.getUserId() == null) {
            return null;
        }
        HrmsUserInfoDO userInfoDO = hrmsUserInfoManage.getUserInfoById(query.getUserId());
        if (userInfoDO == null || StringUtils.isBlank(userInfoDO.getUserCode())) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        UserInformationDTO userInformationDTO = hrmsUserInfoMapper.getUserInformationDTO(query.getUserId());
        //员工不存在则抛出异常
        if (userInformationDTO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        Date date = DateUtil.parse(query.getYear().toString(), "yyyy");
        //查询调入调出离职状态
        // 查询入职和调入时间
//        List<DateRange> dateRanges = getDateRange(userInfoDO);
        //处理国际化
        handlerInformation(userInformationDTO);
        //结果类封装
        UserAttendanceDTO userAttendanceDTO = BeanUtil.copyProperties(userInformationDTO, UserAttendanceDTO.class);

        //传递月份 则按月查询
        if (Objects.nonNull(query.getMonth()) && query.getMonth() > 0) {
            return baseDataHandlerByMonth(query, userInfoDO, userAttendanceDTO);
        }

        //查询员工考勤明细
        List<HrmsAttendanceEmployeeDetailDO> hrmsAttendanceEmployeeDetailDOS = hrmsAttendanceEmployeeDetailManage.selectByYear(query.getUserId(), query.getYear());

        List<Long> formIdList = hrmsAttendanceEmployeeDetailDOS.stream().filter(item -> item.getFormId() != null).map(item -> item.getFormId()).collect(Collectors.toList());
        List<HrmsApplicationFormAttrDO> formAttrDOList = hrmsApplicationFormAttrManage.selectFormAttrByFormIdLit(formIdList);

//        List<HrmsAttendanceConfigDetailDO> attendanceConfigDetailDOList = hrmsAttendanceConfigManage.getRangeRecords(query.getUserId(), query.getYear(), userInfoDO.getLocationCountry());
        List<HrmsAttendanceConfigDetailDO> attendanceConfigDetailDOList = calendarManageAdapter.getRangeRecords(query.getUserId(), query.getYear(), userInfoDO.getLocationCountry());
        if (CollectionUtils.isEmpty(attendanceConfigDetailDOList)) {
            int year = Math.toIntExact(query.getYear());
            throw BusinessException.get(HrmsErrorCodeEnums.ATTENDANCE_CALENDAR_NOT_EXIST.getDesc(), I18nUtils.getMessage(HrmsErrorCodeEnums.ATTENDANCE_CALENDAR_NOT_EXIST.getDesc(), year, year));
        }
        //判断该考勤方案是默认的还是部门/个人的
//        HrmsAttendanceConfigDO attendanceConfigDO = hrmsAttendanceConfigManage.getActiveById(attendanceConfigDetailDOList.get(0).getAttendanceConfigId());
        HrmsAttendanceConfigDO attendanceConfigDO = calendarManageAdapter.getActiveById(attendanceConfigDetailDOList.get(0).getAttendanceConfigId());

        //获取用户改年的所有的打卡记录
        EmployeePunchCardRecordQuery employeePunchCardRecordQuery = new EmployeePunchCardRecordQuery();
        employeePunchCardRecordQuery.setStartTime(DateUtil.beginOfYear(date));
        employeePunchCardRecordQuery.setEndTime(DateUtil.endOfYear(date));
        employeePunchCardRecordQuery.setUserCode(userInfoDO.getUserCode());
        List<EmployeePunchRecordDO> allPunchRecordList = punchRecordDao.listRecords(employeePunchCardRecordQuery);
        List<UserPunchRecordDTO> userPunchRecordDTOList = new ArrayList<>();
        for (EmployeePunchRecordDO employeePunchRecordDO : allPunchRecordList) {
            UserPunchRecordDTO userPunchRecordDTO = new UserPunchRecordDTO();
            userPunchRecordDTO.setId(employeePunchRecordDO.getId());
            userPunchRecordDTO.setUserCode(employeePunchRecordDO.getUserCode());
            userPunchRecordDTO.setFormId(employeePunchRecordDO.getFormId());
            String punchTimeString = DateUtil.format(employeePunchRecordDO.getPunchTime(), "yyyy-MM-dd HH:mm");
            userPunchRecordDTO.setFormatPunchTime(DateUtil.parse(punchTimeString + ":00", "yyyy-MM-dd HH:mm:ss"));
            userPunchRecordDTOList.add(userPunchRecordDTO);
        }
        //获取用户改年的所有审批通过的请假/外勤单据
        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setUserId(query.getUserId());
        applicationFormQuery.setStatusList(HrAttendanceApplicationFormStatusEnum.getAttendanceCodeList());
        applicationFormQuery.setFromTypeList(HrAttendanceApplicationFormTypeEnum.getAttendanceCodeList());
        List<HrmsApplicationFormDO> passAndInViewFormDOList = hrmsApplicationFormManage.selectForm(applicationFormQuery);
        List<Long> passAndInViewFormIdList = passAndInViewFormDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsApplicationFormAttrDO> passAndInViewFormAttrDOList = hrmsApplicationFormAttrManage.selectFormAttrByFormIdLit(passAndInViewFormIdList);

        //查询员工改年的所有异常(包含处理和未处理的)
        List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectAbnormalByUserId(query.getUserId(), Long.valueOf(DateUtil.format(DateUtil.beginOfYear(date), "yyyyMMdd")), Long.valueOf(DateUtil.format(DateUtil.endOfYear(date), "yyyyMMdd")));
        List<Long> abnormalIdList = abnormalAttendanceDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = hrmsEmployeeAbnormalOperationRecordManage.selectByAbnormalList(abnormalIdList);

        //查询员工改年的所有排班，然后查出打卡规则（需要获取当天的法定工作时长）
        List<HrmsAttendanceClassEmployeeConfigDO> classEmployeeConfigDOList = hrmsAttendanceClassEmployeeConfigManage.selectRecordByUserIdList(Arrays.asList(query.getUserId()), Long.valueOf(DateUtil.format(DateUtil.beginOfYear(date), "yyyyMMdd")), Long.valueOf(DateUtil.format(DateUtil.endOfYear(date), "yyyyMMdd")));
        List<Long> classIdList = classEmployeeConfigDOList.stream().filter(item -> item.getClassId() != null).map(item -> item.getClassId()).collect(Collectors.toList());
//        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = hrmsAttendancePunchClassConfigManage.selectClassByIdList(classIdList);
        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = punchConfigManageAdapter.selectClassByIdList(classIdList);
//        List<HrmsAttendancePunchClassItemConfigDO> classItemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(classIdList);
        List<HrmsAttendancePunchClassItemConfigDO> classItemConfigDOList = punchConfigManageAdapter.selectItemConfigByClassId(classIdList);
        List<Long> punchConfigIdList = classEmployeeConfigDOList.stream().filter(item -> item.getPunchConfigId() != null).map(item -> item.getPunchConfigId()).collect(Collectors.toList());
//        List<HrmsAttendancePunchConfigDO> punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByIdList(punchConfigIdList);
        List<HrmsAttendancePunchConfigDO> punchConfigDOList = punchConfigManageAdapter.selectAttendancePunchByIdList(punchConfigIdList);

        //不能根据正常考勤表遍历，需要根据整年的日历遍历，一天没有正常，全是异常，也是可以计算出来时间的
//        Map<Long, HrmsAttendanceConfigDetailDO> configDetailMap = hrmsAttendanceConfigManage.getRangeRecords(query.getUserId(), query.getYear(), userInfoDO.getLocationCountry()).stream().collect(Collectors.toMap(HrmsAttendanceConfigDetailDO::getDayId, item -> item, (oldVar, newVar) -> oldVar));
        Map<Long, HrmsAttendanceConfigDetailDO> configDetailMap = calendarManageAdapter.getRangeRecords(query.getUserId(), query.getYear(), userInfoDO.getLocationCountry()).stream().collect(Collectors.toMap(HrmsAttendanceConfigDetailDO::getDayId, item -> item, (oldVar, newVar) -> oldVar));
        BaseDayQuery baseDayQuery = BeanUtil.copyProperties(query, BaseDayQuery.class);
        Map<Integer, List<HrmsBaseDayInfoDO>> dayMap = hrmsBaseDayInfoDao.getBaseDay(baseDayQuery).stream().collect(Collectors.groupingBy(HrmsBaseDayInfoDO::getMonth));

        //免打卡需要用到
        AttendancePunchConfigRangeByDateQuery configRangeByDateQuery = new AttendancePunchConfigRangeByDateQuery();
        configRangeByDateQuery.setUserIds(Arrays.asList(query.getUserId()));
//        List<HrmsAttendancePunchConfigRangeDO> dayPunchConfigRangeDOS = hrmsAttendancePunchConfigRangeManage.selectPunchConfigRangeByDate(configRangeByDateQuery);
        List<HrmsAttendancePunchConfigRangeDO> dayPunchConfigRangeDOS = punchConfigManageAdapter.selectPunchConfigRangeByDate(configRangeByDateQuery);

        //年明细考勤记录
        UserYearAttendanceDTO userYearAttendanceDTO = new UserYearAttendanceDTO();
        userYearAttendanceDTO.setYear(query.getYear());
        List<UserMonthAttendanceDTO> userMonthAttendances = new ArrayList<>();
        userYearAttendanceDTO.setUserMonthAttendances(userMonthAttendances);
        for (int month = JANUARY; month <= DECEMBER; month++) {
            List<HrmsBaseDayInfoDO> baseDays = dayMap.get(month);
            UserMonthAttendanceDTO userMonthAttendanceDTO = new UserMonthAttendanceDTO();
            userMonthAttendanceDTO.setMonth((long) month);
            List<AttendanceDetailDTO> res = new ArrayList<>();
            userMonthAttendanceDTO.setAttendances(res);
            userMonthAttendances.add(userMonthAttendanceDTO);
            //按天遍历
            dayCalcHandler(baseDays, res, configDetailMap, hrmsAttendanceEmployeeDetailDOS, formAttrDOList
                    , abnormalAttendanceDOList, classEmployeeConfigDOList, classConfigDOS, classItemConfigDOList
                    , punchConfigDOList, dayPunchConfigRangeDOS, abnormalOperationRecordDOList, userPunchRecordDTOList
                    , passAndInViewFormDOList, passAndInViewFormAttrDOList);
        }
        //用户考勤记录
        userAttendanceDTO.setUserYearAttendance(userYearAttendanceDTO);
        userAttendanceDTO.setAttendanceConfigType(attendanceConfigDO.getType());
        return userAttendanceDTO;
    }

    private UserAttendanceDTO baseDataHandlerByMonth(AttendanceEmployeeDetailQuery query, HrmsUserInfoDO userInfoDO, UserAttendanceDTO userAttendanceDTO) {
        StringBuilder append = new StringBuilder(String.valueOf(query.getYear())).append("/").append(query.getMonth()).append("/").append("1");
        Date monthTime = DateUtil.parse(append, DEFAULT_DATE_FORMAT_STR);
        //查询员工考勤明细(按月)
        List<HrmsAttendanceEmployeeDetailDO> hrmsAttendanceEmployeeDetailDOS = hrmsAttendanceEmployeeDetailManage.selectByYearAndMonth(query.getUserId(), query.getYear(), query.getMonth());
        List<Long> formIdList = hrmsAttendanceEmployeeDetailDOS.stream().filter(item -> item.getFormId() != null).map(item -> item.getFormId()).collect(Collectors.toList());
        List<HrmsApplicationFormAttrDO> formAttrDOList = hrmsApplicationFormAttrManage.selectFormAttrByFormIdLit(formIdList);
        //查询国家日历(按月)
//        List<HrmsAttendanceConfigDetailDO> attendanceConfigDetailDOList = hrmsAttendanceConfigManage.getRangeRecords(query.getUserId(), query.getYear(), query.getMonth(), userInfoDO.getLocationCountry());
        List<HrmsAttendanceConfigDetailDO> attendanceConfigDetailDOList = calendarManageAdapter.getRangeRecords(query.getUserId(), query.getYear(), query.getMonth(), userInfoDO.getLocationCountry());
        if (CollectionUtils.isEmpty(attendanceConfigDetailDOList)) {
            int year = Math.toIntExact(query.getYear());
            throw BusinessException.get(HrmsErrorCodeEnums.ATTENDANCE_CALENDAR_NOT_EXIST.getDesc(), I18nUtils.getMessage(HrmsErrorCodeEnums.ATTENDANCE_CALENDAR_NOT_EXIST.getDesc(), year, year));
        }
        //判断该考勤方案是默认的还是部门/个人的
//        HrmsAttendanceConfigDO attendanceConfigDO = hrmsAttendanceConfigManage.getActiveById(attendanceConfigDetailDOList.get(0).getAttendanceConfigId());
        HrmsAttendanceConfigDO attendanceConfigDO = calendarManageAdapter.getActiveById(attendanceConfigDetailDOList.get(0).getAttendanceConfigId());

        //获取用户该月的所有的打卡记录(避免跨天 需要+1 -1天)
        EmployeePunchCardRecordQuery employeePunchCardRecordQuery = new EmployeePunchCardRecordQuery();
        employeePunchCardRecordQuery.setStartTime(DateUtil.offsetDay(DateUtil.beginOfMonth(monthTime), BusinessConstant.DEFAULT_OFFSET));
        employeePunchCardRecordQuery.setEndTime(DateUtil.offsetDay(DateUtil.endOfMonth(monthTime), -BusinessConstant.DEFAULT_OFFSET));
        employeePunchCardRecordQuery.setUserCode(userInfoDO.getUserCode());
        List<EmployeePunchRecordDO> allPunchRecordList = punchRecordDao.listRecords(employeePunchCardRecordQuery);
        List<UserPunchRecordDTO> userPunchRecordDTOList = new ArrayList<>();
        for (EmployeePunchRecordDO employeePunchRecordDO : allPunchRecordList) {
            UserPunchRecordDTO userPunchRecordDTO = new UserPunchRecordDTO();
            userPunchRecordDTO.setId(employeePunchRecordDO.getId());
            userPunchRecordDTO.setUserCode(employeePunchRecordDO.getUserCode());
            userPunchRecordDTO.setFormId(employeePunchRecordDO.getFormId());
            String punchTimeString = DateUtil.format(employeePunchRecordDO.getPunchTime(), "yyyy-MM-dd HH:mm");
            userPunchRecordDTO.setFormatPunchTime(DateUtil.parse(punchTimeString + ":00", "yyyy-MM-dd HH:mm:ss"));
            userPunchRecordDTOList.add(userPunchRecordDTO);
        }
        //获取用户改年的所有审批通过的请假/外勤单据
        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setUserId(query.getUserId());
        applicationFormQuery.setStatusList(HrAttendanceApplicationFormStatusEnum.getAttendanceCodeList());
        applicationFormQuery.setFromTypeList(HrAttendanceApplicationFormTypeEnum.getAttendanceCodeList());
        List<HrmsApplicationFormDO> passAndInViewFormDOList = hrmsApplicationFormManage.selectForm(applicationFormQuery);
        List<Long> passAndInViewFormIdList = passAndInViewFormDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsApplicationFormAttrDO> passAndInViewFormAttrDOList = hrmsApplicationFormAttrManage.selectFormAttrByFormIdLit(passAndInViewFormIdList);

        Long startMonthTime = Long.valueOf(DateUtil.format(DateUtil.beginOfMonth(monthTime), "yyyyMMdd"));
        Long endMonthTime = Long.valueOf(DateUtil.format(DateUtil.endOfMonth(monthTime), "yyyyMMdd"));
        //查询员工该月的所有异常(包含处理和未处理的)
        List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectAbnormalByUserId(query.getUserId(), startMonthTime, endMonthTime);
        List<Long> abnormalIdList = abnormalAttendanceDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = hrmsEmployeeAbnormalOperationRecordManage.selectByAbnormalList(abnormalIdList);

        //查询员工该月的所有排班，然后查出打卡规则（需要获取当天的法定工作时长）
        List<HrmsAttendanceClassEmployeeConfigDO> classEmployeeConfigDOList = hrmsAttendanceClassEmployeeConfigManage.selectRecordByUserIdList(Arrays.asList(query.getUserId()), startMonthTime, endMonthTime);
        List<Long> classIdList = classEmployeeConfigDOList.stream().filter(item -> item.getClassId() != null).map(item -> item.getClassId()).collect(Collectors.toList());
//        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = hrmsAttendancePunchClassConfigManage.selectClassByIdList(classIdList);
        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = punchConfigManageAdapter.selectClassByIdList(classIdList);
//        List<HrmsAttendancePunchClassItemConfigDO> classItemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(classIdList);
        List<HrmsAttendancePunchClassItemConfigDO> classItemConfigDOList = punchConfigManageAdapter.selectItemConfigByClassId(classIdList);
        List<Long> punchConfigIdList = classEmployeeConfigDOList.stream().filter(item -> item.getPunchConfigId() != null).map(item -> item.getPunchConfigId()).collect(Collectors.toList());
//        List<HrmsAttendancePunchConfigDO> punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByIdList(punchConfigIdList);
        List<HrmsAttendancePunchConfigDO> punchConfigDOList = punchConfigManageAdapter.selectAttendancePunchByIdList(punchConfigIdList);

        //按月进行日历遍历
//        Map<Long, HrmsAttendanceConfigDetailDO> configDetailMap = hrmsAttendanceConfigManage.getRangeRecords(query.getUserId(), query.getYear(), query.getMonth(), userInfoDO.getLocationCountry()).stream().collect(Collectors.toMap(HrmsAttendanceConfigDetailDO::getDayId, item -> item, (oldVar, newVar) -> oldVar));
        Map<Long, HrmsAttendanceConfigDetailDO> configDetailMap = calendarManageAdapter.getRangeRecords(query.getUserId(), query.getYear(), query.getMonth(), userInfoDO.getLocationCountry()).stream().collect(Collectors.toMap(HrmsAttendanceConfigDetailDO::getDayId, item -> item, (oldVar, newVar) -> oldVar));
        BaseDayQuery baseDayQuery = BeanUtil.copyProperties(query, BaseDayQuery.class);
        //当月天数
        List<HrmsBaseDayInfoDO> dayOfMonth = hrmsBaseDayInfoDao.getBaseDay(baseDayQuery);

        //免打卡需要用到
        AttendancePunchConfigRangeByDateQuery configRangeByDateQuery = new AttendancePunchConfigRangeByDateQuery();
        configRangeByDateQuery.setUserIds(Arrays.asList(query.getUserId()));
//        List<HrmsAttendancePunchConfigRangeDO> dayPunchConfigRangeDOS = hrmsAttendancePunchConfigRangeManage.selectPunchConfigRangeByDate(configRangeByDateQuery);
        List<HrmsAttendancePunchConfigRangeDO> dayPunchConfigRangeDOS = punchConfigManageAdapter.selectPunchConfigRangeByDate(configRangeByDateQuery);

        UserYearAttendanceDTO userYearAttendanceDTO = new UserYearAttendanceDTO();
        userYearAttendanceDTO.setYear(query.getYear());
        List<UserMonthAttendanceDTO> userMonthAttendances = new ArrayList<>();
        userYearAttendanceDTO.setUserMonthAttendances(userMonthAttendances);
        UserMonthAttendanceDTO userMonthAttendanceDTO = new UserMonthAttendanceDTO();
        userMonthAttendanceDTO.setMonth(query.getMonth());
        List<AttendanceDetailDTO> res = new ArrayList<>();
        userMonthAttendanceDTO.setAttendances(res);
        userMonthAttendances.add(userMonthAttendanceDTO);
        //按天遍历
        dayCalcHandler(dayOfMonth, res, configDetailMap, hrmsAttendanceEmployeeDetailDOS, formAttrDOList
                , abnormalAttendanceDOList, classEmployeeConfigDOList, classConfigDOS, classItemConfigDOList
                , punchConfigDOList, dayPunchConfigRangeDOS, abnormalOperationRecordDOList, userPunchRecordDTOList
                , passAndInViewFormDOList, passAndInViewFormAttrDOList);
        //用户考勤记录
        userAttendanceDTO.setUserYearAttendance(userYearAttendanceDTO);
        userAttendanceDTO.setAttendanceConfigType(attendanceConfigDO.getType());
        return userAttendanceDTO;
    }

    private void dayCalcHandler(List<HrmsBaseDayInfoDO> baseDays, List<AttendanceDetailDTO> res, Map<Long, HrmsAttendanceConfigDetailDO> configDetailMap,
                                List<HrmsAttendanceEmployeeDetailDO> hrmsAttendanceEmployeeDetailDOS, List<HrmsApplicationFormAttrDO> formAttrDOList,
                                List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList, List<HrmsAttendanceClassEmployeeConfigDO> classEmployeeConfigDOList,
                                List<HrmsAttendancePunchClassConfigDO> classConfigDOS, List<HrmsAttendancePunchClassItemConfigDO> classItemConfigDOList,
                                List<HrmsAttendancePunchConfigDO> punchConfigDOList, List<HrmsAttendancePunchConfigRangeDO> dayPunchConfigRangeDOS,
                                List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList, List<UserPunchRecordDTO> userPunchRecordDTOList,
                                List<HrmsApplicationFormDO> passAndInviewFormDOList, List<HrmsApplicationFormAttrDO> passAndInviewFormAttrDOList) {
        Long dayIdNow = Long.valueOf(DateUtil.format(new Date(), "yyyyMMdd"));
        for (HrmsBaseDayInfoDO baseDay : baseDays) {
            Long dayId = baseDay.getId();
            AttendanceDetailDTO attendanceDetailDTO = new AttendanceDetailDTO();
            res.add(attendanceDetailDTO);
            attendanceDetailDTO.setDate(DateUtil.beginOfDay(DateUtil.parse(dayId.toString(), "yyyyMMdd")));
            attendanceDetailDTO.setDay(DateUtil.dayOfMonth(attendanceDetailDTO.getDate()));
            attendanceDetailDTO.setDayId(dayId);
            attendanceDetailDTO.setAttendanceType(AttendanceDayTypeEnum.PRESENT.name());
            HrmsAttendanceConfigDetailDO attendanceConfigDetailDO = configDetailMap.get(dayId);
            if (attendanceConfigDetailDO != null) {
                attendanceDetailDTO.setAttendanceType(attendanceConfigDetailDO.getDayType());
            }
            if (dayIdNow.compareTo(dayId) < 0) {
                continue;
            }
            //获取当天的正常考勤
            List<HrmsAttendanceEmployeeDetailDO> dayRecordList = hrmsAttendanceEmployeeDetailDOS.stream().filter(item -> item.getDayId().equals(dayId)).collect(Collectors.toList());
            List<HrmsAttendanceEmployeeDetailDO> isAttendanceList = dayRecordList.stream().filter(record -> record.getIsAttendance() == 1).collect(Collectors.toList());
            attendanceDetailDTO.setIsAttendance(CollectionUtils.isEmpty(isAttendanceList) ? 0 : 1);
            List<HrmsAttendanceEmployeeDetailDO> isNeedAttendanceList = dayRecordList.stream().filter(record -> StringUtils.equalsIgnoreCase("PRESENT", record.getAttendanceType())).collect(Collectors.toList());
            attendanceDetailDTO.setIsNeedAttendance(CollectionUtils.isEmpty(isNeedAttendanceList) ? 0 : 1);
            if (CollectionUtils.isNotEmpty(dayRecordList)) {
                if (StringUtils.equalsIgnoreCase(dayRecordList.get(0).getScanType(), AttendanceScanTypeEnum.SCAN_DELIVERY.getCode())) {
                    attendanceDetailDTO.setScanNumber(dayRecordList.get(0).getDeliveryCount());
                } else {
                    //司机每日OFD数
                    List<HrmsAttendanceEmployeeDetailDO> ofdRecordList = dayRecordList.stream().filter(record -> !StringUtils.equalsIgnoreCase(record.getAttendanceType(), AttendanceDayTypeEnum.LEAVE.name())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(ofdRecordList)) {
                        HrmsAttendanceEmployeeDetailDO ofdRecord = ofdRecordList.get(0);
                        //2个字段有一个不为空，就需要相加
                        if (ofdRecord.getDeliveryCount() != null || ofdRecord.getPickUpCount() != null) {
                            int deliveryCount = ofdRecord.getDeliveryCount() == null ? 0 : ofdRecord.getDeliveryCount();
                            int pickUpCount = ofdRecord.getPickUpCount() == null ? 0 : ofdRecord.getPickUpCount();
                            attendanceDetailDTO.setDriverOfdCount(deliveryCount + pickUpCount);
                        }
                    }
                }
            }

            BigDecimal overtimeMinutes = BigDecimal.ZERO;
            BigDecimal attendanceMinutes = BigDecimal.ZERO;
            for (HrmsAttendanceEmployeeDetailDO detailDO : dayRecordList) {
                attendanceDetailDTO.setAttendanceConcreteType(detailDO.getConcreteType());
                if (detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                    attendanceMinutes = attendanceMinutes.add(detailDO.getAttendanceMinutes());
                }
                if (detailDO.getOvertimeMinutes() != null && detailDO.getOvertimeMinutes().compareTo(BigDecimal.ZERO) > 0) {
                    overtimeMinutes = overtimeMinutes.add(detailDO.getOvertimeMinutes());
                }
            }
            attendanceDetailDTO.setOvertimeMinutes(overtimeMinutes);
            attendanceDetailDTO.setAttendanceMinutes(attendanceMinutes);
            attendanceDetailDTO.setOvertimeHours(overtimeMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
            attendanceDetailDTO.setAttendanceHours(attendanceMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));

            //根据当天的出勤类型进行分组(请假)
            List<LeaveHoursRecordDTO> leaveHoursRecordDTOList = new ArrayList<>();
            Map<String, List<HrmsAttendanceEmployeeDetailDO>> dayLeaveGroup = dayRecordList.stream().filter(item -> StringUtils.isNotBlank(item.getConcreteType()) && item.getLeaveMinutes() != null && item.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.groupingBy(HrmsAttendanceEmployeeDetailDO::getConcreteType));
            BigDecimal totalLeaveMinutes = BigDecimal.ZERO;
            for (Map.Entry<String, List<HrmsAttendanceEmployeeDetailDO>> leaveEntry : dayLeaveGroup.entrySet()) {
                List<HrmsAttendanceEmployeeDetailDO> leaveList = leaveEntry.getValue();
                BigDecimal leaveMinutes = BigDecimal.ZERO;
                List<String> picturePathList = new ArrayList<>();
                for (HrmsAttendanceEmployeeDetailDO leaveDetailDO : leaveList) {
                    leaveMinutes = leaveMinutes.add(leaveDetailDO.getLeaveMinutes());
                    List<HrmsApplicationFormAttrDO> attachmentDOList = formAttrDOList.stream().filter(item -> item.getFormId().equals(leaveDetailDO.getFormId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.attachmentList.getLowerCode())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(attachmentDOList)) {
                        List<AttachmentDTO> attachmentDTOS = JSONObject.parseArray(attachmentDOList.get(0).getAttrValue(), AttachmentDTO.class);
                        for (AttachmentDTO attachmentDTO : attachmentDTOS) {
                            picturePathList.add(attachmentDTO.getUrlPath());
                        }
                    }
                }
                LeaveHoursRecordDTO leaveHoursRecordDTO = new LeaveHoursRecordDTO();
                leaveHoursRecordDTO.setLeaveType(leaveList.get(0).getLeaveType());
                leaveHoursRecordDTO.setLeaveMinutes(leaveMinutes);
                leaveHoursRecordDTO.setLeaveHours(leaveMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
                leaveHoursRecordDTO.setLeaveConcreteType(leaveList.get(0).getConcreteType());
                leaveHoursRecordDTO.setPicturePathList(picturePathList);
                leaveHoursRecordDTOList.add(leaveHoursRecordDTO);
                attendanceDetailDTO.setAttendanceConcreteType(leaveList.get(0).getConcreteType());
                totalLeaveMinutes = totalLeaveMinutes.add(leaveMinutes);
            }
            attendanceDetailDTO.setLeaveHoursRecordDTOList(leaveHoursRecordDTOList);
            attendanceDetailDTO.setLeaveMinutes(totalLeaveMinutes);
            attendanceDetailDTO.setLeaveHours(totalLeaveMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
            //查询当天是否包含考勤流程
            attendanceDetailDTO.setHasSubmittedAttendanceProcess(hasDayForm(dayId, passAndInviewFormDOList, passAndInviewFormAttrDOList));

            //获取当天所有异常
            List<HrmsEmployeeAbnormalAttendanceDO> dayAbnormalAttendanceDOList = abnormalAttendanceDOList.stream().filter(item -> item.getDayId().compareTo(dayId) == 0).collect(Collectors.toList());

            //获取当天的排班和打卡班次信息
            List<HrmsAttendanceClassEmployeeConfigDO> dayClassEmployeeConfigDOList = classEmployeeConfigDOList.stream().filter(item -> item.getDayId().compareTo(dayId) == 0).collect(Collectors.toList());
            List<HrmsAttendancePunchClassConfigDO> dayClassConfigDOS = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dayClassEmployeeConfigDOList) && dayClassEmployeeConfigDOList.get(0).getClassId() != null) {
                dayClassConfigDOS = classConfigDOS.stream().filter(item -> item.getId().equals(dayClassEmployeeConfigDOList.get(0).getClassId())).collect(Collectors.toList());
            }
            List<HrmsAttendancePunchClassItemConfigDO> dayClassItemConfigDOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                HrmsAttendancePunchClassConfigDO classConfigDO = dayClassConfigDOS.get(0);
                dayClassItemConfigDOList = classItemConfigDOList.stream().filter(item -> item.getPunchClassId().equals(classConfigDO.getId())).collect(Collectors.toList());
            }
            List<HrmsAttendancePunchConfigDO> dayPunchConfigDOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                HrmsAttendancePunchClassConfigDO classConfigDO = dayClassConfigDOS.get(0);
                dayPunchConfigDOList = punchConfigDOList.stream().filter(item -> item.getId().equals(classConfigDO.getPunchConfigId())).collect(Collectors.toList());
            }
            //注意特殊情况，免打卡
            List<HrmsAttendancePunchConfigRangeDO> userDayNoPunchConfigRangeDOS = dayPunchConfigRangeDOS.stream().filter(item -> item.getEffectTime().compareTo(DateUtil.endOfDay(attendanceDetailDTO.getDate())) < 1 && item.getExpireTime().compareTo(DateUtil.endOfDay(attendanceDetailDTO.getDate())) > -1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userDayNoPunchConfigRangeDOS) && userDayNoPunchConfigRangeDOS.get(0).getIsNeedPunch().equals(0)) {
                if (attendanceDetailDTO.getLeaveMinutes() == null || attendanceDetailDTO.getLeaveMinutes().compareTo(BigDecimal.ZERO) == 0) {
                    attendanceDetailDTO.setAttendanceConcreteType("P");
                    continue;
                }
                if (attendanceDetailDTO.getLeaveMinutes().compareTo(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES)) == 0) {
                    attendanceDetailDTO.setAttendanceConcreteType("L");
                    continue;
                }
                attendanceDetailDTO.setAttendanceMinutes(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).subtract(attendanceDetailDTO.getLeaveMinutes()));
                attendanceDetailDTO.setAttendanceHours(attendanceDetailDTO.getAttendanceMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
                continue;
                    /*attendanceDetailDTO.setAttendanceConcreteType("A");
                    continue;*/
            }
            dayTypeHandler(attendanceDetailDTO, dayRecordList, dayAbnormalAttendanceDOList, dayClassEmployeeConfigDOList, dayClassConfigDOS, dayClassItemConfigDOList, dayPunchConfigDOList, abnormalOperationRecordDOList, userPunchRecordDTOList, passAndInviewFormDOList, passAndInviewFormAttrDOList);
        }
    }

    private void dayTypeHandler(AttendanceDetailDTO attendanceDetailDTO, List<HrmsAttendanceEmployeeDetailDO> dayRecordList,
                                List<HrmsEmployeeAbnormalAttendanceDO> dayAbnormalAttendanceDOList, List<HrmsAttendanceClassEmployeeConfigDO> dayClassEmployeeConfigDOList,
                                List<HrmsAttendancePunchClassConfigDO> dayClassConfigDOS, List<HrmsAttendancePunchClassItemConfigDO> dayClassItemConfigDOList,
                                List<HrmsAttendancePunchConfigDO> dayPunchConfigDOList, List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList,
                                List<UserPunchRecordDTO> allPunchRecordList, List<HrmsApplicationFormDO> passAndInviewFormDOList, List<HrmsApplicationFormAttrDO> passAndInviewFormAttrDOList) {
        attendanceDetailDTO.setDefaultLegalWorkingHours(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
        List<BigDecimal> defaultAbnormalHours = dayRecordList.stream()
                .filter(item -> item.getLegalWorkingHours() != null && item.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > 0 && item.getAttendanceMinutes() != null && item.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0).map(HrmsAttendanceEmployeeDetailDO::getLegalWorkingHours).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(defaultAbnormalHours)) {
            attendanceDetailDTO.setDefaultLegalWorkingHours(defaultAbnormalHours.get(0));
        }
       /* List<BigDecimal> defaultAttendanceMinutesList = dayRecordList.stream().filter(o -> o.getLegalWorkingHours() != null && o.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > 0 && o.getAttendanceMinutes() != null && o.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0).map(o -> o.getAttendanceMinutes()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(defaultAttendanceMinutesList)) {
            //这种数据存在的前提是，当天未排班，出现未排班异常，用户手动确认异常，输入的应出勤时长
            attendanceDetailDTO.setDefaultLegalWorkingHours(defaultAttendanceMinutesList.get(0).divide(BusinessConstant.MINUTES));
        }*/
        if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
            if (dayClassConfigDOS.get(0).getLegalWorkingHours() != null && dayClassConfigDOS.get(0).getLegalWorkingHours().compareTo(BigDecimal.ZERO) > -1) {
                attendanceDetailDTO.setDefaultLegalWorkingHours(dayClassConfigDOS.get(0).getLegalWorkingHours());
            }
        }
        //先判断当天正常表时间够不够，正常就直接返回
        if ((attendanceDetailDTO.getAttendanceMinutes().add(attendanceDetailDTO.getLeaveMinutes())).compareTo(attendanceDetailDTO.getDefaultLegalWorkingHours().multiply(BusinessConstant.MINUTES)) > -1) {
            return;
        }
        //当天没有异常，直接返回
      /*  if (CollectionUtils.isEmpty(dayAbnormalAttendanceDOList)) {
            return;
        }*/

        //后续的所有情况都是当天没有完整出勤

        //当天未排班，需要看一下正常出勤表有没有数据(有的话也是异常处理确认后的数据)
        if (CollectionUtils.isEmpty(dayClassEmployeeConfigDOList)) {
            //当天未排班，也没有处理异常，就是A缺勤
            attendanceDetailDTO.setAttendanceConcreteType("A");
            return;
        }
        //当天排班如果是PH/OFF就无需调用
        List<HrmsAttendanceClassEmployeeConfigDO> phEmployeeList = dayClassEmployeeConfigDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getDayPunchType(), PunchDayTypeEnum.PH.getCode()) || StringUtils.equalsIgnoreCase(item.getDayPunchType(), PunchDayTypeEnum.OFF.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(phEmployeeList)) {
            return;
        }
        //添加打卡规则类型
        if (CollectionUtils.isNotEmpty(dayPunchConfigDOList)) {
            attendanceDetailDTO.setPunchConfigType(dayPunchConfigDOList.get(0).getPunchConfigType());
            attendanceDetailDTO.setPunchConfigTypeDesc(RequestInfoHolder.isChinese() ?
                    PunchConfigTypeEnum.getInstance(dayPunchConfigDOList.get(0).getPunchConfigType()).getDesc() :
                    PunchConfigTypeEnum.getInstance(dayPunchConfigDOList.get(0).getPunchConfigType()).getDescEn());
        }

        BigDecimal formMinutes = BigDecimal.ZERO;
        for (HrmsAttendanceEmployeeDetailDO detailDO : dayRecordList) {
            if (detailDO.getFormId() != null && detailDO.getLeaveMinutes() != null && detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                formMinutes = formMinutes.add(detailDO.getLeaveMinutes());
            }
            if (detailDO.getFormId() != null && detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                formMinutes = formMinutes.add(detailDO.getAttendanceMinutes());
            }
        }
        BigDecimal availableMinutes = BigDecimal.ZERO;
        //接下来需要重新计算当天的考勤，根据打卡时间来获取
        //自由打卡
        if (CollectionUtils.isNotEmpty(dayPunchConfigDOList) && StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())) {
            //仅仅是打卡时间，还需要请假/外勤时间
            availableMinutes = userFreeDayAttendanceHandler(attendanceDetailDTO.getDayId(), dayClassItemConfigDOList, allPunchRecordList);
            //需要把请假时间也加上，自由打卡规则需要修改下
            availableMinutes = availableMinutes.add(formMinutes);
        }
        //一次打卡
        if (CollectionUtils.isNotEmpty(dayPunchConfigDOList) && StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FIXED_WORK_ONCE.name())) {
            //一次打卡，当前出勤时间是默认法定时长 - 请假时间
            BigDecimal defaultMinutes = attendanceDetailDTO.getDefaultLegalWorkingHours().multiply(BusinessConstant.MINUTES);
            availableMinutes = userOnceDayAttendanceHandler(attendanceDetailDTO.getDayId(), dayClassItemConfigDOList
                    , allPunchRecordList, attendanceDetailDTO.getLeaveMinutes(), defaultMinutes, attendanceDetailDTO.getAttendanceMinutes());
        }
        //固班/班次
        if (CollectionUtils.isNotEmpty(dayPunchConfigDOList)
                && !StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())
                && !StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FIXED_WORK_ONCE.name())) {
            availableMinutes = userShiftDayAttendanceHandler(attendanceDetailDTO.getDayId(), dayClassItemConfigDOList, allPunchRecordList, passAndInviewFormDOList, passAndInviewFormAttrDOList);
        }
        if (availableMinutes.compareTo(BigDecimal.ZERO) == 0) {
            attendanceDetailDTO.setAttendanceConcreteType("A");
            return;
        }
        //当天存在出勤时间，可能是请假也可能是打卡，或者两者都有
        if (attendanceDetailDTO.getLeaveMinutes() == null || attendanceDetailDTO.getLeaveMinutes().compareTo(BigDecimal.ZERO) == 0) {
            //没有请假时间
            attendanceDetailDTO.setAttendanceConcreteType("P");
        }
        //在减去单据申请时间
        availableMinutes = availableMinutes.subtract(attendanceDetailDTO.getLeaveMinutes());
        attendanceDetailDTO.setAttendanceMinutes(availableMinutes);
        attendanceDetailDTO.setAttendanceHours(availableMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
        if (availableMinutes.compareTo(attendanceDetailDTO.getDefaultLegalWorkingHours().multiply(BusinessConstant.MINUTES)) < 0) {
            attendanceDetailDTO.setAttendanceConcreteType("A");
        }
    }

    /**
     * 固定/班次打卡规则
     */
    private BigDecimal userShiftDayAttendanceHandler(Long dayId, List<HrmsAttendancePunchClassItemConfigDO> dayClassItemConfigDOList, List<UserPunchRecordDTO> allPunchRecordList, List<HrmsApplicationFormDO> passFormDOList, List<HrmsApplicationFormAttrDO> passFormAttrDOList) {
        BigDecimal availableMinutes = BigDecimal.ZERO;

        List<DayAttendanceHandlerFormDTO> handlerFormDTOList = new ArrayList<>();
        dayFormInfoBuild(handlerFormDTOList, passFormDOList, passFormAttrDOList);
        handlerFormDTOList = handlerFormDTOList.stream().sorted(Comparator.comparing(DayAttendanceHandlerFormDTO::getStartTime)).collect(Collectors.toList());
        for (HrmsAttendancePunchClassItemConfigDO itemConfigDO : dayClassItemConfigDOList) {
            //获取当前时刻的正常时间
            DayPunchTimeDTO dayPunchTimeDTO = hrmsAttendanceBaseService.getUserPunchClassItemDayTime(dayId, itemConfigDO.getId(), dayClassItemConfigDOList);
            if (dayPunchTimeDTO == null || dayPunchTimeDTO.getDayPunchStartTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) > -1) {
                return availableMinutes;
            }
            //获取打卡时间的所有点
            //最早上班打卡时间
            Date earliestPunchInTime = dayPunchTimeDTO.getDayPunchStartTime();
            //上班时间  默认和最早打卡时间同一天
            Date punchInTime = DateUtil.parse(DateUtil.format(earliestPunchInTime, "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            //上班时间早于最早打卡时间，跨天
            if (itemConfigDO.getPunchInTime().before(itemConfigDO.getEarliestPunchInTime())) {
                punchInTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(earliestPunchInTime, 1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            }
            //最晚上班打卡时间 默认和上班时间同一天
            Date latestPunchInTime = DateUtil.parse(DateUtil.format(punchInTime, "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getLatestPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            if (itemConfigDO.getLatestPunchInTime().before(itemConfigDO.getPunchInTime())) {
                latestPunchInTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(punchInTime, 1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getLatestPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            }

            //休息开始时间
            Date restStartTime = null;
            Date restEndTime = null;
            if (itemConfigDO.getRestStartTime() != null) {
                //默认和开始时间同一天
                restStartTime = DateUtil.parse(DateUtil.format(punchInTime, "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                if (itemConfigDO.getRestStartTime().before(itemConfigDO.getPunchInTime())) {
                    restStartTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(punchInTime, 1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                }
                restEndTime = DateUtil.parse(DateUtil.format(restStartTime, "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                if (itemConfigDO.getRestEndTime().before(itemConfigDO.getRestStartTime())) {
                    restEndTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(restStartTime, 1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                }
            }

            //最晚下班打卡时间
            Date latestPunchOutTime = dayPunchTimeDTO.getDayPunchEndTime();
            //下班时间  默认和最晚下班时间同一天
            Date punchOutTime = DateUtil.parse(DateUtil.format(latestPunchOutTime, "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            if (itemConfigDO.getPunchOutTime().after(itemConfigDO.getLatestPunchOutTime())) {
                punchOutTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(latestPunchOutTime, -1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            }

            BigDecimal elasticTime = BigDecimal.ZERO;
            if (itemConfigDO.getElasticTime() != null) {
                elasticTime = itemConfigDO.getElasticTime();
            }
            //弹性下班时间
            Date elasticPunchOutTime = DateUtil.offsetMinute(punchOutTime, elasticTime.multiply(BusinessConstant.MINUTES).intValue());

            //这个时刻的应出勤分钟
            BigDecimal itemTotalMinutes = BigDecimal.valueOf(DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE));
            if (restStartTime != null) {
                itemTotalMinutes = itemTotalMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
            }

            // 获取该班次最早上班打卡时间和最晚上班打卡时间之间的打卡记录
            Date finalLatestPunchInTime = latestPunchInTime;
            List<UserPunchRecordDTO> punchRecordList = allPunchRecordList.stream()
                    .filter(item -> item.getFormatPunchTime().compareTo(earliestPunchInTime) > -1 && item.getFormatPunchTime().compareTo(finalLatestPunchInTime) < 1)
                    .sorted(Comparator.comparing(UserPunchRecordDTO::getFormatPunchTime)).collect(Collectors.toList());
            // 获取最新的一个打卡记录
            UserPunchRecordDTO userPunchRecordDTO = null;
            // 如果最近的一条打卡记录是大于等于上班时间，小于等于最晚上班时间也就是弹性之后的，那么需要根据打卡时间进行弹性处理
            long betweenMinutes = 0;
            if (CollUtil.isNotEmpty(punchRecordList)) {
                userPunchRecordDTO = punchRecordList.get(0);
                // 如果最近的一条打卡记录是大于等于上班时间，小于等于最晚上班时间也就是弹性之后的，那么需要根据打卡时间进行弹性处理
                if (userPunchRecordDTO.getFormatPunchTime().compareTo(punchInTime) > -1 && userPunchRecordDTO.getFormatPunchTime().compareTo(finalLatestPunchInTime) < 1) {
                    // 获取两个时间相差分钟数
                    betweenMinutes = DateUtil.between(userPunchRecordDTO.getFormatPunchTime(), punchInTime, DateUnit.MINUTE);
                }
            }

            BigDecimal usedMinutes = BigDecimal.ZERO;
            //找出和该时刻相关的所有单据
            //先生成正常考勤，所有的请假/外勤落库
            //这里请假可能超出出勤时长了
            List<DayAttendanceHandlerFormDTO> filterFormDTOList = new ArrayList<>();
            for (DayAttendanceHandlerFormDTO handlerFormDTO : handlerFormDTOList) {
                BigDecimal leaveMinutes = BigDecimal.ZERO;
                //请假时间和本时刻一定有交集，看和时刻内的休息时间的关系
                //看看本次请假/外勤的起始时间还是结束时间有咩有落在上班的弹性时间中(会有一个假跨多个时段的情况出现)
                leaveMinutes = hrmsAttendanceBaseService.shiftDayLeaveMinuteHandler(handlerFormDTO, punchInTime, punchOutTime, restStartTime, restEndTime, filterFormDTOList, betweenMinutes, elasticPunchOutTime);
                usedMinutes = usedMinutes.add(leaveMinutes);
            }

            //当前时刻完全请假
            if ((itemTotalMinutes.subtract(usedMinutes)).compareTo(BigDecimal.ZERO) < 1) {
                //当前时刻完全请假
                availableMinutes = availableMinutes.add(usedMinutes);
                continue;
            }
            //可能部分请假/外勤，需要加上
            availableMinutes = availableMinutes.add(usedMinutes);
            //没有全部请假，需要看打卡时间（可能打卡时间够，正常考勤，也可能不够，异常考勤）
            List<UserPunchRecordDTO> itemPunchRecordList = allPunchRecordList.stream().filter(item -> item.getFormatPunchTime().compareTo(earliestPunchInTime) > -1 && item.getFormatPunchTime().compareTo(latestPunchOutTime) < 1).sorted(Comparator.comparing(UserPunchRecordDTO::getFormatPunchTime)).collect(Collectors.toList());
            //情况1:当天没有打卡时间
            if (CollectionUtils.isEmpty(itemPunchRecordList)) {
                continue;
            }
            //情况2:当天完全没请假，看打卡记录
            if (CollectionUtils.isEmpty(filterFormDTOList)) {
                BigDecimal minutes = normalPunchHandler(itemPunchRecordList, punchInTime, latestPunchInTime, punchOutTime, restStartTime, restEndTime, itemTotalMinutes);
                availableMinutes = availableMinutes.add(minutes);
                continue;
            }

            //情况3:当天存在请假  有一条打卡记录
            if (itemPunchRecordList.size() == 1) {
                continue;
            }
            //情况4: 当天存在请假 多条数据，但可能都是上班卡
            BigDecimal minutes = leaveDayBatchHandler(itemPunchRecordList, filterFormDTOList, earliestPunchInTime, punchInTime, latestPunchInTime, punchOutTime, latestPunchOutTime, restStartTime, restEndTime, itemTotalMinutes.subtract(usedMinutes));
            availableMinutes = availableMinutes.add(minutes);
        }
        return availableMinutes;
    }

    /**
     * 一次打卡规则
     */
    private BigDecimal userOnceDayAttendanceHandler(Long dayId, List<HrmsAttendancePunchClassItemConfigDO> dayClassItemConfigDOList
            , List<UserPunchRecordDTO> allPunchRecordList, BigDecimal leaveMinutes
            , BigDecimal defaultLegalWorkingMinutes, BigDecimal outOffOfficeMinutes) {
        BigDecimal availableMinutes = BigDecimal.ZERO;
        Date earliestPunchInTime = null;
        Date latestPunchOutTime = null;
        for (HrmsAttendancePunchClassItemConfigDO itemConfigDO : dayClassItemConfigDOList) {
            //获取当前时刻的正常时间
            DayPunchTimeDTO dayPunchTimeDTO = hrmsAttendanceBaseService.getUserPunchClassItemDayTime(dayId, itemConfigDO.getId(), dayClassItemConfigDOList);
            if (dayPunchTimeDTO == null || dayPunchTimeDTO.getDayPunchStartTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) > -1) {
                return availableMinutes;
            }
            //获取打卡时间的所有点
            //最早上班打卡时间
            if (Objects.isNull(earliestPunchInTime) || dayPunchTimeDTO.getDayPunchStartTime().compareTo(earliestPunchInTime) < 0) {
                earliestPunchInTime = dayPunchTimeDTO.getDayPunchStartTime();
            }
            //最晚下班打卡时间
            if (Objects.isNull(latestPunchOutTime) || dayPunchTimeDTO.getDayPunchEndTime().compareTo(latestPunchOutTime) > 0) {
                latestPunchOutTime = dayPunchTimeDTO.getDayPunchEndTime();
            }
        }
        // 获取该班次最早上班打卡时间和最晚下班打卡时间之间的打卡记录
        Date finalEarliestPunchInTime = earliestPunchInTime;
        Date finalLatestPunchOutTime = latestPunchOutTime;
        List<UserPunchRecordDTO> punchRecordList = allPunchRecordList.stream()
                .filter(item -> item.getFormatPunchTime().compareTo(finalEarliestPunchInTime) > -1 && item.getFormatPunchTime().compareTo(finalLatestPunchOutTime) < 1)
                .sorted(Comparator.comparing(UserPunchRecordDTO::getFormatPunchTime)).collect(Collectors.toList());
        //当天不存在打卡记录
        if (CollectionUtils.isEmpty(punchRecordList)) {
            //当天完全请假
            if (leaveMinutes.compareTo(defaultLegalWorkingMinutes) == 0) {
                return availableMinutes.add(leaveMinutes);
            }
            //当天没有请假或部分请假
            return availableMinutes.add(outOffOfficeMinutes);
        }
        //当天存在打卡记录
        if (CollectionUtils.isNotEmpty(punchRecordList) && leaveMinutes.compareTo(BigDecimal.ZERO) == 0) {
            //当天没有请假
            return defaultLegalWorkingMinutes;
        }
        //当天完全请假
        if (leaveMinutes.compareTo(defaultLegalWorkingMinutes) == 0) {
            return availableMinutes.add(leaveMinutes);
        }
        //部分请假
//        BigDecimal oncePunchAttendanceMinutes = defaultLegalWorkingMinutes.subtract(leaveMinutes);
        availableMinutes = availableMinutes.add(defaultLegalWorkingMinutes);
        return availableMinutes;
    }

    /**
     * 自由打卡规则
     */
    private BigDecimal userFreeDayAttendanceHandler(Long dayId, List<HrmsAttendancePunchClassItemConfigDO> dayClassItemConfigDOList, List<UserPunchRecordDTO> allPunchRecordList) {

        BigDecimal availableMinutes = BigDecimal.ZERO;
        //获取当天的正常上班时间
        Date earliestPunchInTime = dayClassItemConfigDOList.get(0).getEarliestPunchInTime();
        String earliestPunchInTimeString = DateUtil.format(earliestPunchInTime, "HH:mm:ss");
        String earliestPunchInTimeDayString = DateUtil.format(DateUtil.parse(dayId.toString(), "yyyyMMdd"), "yyyy-MM-dd");
        Date actualAttendanceStartTime = DateUtil.parse(earliestPunchInTimeDayString + " " + earliestPunchInTimeString, "yyyy-MM-dd HH:mm:ss");
        Date actualAttendanceEndTime = DateUtil.offsetDay(actualAttendanceStartTime, 1);

        List<UserPunchRecordDTO> itemPunchRecordList = allPunchRecordList.stream().filter(item -> item.getFormatPunchTime().compareTo(actualAttendanceStartTime) > -1 && item.getFormatPunchTime().compareTo(actualAttendanceEndTime) < 1).sorted(Comparator.comparing(UserPunchRecordDTO::getFormatPunchTime)).collect(Collectors.toList());

        //么有打卡数据，直接上下班缺卡异常
        if (CollectionUtils.isEmpty(itemPunchRecordList)) {
            return availableMinutes;
        }
        //只有一个打卡
        if (itemPunchRecordList.size() == 1) {
            return availableMinutes;
        }
        //有多个打卡记录
        availableMinutes = BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE));
        return availableMinutes;
    }

    /**
     * 时刻没有请假，正常打卡
     */
    private BigDecimal normalPunchHandler(List<UserPunchRecordDTO> itemPunchRecordList, Date punchInTime, Date latestPunchInTime, Date punchOutTime, Date restStartTime, Date restEndTime, BigDecimal itemTotalMinutes) {
        BigDecimal availableMinutes = BigDecimal.ZERO;
        //一条打卡记录
        if (itemPunchRecordList.size() == 1) {
            return availableMinutes;
        }

        //多条打卡记录
        //下班未打卡
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchInTime) < 1) {
            return availableMinutes;
        }
        //上班未打卡
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchOutTime) > -1) {
            return availableMinutes;
        }

        //todo 这里有问题啊，考勤也同样，这里的休息时间是不是没算上去
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchInTime) < 1) {
            //早退  下班时间在正常上班之前
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchInTime) < 1) {
                return availableMinutes;
            }
            //早退
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
                //需要和休息时间比较
                availableMinutes = compareRestTimeHandler(punchInTime, itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), restStartTime, restEndTime);
                return availableMinutes;
            }
            availableMinutes = compareRestTimeHandler(punchInTime, punchOutTime, restStartTime, restEndTime);
            return availableMinutes;
        }
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(latestPunchInTime) < 1) {
            //早退
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
                availableMinutes = compareRestTimeHandler(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), restStartTime, restEndTime);
                return availableMinutes;
            }
            //比较时间长短
            if (BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE)).compareTo(BigDecimal.valueOf(DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE))) > -1) {
                availableMinutes = itemTotalMinutes;
                return availableMinutes;
            }
            availableMinutes = compareRestTimeHandler(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), restStartTime, restEndTime);
            return availableMinutes;
        }
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
            availableMinutes = compareRestTimeHandler(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), restStartTime, restEndTime);
            return availableMinutes;
        }
        availableMinutes = compareRestTimeHandler(itemPunchRecordList.get(0).getFormatPunchTime(), punchOutTime, restStartTime, restEndTime);
        return availableMinutes;
    }

    /**
     * 时刻有请假，正常打卡
     */
    private BigDecimal leaveDayBatchHandler(List<UserPunchRecordDTO> itemPunchRecordList, List<DayAttendanceHandlerFormDTO> filterFormDTOList, Date earliestPunchInTime, Date punchInTime, Date latestPunchInTime, Date punchOutTime, Date latestPunchOutTime, Date restStartTime, Date restEndTime, BigDecimal itemTotalMinutes) {
        Date finalPunchInTime = punchInTime;
        Date finalPunchOutTime = punchOutTime;
        List<UserPunchRecordDTO> punchBeforeCardList = itemPunchRecordList.stream().filter(o -> o.getFormatPunchTime().compareTo(earliestPunchInTime) > -1 && o.getFormatPunchTime().compareTo(finalPunchInTime) < 0).collect(Collectors.toList());
        List<UserPunchRecordDTO> punchBetweenCardList = itemPunchRecordList.stream().filter(o -> o.getFormatPunchTime().compareTo(finalPunchInTime) > -1 && o.getFormatPunchTime().compareTo(finalPunchOutTime) < 1).collect(Collectors.toList());
        List<UserPunchRecordDTO> punchAfterCardList = itemPunchRecordList.stream().filter(o -> o.getFormatPunchTime().compareTo(finalPunchOutTime) > 0 && o.getFormatPunchTime().compareTo(latestPunchOutTime) < 1).collect(Collectors.toList());
        List<DayItemInfoDTO> dayItemInfoDTOS = new ArrayList<>();
        BigDecimal presentMinutes = BigDecimal.ZERO;
        //打卡时间全部在正规上下班中
        if (CollectionUtils.isEmpty(punchBeforeCardList) && CollectionUtils.isEmpty(punchAfterCardList)) {
            //整个时段内，除去请假外的打卡记录的区间
            hrmsAttendanceBaseService.multiplePunchHandler(dayItemInfoDTOS, filterFormDTOList, punchBetweenCardList);
            //再去和休息时间比较
            presentMinutes = hrmsAttendanceBaseService.restPunchHandler(dayItemInfoDTOS, restStartTime, restEndTime);
            return presentMinutes;
        }
        //开始时间和中间时间为空,直接是上班卡未打异常
        if (CollectionUtils.isEmpty(punchBeforeCardList) && CollectionUtils.isEmpty(punchBetweenCardList)) {
            return presentMinutes;
        }
        //结束时间和中间时间为空,直接是下班卡未打异常
        if (CollectionUtils.isEmpty(punchAfterCardList) && CollectionUtils.isEmpty(punchBetweenCardList)) {
            return presentMinutes;
        }
        if (CollectionUtils.isNotEmpty(punchBeforeCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isEmpty(punchAfterCardList)) {
            Date beginPunchTime = punchInTime;
            Date endPunchTime = punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime();
            for (int i = 0; i < filterFormDTOList.size(); i++) {
                if (endPunchTime.compareTo(filterFormDTOList.get(i).getStartTime()) < 1) {
                    DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                    dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    dayItemInfoDTO.setEndItemTime(endPunchTime);
                    dayItemInfoDTOS.add(dayItemInfoDTO);
                    break;
                }
                DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                dayItemInfoDTO.setEndItemTime(filterFormDTOList.get(i).getStartTime());
                dayItemInfoDTOS.add(dayItemInfoDTO);
                if (endPunchTime.compareTo(filterFormDTOList.get(i).getEndTime()) > -1) {
                    beginPunchTime = filterFormDTOList.get(i).getEndTime();
                    if (i == filterFormDTOList.size() - 1) {
                        DayItemInfoDTO dayItemInfoFinalDTO = new DayItemInfoDTO();
                        dayItemInfoFinalDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoFinalDTO.setEndItemTime(endPunchTime);
                        dayItemInfoDTOS.add(dayItemInfoFinalDTO);
                    }
                    continue;
                }
                break;
            }
            for (DayItemInfoDTO itemInfoDTO : dayItemInfoDTOS) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(itemInfoDTO.getBeginItemTime(), itemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
            }
            //再去和休息时间比较
            presentMinutes = hrmsAttendanceBaseService.restPunchHandler(dayItemInfoDTOS, restStartTime, restEndTime);
            return presentMinutes;
        }
        if (CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isEmpty(punchBeforeCardList)) {
            Date beginPunchTime = punchBetweenCardList.get(0).getFormatPunchTime();
            Date endPunchTime = punchOutTime;
            for (int i = filterFormDTOList.size() - 1; i >= 0; i--) {
                if (beginPunchTime.compareTo(filterFormDTOList.get(i).getEndTime()) > -1) {
                    DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                    dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    dayItemInfoDTO.setEndItemTime(endPunchTime);
                    dayItemInfoDTOS.add(dayItemInfoDTO);
                    break;
                }
                DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                dayItemInfoDTO.setBeginItemTime(filterFormDTOList.get(i).getEndTime());
                dayItemInfoDTO.setEndItemTime(endPunchTime);
                dayItemInfoDTOS.add(dayItemInfoDTO);

                if (beginPunchTime.compareTo(filterFormDTOList.get(i).getStartTime()) < 1) {
                    endPunchTime = filterFormDTOList.get(i).getStartTime();
                    if (i == 0) {
                        DayItemInfoDTO dayItemInfoFinalDTO = new DayItemInfoDTO();
                        dayItemInfoFinalDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoFinalDTO.setEndItemTime(endPunchTime);
                        dayItemInfoDTOS.add(dayItemInfoFinalDTO);
                    }
                    continue;
                }
                break;
            }
            for (DayItemInfoDTO itemInfoDTO : dayItemInfoDTOS) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(itemInfoDTO.getBeginItemTime(), itemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
            }
            //再去和休息时间比较
            presentMinutes = hrmsAttendanceBaseService.restPunchHandler(dayItemInfoDTOS, restStartTime, restEndTime);
            return presentMinutes;

        }
        //全部打卡,（或者前后都有打卡，就中间没打卡）没有异常，就看P的时间
        if ((CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isNotEmpty(punchBeforeCardList)) || (CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isEmpty(punchBetweenCardList) && CollectionUtils.isNotEmpty(punchBeforeCardList))) {
            presentMinutes = itemTotalMinutes;
            return presentMinutes;
        }
        return presentMinutes;
    }

    private BigDecimal compareRestTimeHandler(Date startDate, Date endDate, Date restStartTime, Date restEndTime) {
        BigDecimal minutes = BigDecimal.valueOf(DateUtil.between(startDate, endDate, DateUnit.MINUTE));
        if (restStartTime == null) {
            return minutes;
        }
        //没有交集
        if (endDate.compareTo(restStartTime) < 1 || startDate.compareTo(restEndTime) > -1) {
            return minutes;
        }
        //被休息时间包含
        if (startDate.compareTo(restStartTime) > -1 && endDate.compareTo(restEndTime) < 1) {
            return BigDecimal.ZERO;
        }
        //休息时间被包含
        if (startDate.compareTo(restStartTime) < 1 && endDate.compareTo(restEndTime) > -1) {
            return minutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
        }
        if (startDate.compareTo(restStartTime) < 0) {
            return minutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, endDate, DateUnit.MINUTE)));
        }
        return minutes.subtract(BigDecimal.valueOf(DateUtil.between(startDate, restEndTime, DateUnit.MINUTE)));
    }


    private void dayFormInfoBuild(List<DayAttendanceHandlerFormDTO> handlerFormDTOList, List<HrmsApplicationFormDO> passFormDOList, List<HrmsApplicationFormAttrDO> passFormAttrDOList) {
        for (HrmsApplicationFormDO formDO : passFormDOList) {
            //请假
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.LEAVE.getCode())
                    && HrAttendanceApplicationFormStatusEnum.PASS.getCode().equals(formDO.getFormStatus())) {
                List<HrmsApplicationFormAttrDO> leaveStartDateDO = passFormAttrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
                List<HrmsApplicationFormAttrDO> leaveEndDateDO = passFormAttrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(leaveStartDateDO) || CollectionUtils.isEmpty(leaveEndDateDO)) {
                    continue;
                }
                Date leaveStartDate = DateUtil.parse(leaveStartDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Date leaveEndDate = DateUtil.parse(leaveEndDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Long leaveStartDayId = Long.valueOf(DateUtil.format(leaveStartDate, "yyyyMMdd"));
                Long leaveEndDayId = Long.valueOf(DateUtil.format(leaveEndDate, "yyyyMMdd"));
                DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = new DayAttendanceHandlerFormDTO();
                dayAttendanceHandlerFormDTO.setFormId(formDO.getId());
                dayAttendanceHandlerFormDTO.setFormType(formDO.getFormType());
                dayAttendanceHandlerFormDTO.setStartTime(leaveStartDate);
                dayAttendanceHandlerFormDTO.setStartDayId(leaveStartDayId);
                dayAttendanceHandlerFormDTO.setEndTime(leaveEndDate);
                dayAttendanceHandlerFormDTO.setEndDayId(leaveEndDayId);
                handlerFormDTOList.add(dayAttendanceHandlerFormDTO);
            }
            //外勤
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode())
                    && HrAttendanceApplicationFormStatusEnum.PASS.getCode().equals(formDO.getFormStatus())) {
                List<HrmsApplicationFormAttrDO> outOfOfficeStartDateDO = passFormAttrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
                List<HrmsApplicationFormAttrDO> outOfOfficeEndDateDO = passFormAttrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(outOfOfficeStartDateDO) || CollectionUtils.isEmpty(outOfOfficeEndDateDO)) {
                    continue;
                }
                Date outOfOfficeStartDate = DateUtil.parse(outOfOfficeStartDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Date outOfOfficeEndDate = DateUtil.parse(outOfOfficeEndDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Long outOfOfficeStartDayId = Long.valueOf(DateUtil.format(outOfOfficeStartDate, "yyyyMMdd"));
                Long outOfOfficeEndDayId = Long.valueOf(DateUtil.format(outOfOfficeEndDate, "yyyyMMdd"));
                DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = new DayAttendanceHandlerFormDTO();
                dayAttendanceHandlerFormDTO.setFormId(formDO.getId());
                dayAttendanceHandlerFormDTO.setFormType(formDO.getFormType());
                dayAttendanceHandlerFormDTO.setStartTime(outOfOfficeStartDate);
                dayAttendanceHandlerFormDTO.setStartDayId(outOfOfficeStartDayId);
                dayAttendanceHandlerFormDTO.setEndTime(outOfOfficeEndDate);
                dayAttendanceHandlerFormDTO.setEndDayId(outOfOfficeEndDayId);
                handlerFormDTOList.add(dayAttendanceHandlerFormDTO);
            }
        }
    }


    private BigDecimal abnormalMinutesHandler(DayNormalPunchTimeDTO dayNormalPunchTimeDTO, AbnormalExtendDTO abnormalExtendDTO) {
        BigDecimal abnormalMinutes = BigDecimal.ZERO;
        if (dayNormalPunchTimeDTO == null) {
            return abnormalMinutes;
        }
        if (abnormalExtendDTO == null || abnormalExtendDTO.getActualPunchTime() == null || abnormalExtendDTO.getCorrectPunchTime() == null) {
            return abnormalMinutes;
        }
        if (dayNormalPunchTimeDTO.getDayPunchStartTime() == null || dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
            abnormalMinutes = BigDecimal.valueOf(DateUtil.between(abnormalExtendDTO.getActualPunchTime(), abnormalExtendDTO.getCorrectPunchTime(), DateUnit.MINUTE));
            return abnormalMinutes;
        }
        Date startDate = abnormalExtendDTO.getActualPunchTime();
        Date endDate = abnormalExtendDTO.getCorrectPunchTime();
        if (abnormalExtendDTO.getActualPunchTime().compareTo(abnormalExtendDTO.getCorrectPunchTime()) > 0) {
            startDate = abnormalExtendDTO.getCorrectPunchTime();
            endDate = abnormalExtendDTO.getActualPunchTime();
        }

        //没有交集
        if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 0 || startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > 0) {
            abnormalMinutes = BigDecimal.valueOf(DateUtil.between(startDate, endDate, DateUnit.MINUTE));
            return abnormalMinutes;
        }
        //被休息时间包含
        if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) > -1 && endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) < 1) {
            return abnormalMinutes;
        }
        //休息时间被包含
        if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1 && endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1) {
            abnormalMinutes = BigDecimal.valueOf(DateUtil.between(startDate, endDate, DateUnit.MINUTE));
            abnormalMinutes = abnormalMinutes.subtract(BigDecimal.valueOf(DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE)));
            return abnormalMinutes;
        }
        if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 0) {
            abnormalMinutes = BigDecimal.valueOf(DateUtil.between(startDate, dayNormalPunchTimeDTO.getDayPunchRestStartTime(), DateUnit.MINUTE));
            return abnormalMinutes;
        }
        abnormalMinutes = BigDecimal.valueOf(DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestEndTime(), endDate, DateUnit.MINUTE));
        return abnormalMinutes;
    }

    /**
     * 是否允许编辑
     *
     * @param dayId
     * @param dateRanges
     * @return
     */
    private Boolean editPermission(Long dayId, List<DateRange> dateRanges) {
        if (CollUtil.isEmpty(dateRanges)) {
            return Boolean.FALSE;
        }
        for (DateRange dateRange : dateRanges) {
            if (dayId.compareTo(dateRange.getStartDayId()) >= 0 && dayId.compareTo(dateRange.getEndDayId()) <= 0) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 获取可修改考勤记录的每一段时间
     *
     * @param userInfoDO
     */
    private List<DateRange> getDateRange(HrmsUserInfoDO userInfoDO) {
        Queue<DateType> queue = new PriorityQueue<>((o1, o2) -> (int) (o1.getDayId() - o2.getDayId()));
        HrmsUserEntryRecordDO userEntryRecordDO = hrmsUserEntryRecordDao.getByUserId(userInfoDO.getId());
        if (userEntryRecordDO == null) {
            return new ArrayList<>();
        }
        DateType dateType = getDateType(userEntryRecordDO.getEntryDate(), IN);
        queue.add(dateType);
        //TODO 调动表的公司
        List<HrmsUserTransferRecordDO> transferInList = userTransferManage.listTransfer(userInfoDO.getId(), userInfoDO.getLocationCountry(), null);
//        List<HrmsUserTransferRecordDO> transferInList = hrmsUserTransferRecordDao.listTransfer(userId, RequestInfoHolder.getCompanyId(), null);
        for (HrmsUserTransferRecordDO userTransferRecordDO : transferInList) {
            dateType = getDateType(userTransferRecordDO.getTransferTime(), IN);
            queue.add(dateType);
        }
        //TODO 调动表的公司
        List<HrmsUserTransferRecordDO> transferOutList = userTransferManage.listTransfer(userInfoDO.getId(), null, userInfoDO.getLocationCountry());
//        List<HrmsUserTransferRecordDO> transferOutList = hrmsUserTransferRecordDao.listTransfer(userId, null, RequestInfoHolder.getCompanyId());
        for (HrmsUserTransferRecordDO userTransferRecordDO : transferOutList) {
            dateType = getDateType(userTransferRecordDO.getTransferTime(), OUT);
            queue.add(dateType);
        }
        List<DateRange> dateRanges = new ArrayList<>();
        //遍历
        Boolean isIn = Boolean.FALSE;
        DateRange dateRange = new DateRange();
        while (!queue.isEmpty()) {
            DateType poll = queue.poll();
            if (Boolean.FALSE.equals(isIn) && OUT.equals(poll.getTransferType())) {
                continue;
            }
            if (Boolean.TRUE.equals(isIn) && OUT.equals(poll.getTransferType())) {
                dateRange.setEndDayId(poll.getDayId());
                isIn = Boolean.FALSE;
                dateRanges.add(BeanUtil.copyProperties(dateRange, DateRange.class));
                dateRange.setStartDayId(null);
                dateRange.setEndDayId(null);
                continue;
            }
            dateRange.setStartDayId(poll.getDayId());
            isIn = Boolean.TRUE;

        }
        if (dateRange.getStartDayId() != null && dateRange.getEndDayId() == null) {
            //那么截止时间就是今天
            dateRange.setEndDayId(Long.parseLong(DateUtil.format(DateUtil.offsetHour(DateUtil.date(), RequestInfoHolder.getTimeZoneOffset() - BusinessConstant.DEFAULT_TIMEZONE), DatePattern.PURE_DATE_PATTERN)));
            dateRanges.add(dateRange);
        }
        return dateRanges;
    }

    /**
     * 日期和调入调出类型
     *
     * @param transferTime
     * @param
     * @return
     */
    private DateType getDateType(Date transferTime, Integer state) {
        DateType dateType = new DateType();
        dateType.setDayId(Long.parseLong(DateUtil.format(transferTime, DatePattern.PURE_DATE_PATTERN)));
        dateType.setTransferType(state);
        return dateType;
    }


    @Override
    public List<DriverCodeDTO> listDriverCodes(XxlDriverQuery query) {
        return hrmsAttendanceEmployeeDetailMapper.listDrivers(query);
    }

    @Override
    public List<DriverCodeDTO> listAttendanceDriverCodes(DriverAttendanceQuery query) {
        return hrmsAttendanceEmployeeDetailMapper.listAttendanceDrivers(query);
    }

    @Override
    public PaginationResult<AttendanceDTO> employeeAttendanceList(EmployeeAttendanceQueryDTO queryDTO) {
        UserAttendanceQuery query = attendanceBeforeHandler(Boolean.TRUE, queryDTO, BusinessConstant.N, null, null);
        PageInfo<AttendanceDTO> pageInfo = queryUserAttendanceList(query);
        return attendanceAfterHandler(pageInfo, queryDTO);
    }

    @Override
    public PaginationResult<AttendanceDTO> driverAttendanceList(DriverAttendanceQueryDTO queryDTO) {
        UserAttendanceQuery query = attendanceBeforeHandler(Boolean.TRUE, queryDTO, BusinessConstant.Y, BusinessConstant.N, null);
        PageInfo<AttendanceDTO> pageInfo = queryUserAttendanceList(query);
        return attendanceAfterHandler(pageInfo, queryDTO);
    }

    @Override
    public AttendanceCycleDTO getAttendanceCycle(AttendanceExportQueryDTO queryDTO) {
        if (StringUtils.isBlank(queryDTO.getCycleMonth())) {
            throw BusinessException.get(HrmsErrorCodeEnums.ATTENDANCE_MONTH_EXPORT_CYCLE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ATTENDANCE_MONTH_EXPORT_CYCLE_NOT_EMPTY.getDesc()));
        }
        if (StringUtils.isBlank(queryDTO.getCountry())) {
            throw BusinessException.get(HrmsErrorCodeEnums.ATTENDANCE_COUNTRY_EXPORT_CYCLE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ATTENDANCE_COUNTRY_EXPORT_CYCLE_NOT_EMPTY.getDesc()));
        }
        //SalaryConfigQuery query = new SalaryConfigQuery();
        //query.setCountry(queryDTO.getCountry());
        //query.setStatus(StatusEnum.ACTIVE.getCode());
        ////周期为月的
        //List<HrmsSalaryConfigDO> salaryConfigDOS = salaryConfigDao.list(query).stream().filter(item -> StringUtils.equalsIgnoreCase(item.getCycleType(), "MONTH")).collect(Collectors.toList());
        //if (CollectionUtils.isEmpty(salaryConfigDOS)) {
        //    throw BusinessException.get(HrmsErrorCodeEnums.SALARY_CONFIG_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_CONFIG_NOT_EMPTY.getDesc()));
        //}
        //HrmsSalaryConfigDO salaryConfigDO = salaryConfigDOS.get(0);
        //Date cycleStartDate = null;
        //Date cycleEndDate = null;
        //
        //if (StringUtils.equalsIgnoreCase(salaryConfigDO.getCycleEnd(), "END_OF_MONTH")) {
        //    cycleStartDate = DateUtil.beginOfMonth(DateUtil.parse(queryDTO.getCycleMonth(), "yyyyMM"));
        //    cycleEndDate = DateUtil.endOfMonth(DateUtil.parse(queryDTO.getCycleMonth(), "yyyyMM"));
        //} else {
        //    cycleStartDate = DateUtil.offsetMonth(DateUtil.parse(queryDTO.getCycleMonth() + salaryConfigDO.getCycleStart(), "yyyyMMdd"), -1);
        //    cycleEndDate = DateUtil.parse(DateUtil.format(DateUtil.offsetMonth(cycleStartDate, 1), "yyyyMM") + salaryConfigDO.getCycleEnd(), "yyyyMMdd");
        //}

        // 考勤方案
        AttendanceCycleConfigQuery cycleQuery = new AttendanceCycleConfigQuery();
        cycleQuery.setCountry(queryDTO.getCountry());
        cycleQuery.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        List<HrmsAttendanceCycleConfigDO> attendanceCycleConfigList = hrmsAttendanceCycleConfigDao.selectByCondition(cycleQuery);
        // 过滤为激活状态的
        attendanceCycleConfigList = attendanceCycleConfigList.stream().filter(attendanceCycleConfigDO ->
                ObjectUtil.equal(attendanceCycleConfigDO.getStatus(), StatusEnum.ACTIVE.getCode())).collect(Collectors.toList());

        if (CollUtil.isEmpty(attendanceCycleConfigList)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ATTENDANCE_CYCLE_CONFIG_NO_EXISTS_ERROR);
        }
        HrmsAttendanceCycleConfigDO attendanceCycleConfig = attendanceCycleConfigList.get(0);
        Date cycleStartDate = null;
        Date cycleEndDate = null;

        if (StringUtils.equalsIgnoreCase(attendanceCycleConfig.getCycleEnd(), "END_OF_MONTH")) {
            cycleStartDate = DateUtil.beginOfMonth(DateUtil.parse(queryDTO.getCycleMonth(), "yyyyMM"));
            cycleEndDate = DateUtil.endOfMonth(DateUtil.parse(queryDTO.getCycleMonth(), "yyyyMM"));
        } else {
            cycleStartDate = DateUtil.offsetMonth(DateUtil.parse(queryDTO.getCycleMonth() + attendanceCycleConfig.getCycleStart(), "yyyyMMdd"), -1);
            cycleEndDate = DateUtil.parse(DateUtil.format(DateUtil.offsetMonth(cycleStartDate, 1), "yyyyMM") + attendanceCycleConfig.getCycleEnd(), "yyyyMMdd");
        }
        Long cycleStartDayId = Long.valueOf(DateUtil.format(cycleStartDate, "yyyyMMdd"));
        Long cycleEndDayId = Long.valueOf(DateUtil.format(cycleEndDate, "yyyyMMdd"));
        AttendanceCycleDTO cycleDTO = new AttendanceCycleDTO();
        cycleDTO.setAttendanceStartCycle(cycleStartDayId.toString());
        cycleDTO.setAttendanceEndCycle(cycleEndDayId.toString());
        return cycleDTO;
    }

    @Override
    public List<AttendanceTitleExportDTO> attendanceMonthTitleExport(AttendanceExportQueryDTO queryDTO) {
        if (StringUtils.isBlank(queryDTO.getCycleMonth())) {
            throw BusinessException.get(HrmsErrorCodeEnums.ATTENDANCE_MONTH_EXPORT_CYCLE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ATTENDANCE_MONTH_EXPORT_CYCLE_NOT_EMPTY.getDesc()));
        }
        if (StringUtils.isBlank(queryDTO.getCountry())) {
            throw BusinessException.get(HrmsErrorCodeEnums.ATTENDANCE_COUNTRY_EXPORT_CYCLE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ATTENDANCE_COUNTRY_EXPORT_CYCLE_NOT_EMPTY.getDesc()));
        }
        List<AttendanceTitleExportDTO> resultList = new ArrayList<>();
        if (StringUtils.isBlank(queryDTO.getCycleMonth())) {
            throw BusinessException.get(HrmsErrorCodeEnums.ATTENDANCE_MONTH_EXPORT_CYCLE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ATTENDANCE_MONTH_EXPORT_CYCLE_NOT_EMPTY.getDesc()));
        }
        // 考勤周期
        AttendanceCycleConfigQuery cycleQuery = new AttendanceCycleConfigQuery();
        cycleQuery.setCountry(queryDTO.getCountry());
        cycleQuery.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        List<HrmsAttendanceCycleConfigDO> attendanceCycleConfigList = hrmsAttendanceCycleConfigDao.selectByCondition(cycleQuery);
        // 过滤为激活状态的
        attendanceCycleConfigList = attendanceCycleConfigList.stream().filter(attendanceCycleConfigDO ->
                ObjectUtil.equal(attendanceCycleConfigDO.getStatus(), StatusEnum.ACTIVE.getCode())).collect(Collectors.toList());

        if (CollUtil.isEmpty(attendanceCycleConfigList)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ATTENDANCE_CYCLE_CONFIG_NO_EXISTS_ERROR);
        }
        HrmsAttendanceCycleConfigDO attendanceCycleConfig = attendanceCycleConfigList.get(0);
        Date cycleStartDate = null;
        Date cycleEndDate = null;

        if (StringUtils.equalsIgnoreCase(attendanceCycleConfig.getCycleEnd(), "END_OF_MONTH")) {
            cycleStartDate = DateUtil.beginOfMonth(DateUtil.parse(queryDTO.getCycleMonth(), "yyyyMM"));
            cycleEndDate = DateUtil.endOfMonth(DateUtil.parse(queryDTO.getCycleMonth(), "yyyyMM"));
        } else {
            cycleStartDate = DateUtil.offsetMonth(DateUtil.parse(queryDTO.getCycleMonth() + attendanceCycleConfig.getCycleStart(), "yyyyMMdd"), -1);
            cycleEndDate = DateUtil.parse(DateUtil.format(DateUtil.offsetMonth(cycleStartDate, 1), "yyyyMM") + attendanceCycleConfig.getCycleEnd(), "yyyyMMdd");
        }
        Long cycleStartDayId = Long.valueOf(DateUtil.format(cycleStartDate, "yyyyMMdd"));
        Long cycleEndDayId = Long.valueOf(DateUtil.format(cycleEndDate, "yyyyMMdd"));

        //buildTitleDTO(resultList, "sr", RequestInfoHolder.isChinese() ? "序号" : "Sr");
        buildTitleDTO(resultList, "employeeId", "HRMS ID");
        buildTitleDTO(resultList, "name", RequestInfoHolder.isChinese() ? "姓名" : "Name");
        buildTitleDTO(resultList, "nationality", RequestInfoHolder.isChinese() ? "国籍" : "Nationality");
        buildTitleDTO(resultList, "country", RequestInfoHolder.isChinese() ? "常驻国" : "Country");
        buildTitleDTO(resultList, "settlementCenterName", RequestInfoHolder.isChinese() ? "签约主体" : "Contract Entity");
        buildTitleDTO(resultList, "vendorName", RequestInfoHolder.isChinese() ? "供应商" : "Vendor Name");

        //buildTitleDTO(resultList, "organizationCode", "Organization Code");
        buildTitleDTO(resultList, "department", RequestInfoHolder.isChinese() ? "部门" : "Department");
        buildTitleDTO(resultList, "designation", RequestInfoHolder.isChinese() ? "岗位" : "Designation");
        buildTitleDTO(resultList, "joiningDate", RequestInfoHolder.isChinese() ? "入职日期" : "Joining Date");
        buildTitleDTO(resultList, "staffType", RequestInfoHolder.isChinese() ? "员工性质" : "Staff Type");
        buildTitleDTO(resultList, "employeeStatus", RequestInfoHolder.isChinese() ? "员工状态" : "Employee Status");
        buildTitleDTO(resultList, "dimissionDate", RequestInfoHolder.isChinese() ? "离职日期" : "Dimission Date");
        buildTitleDTO(resultList, "accountStatus", RequestInfoHolder.isChinese() ? "账号状态" : "Account Status");
        buildTitleDTO(resultList, "disabledDate", RequestInfoHolder.isChinese() ? "停用日期" : "DisabledDate");


        Long tempDayId = cycleStartDayId;
        //获取每天的出勤情况
        while (tempDayId <= cycleEndDayId) {
            buildTitleDTO(resultList, "day" + tempDayId.toString(), tempDayId.toString());
            //后移一天
            tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));
        }
        //TODO 这里注意，标头字段不能随意改，SalaryItemReportValueEnum字典维护了，薪资会用到
        buildTitleDTO(resultList, "attendanceDays", RequestInfoHolder.isChinese() ? "应出勤天数" : "Attendance days");
        buildTitleDTO(resultList, "present", RequestInfoHolder.isChinese() ? "实际出勤天数" : "Present");
        buildTitleDTO(resultList, "offDay", RequestInfoHolder.isChinese() ? "排休天数" : "OFF-Day");
        buildTitleDTO(resultList, "attendanceHour", RequestInfoHolder.isChinese() ? "应出勤小时数" : "Attendance hour");
        buildTitleDTO(resultList, "presentHour", RequestInfoHolder.isChinese() ? "实际出勤小时数" : "Present(h)");


        buildTitleDTO(resultList, "lateCount", RequestInfoHolder.isChinese() ? "迟到次数" : "Late");
        buildTitleDTO(resultList, "lateMinutes", RequestInfoHolder.isChinese() ? "迟到分钟数" : "Late(min)");
        buildTitleDTO(resultList, "leaveEarlyCount", RequestInfoHolder.isChinese() ? "早退次数" : "Early Leave");
        buildTitleDTO(resultList, "leaveEarlyMinutes", RequestInfoHolder.isChinese() ? "早退分钟数" : "Early Leave(min)");
        buildTitleDTO(resultList, "absentDays", RequestInfoHolder.isChinese() ? "缺勤天数" : "Absent(d)");
        buildTitleDTO(resultList, "absentHours", RequestInfoHolder.isChinese() ? "缺勤小时数" : "Absent(h)");
        buildTitleDTO(resultList, "absentCount", RequestInfoHolder.isChinese() ? "缺勤次数" : "Absent");

        buildTitleDTO(resultList, "beforeOfficeLackCount", RequestInfoHolder.isChinese() ? "上班缺卡次数" : "Missed Punch-in");
        buildTitleDTO(resultList, "afterOfficeLackCount", RequestInfoHolder.isChinese() ? "下班缺卡次数" : "Missed Punch-out");
        buildTitleDTO(resultList, "abnormalDurationCount", RequestInfoHolder.isChinese() ? "时长异常次数" : "Abnormal Duration");
        buildTitleDTO(resultList, "abnormalDurationMinutes", RequestInfoHolder.isChinese() ? "时长异常分钟数" : "Abnormal Duration(min)");
        buildTitleDTO(resultList, "reissueCardCount", RequestInfoHolder.isChinese() ? "补卡次数" : "Punch-in/out Correction Times");

        buildTitleDTO(resultList, "outOfOfficeHours", RequestInfoHolder.isChinese() ? "外勤（小时）" : "out of office(h)");

        List<HrmsCompanyLeaveConfigDO> companyLeaveConfigDOS = hrmsCompanyLeaveConfigManage.selectLeaveConfigByCountryList(Arrays.asList(queryDTO.getCountry()));
        for (HrmsCompanyLeaveConfigDO companyLeaveConfigDO : companyLeaveConfigDOS) {
            buildTitleDTO(resultList, companyLeaveConfigDO.getLeaveName(), companyLeaveConfigDO.getLeaveName());
        }
        return resultList;
    }

    @Override
    public List<AttendanceTitleExportDTO> attendanceDayTitleExport(AttendanceExportQueryDTO queryDTO) {
        if (StringUtils.isBlank(queryDTO.getCountry())) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.COUNTRY_NOT_EMPTY);
        }
        List<String> countryList = Arrays.asList(queryDTO.getCountry());
        List<HrmsCompanyLeaveConfigDO> companyLeaveConfigDOS = hrmsCompanyLeaveConfigManage.selectLeaveConfigByCountryList(countryList);

        List<AttendanceTitleExportDTO> resultList = new ArrayList<>();
        //buildTitleDTO(resultList, "sr", RequestInfoHolder.isChinese() ? "序号" : "Sr");
        buildTitleDTO(resultList, "employeeId", "HRMS ID");
        buildTitleDTO(resultList, "name", RequestInfoHolder.isChinese() ? "姓名" : "Name");
        buildTitleDTO(resultList, "nationality", RequestInfoHolder.isChinese() ? "国籍" : "Nationality");
        buildTitleDTO(resultList, "country", RequestInfoHolder.isChinese() ? "常驻国" : "Country");
        buildTitleDTO(resultList, "locationCity", RequestInfoHolder.isChinese() ? "常驻地城市" : "Location City");
        buildTitleDTO(resultList, "settlementCenterName", RequestInfoHolder.isChinese() ? "签约主体" : "Contract Entity");
        buildTitleDTO(resultList, "vendorName", RequestInfoHolder.isChinese() ? "供应商" : "Vendor Name");

        //buildTitleDTO(resultList, "organizationCode", "Organization Code");
        buildTitleDTO(resultList, "department", RequestInfoHolder.isChinese() ? "部门" : "Department");
        buildTitleDTO(resultList, "designation", RequestInfoHolder.isChinese() ? "岗位" : "Designation");
        buildTitleDTO(resultList, "joiningDate", RequestInfoHolder.isChinese() ? "入职日期" : "Joining Date");
        buildTitleDTO(resultList, "staffType", RequestInfoHolder.isChinese() ? "员工性质" : "Staff Type");
        buildTitleDTO(resultList, "employeeStatus", RequestInfoHolder.isChinese() ? "员工状态" : "Employee Status");
        buildTitleDTO(resultList, "dimissionDate", RequestInfoHolder.isChinese() ? "离职日期" : "Dimission Date");
        buildTitleDTO(resultList, "accountStatus", RequestInfoHolder.isChinese() ? "账号状态" : "Account Status");
        buildTitleDTO(resultList, "disabledDate", RequestInfoHolder.isChinese() ? "停用日期" : "DisabledDate");

        buildTitleDTO(resultList, "attendanceDate", RequestInfoHolder.isChinese() ? "考勤日期" : "Attendance Date");
        buildTitleDTO(resultList, "attendanceStatus", RequestInfoHolder.isChinese() ? "考勤状态" : "Attendance Status");
        buildTitleDTO(resultList, "abnormalType", RequestInfoHolder.isChinese() ? "状态说明" : "Abnormal type");
        buildTitleDTO(resultList, "punchRuleName", RequestInfoHolder.isChinese() ? "打卡规则名称" : "Punch rule name");
        buildTitleDTO(resultList, "punchRuleType", RequestInfoHolder.isChinese() ? "打卡类型" : "Punch rule type");
        buildTitleDTO(resultList, "shiftName", RequestInfoHolder.isChinese() ? "班次名称" : "Shift Name");

        buildTitleDTO(resultList, "punchArea", RequestInfoHolder.isChinese() ? "打卡区域" : "Punch Area");
        buildTitleDTO(resultList, "earliestPunchInTime1", RequestInfoHolder.isChinese() ? "上班1打卡时间" : "Punch In Time 1");
        buildTitleDTO(resultList, "earliestPunchInResult1", RequestInfoHolder.isChinese() ? "上班1打卡结果" : "Punch In Result 1");
        buildTitleDTO(resultList, "LatestPunchOutTime1", RequestInfoHolder.isChinese() ? "下班1打卡时间" : "Punch Out Time 1");
        buildTitleDTO(resultList, "LatestPunchOutResult1", RequestInfoHolder.isChinese() ? "下班1打卡结果" : "Punch Out Result 1");
        buildTitleDTO(resultList, "earliestPunchInTime2", RequestInfoHolder.isChinese() ? "上班2打卡时间" : "Punch In Time 2");
        buildTitleDTO(resultList, "earliestPunchInResult2", RequestInfoHolder.isChinese() ? "上班2打卡结果" : "Punch In Result 2");
        buildTitleDTO(resultList, "LatestPunchOutTime2", RequestInfoHolder.isChinese() ? "下班2打卡时间" : "Punch Out Time 2");
        buildTitleDTO(resultList, "LatestPunchOutResult2", RequestInfoHolder.isChinese() ? "下班2打卡结果" : "Punch Out Result 2");
        buildTitleDTO(resultList, "earliestPunchInTime3", RequestInfoHolder.isChinese() ? "上班3打卡时间" : "Punch In Time 3");
        buildTitleDTO(resultList, "earliestPunchInResult3", RequestInfoHolder.isChinese() ? "上班3打卡结果" : "Punch In Result 3");
        buildTitleDTO(resultList, "LatestPunchOutTime3", RequestInfoHolder.isChinese() ? "下班3打卡时间" : "Punch Out Time 3");
        buildTitleDTO(resultList, "LatestPunchOutResult3", RequestInfoHolder.isChinese() ? "下班3打卡结果" : "Punch Out Result 3");
        buildTitleDTO(resultList, "approvalForm", RequestInfoHolder.isChinese() ? "关联的审批单" : "Associated approval form");
        buildTitleDTO(resultList, "attendanceHour", RequestInfoHolder.isChinese() ? "应出勤小时数" : "Attendance hour");
        buildTitleDTO(resultList, "actualHoursRest", RequestInfoHolder.isChinese() ? "实际出勤小时数(含休息)" : "Actual attendance hour(including rest)");
        buildTitleDTO(resultList, "actualHours", RequestInfoHolder.isChinese() ? "实际出勤小时数(不含休息)" : "Actual attendance hour(excluding rest)");
        buildTitleDTO(resultList, "presentHour", RequestInfoHolder.isChinese() ? "有效出勤小时数" : "Present(h)");
        buildTitleDTO(resultList, "outOfOfficeHours", RequestInfoHolder.isChinese() ? "外勤（小时）" : "out of office(h)");

        buildTitleDTO(resultList, "lateCount", RequestInfoHolder.isChinese() ? "迟到次数" : "Late");
        buildTitleDTO(resultList, "lateMinutes", RequestInfoHolder.isChinese() ? "迟到分钟数" : "Late(min)");
        buildTitleDTO(resultList, "leaveEarlyCount", RequestInfoHolder.isChinese() ? "早退次数" : "Early Leave");
        buildTitleDTO(resultList, "leaveEarlyMinutes", RequestInfoHolder.isChinese() ? "早退分钟数" : "Early Leave(min)");
        buildTitleDTO(resultList, "absentCount", RequestInfoHolder.isChinese() ? "缺勤次数" : "Absent");
        buildTitleDTO(resultList, "absentHours", RequestInfoHolder.isChinese() ? "缺勤小时数" : "Absent(h)");

        buildTitleDTO(resultList, "beforeOfficeLackCount", RequestInfoHolder.isChinese() ? "上班缺卡次数" : "Missed Punch-in");
        buildTitleDTO(resultList, "afterOfficeLackCount", RequestInfoHolder.isChinese() ? "下班缺卡次数" : "Missed Punch-out");
        buildTitleDTO(resultList, "abnormalDurationCount", RequestInfoHolder.isChinese() ? "时长异常次数" : "Abnormal Duration");
        buildTitleDTO(resultList, "abnormalDurationMinutes", RequestInfoHolder.isChinese() ? "时长异常分钟数" : "Abnormal Duration(min)");
        buildTitleDTO(resultList, "reissueCardCount", RequestInfoHolder.isChinese() ? "补卡次数" : "Punch-in/out Correction Times");

        //获取假期类型
        Map<String, DictVO> leaveTypeEnumMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.HRMS_ATTENDANCE_LEAVE_TYPE);
        Map<String, DictVO> lowerleaveTypeEnumMap = leaveTypeEnumMap.entrySet().stream().collect(Collectors.toMap(item -> item.getKey().toLowerCase(), Map.Entry::getValue));
        for (HrmsCompanyLeaveConfigDO companyLeaveConfigDO : companyLeaveConfigDOS) {
            String leaveName = companyLeaveConfigDO.getLeaveName();
            String leaveTypeName = companyLeaveConfigDO.getLeaveName();
            DictVO dictVO = lowerleaveTypeEnumMap.get(leaveName.toLowerCase());
            if (Objects.nonNull(dictVO)) {
                leaveTypeName = dictVO.getDataValue();
            }
            if (resultList.contains(leaveName)) {
                continue;
            }
            buildTitleDTO(resultList, leaveName, leaveTypeName);
        }
        return resultList;
    }

    private void buildTitleDTO(List<AttendanceTitleExportDTO> resultList, String title, String name) {
        AttendanceTitleExportDTO exportDTO = new AttendanceTitleExportDTO();
        exportDTO.setTitle(title);
        exportDTO.setName(name);
        resultList.add(exportDTO);
    }

    private void buildTitleWidthDTO(List<AttendanceTitleExportDTO> resultList
            , String title, String name, String colspan, String rowspan) {
        AttendanceTitleExportDTO exportDTO = new AttendanceTitleExportDTO();
        exportDTO.setTitle(title);
        exportDTO.setName(name);
        exportDTO.setColspan(colspan);
        exportDTO.setRowspan(rowspan);
        resultList.add(exportDTO);
    }

    @Override
    public PaginationResult<Map<String, String>> attendanceMonthExport(AttendanceExportQueryDTO queryDTO) {
        log.info("attendanceMonthExport | queryDTO:{}", JSON.toJSONString(queryDTO));
        if (StringUtils.isBlank(queryDTO.getCycleMonth()) || StringUtils.isBlank(queryDTO.getCountry())) {
            return new PaginationResult<>();
        }
        // 拼接部门查询条件
        if (StringUtils.isNotBlank(queryDTO.getDeptIdString())) {
            List<String> deptIdStringList = Arrays.asList(queryDTO.getDeptIdString().split(","));
            List<Long> deptIdList = new ArrayList<>();
            deptIdStringList.forEach(item -> {
                deptIdList.add(Long.valueOf(item));
            });
            queryDTO.setDeptIds(deptIdList);
        }
        try {
            // 考勤方案
            AttendanceCycleConfigQuery cycleQuery = new AttendanceCycleConfigQuery();
            cycleQuery.setCountry(queryDTO.getCountry());
            cycleQuery.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
            List<HrmsAttendanceCycleConfigDO> attendanceCycleConfigList = hrmsAttendanceCycleConfigDao.selectByCondition(cycleQuery);
            // 过滤为激活状态的
            attendanceCycleConfigList = attendanceCycleConfigList.stream().filter(attendanceCycleConfigDO ->
                    ObjectUtil.equal(attendanceCycleConfigDO.getStatus(), StatusEnum.ACTIVE.getCode())).collect(Collectors.toList());

            if (CollUtil.isEmpty(attendanceCycleConfigList)) {
                return new PaginationResult();
            }
            HrmsAttendanceCycleConfigDO attendanceCycleConfig = attendanceCycleConfigList.get(0);
            Date cycleStartDate = null;
            Date cycleEndDate = null;

            if (StringUtils.equalsIgnoreCase(attendanceCycleConfig.getCycleEnd(), "END_OF_MONTH")) {
                cycleStartDate = DateUtil.beginOfMonth(DateUtil.parse(queryDTO.getCycleMonth(), "yyyyMM"));
                cycleEndDate = DateUtil.endOfMonth(DateUtil.parse(queryDTO.getCycleMonth(), "yyyyMM"));
            } else {
                cycleStartDate = DateUtil.offsetMonth(DateUtil.parse(queryDTO.getCycleMonth() + attendanceCycleConfig.getCycleStart(), "yyyyMMdd"), -1);
                cycleEndDate = DateUtil.parse(DateUtil.format(DateUtil.offsetMonth(cycleStartDate, 1), "yyyyMM") + attendanceCycleConfig.getCycleEnd(), "yyyyMMdd");
            }


            // 封装考勤数据查询条件
            UserAttendanceQuery query = attendanceBeforeHandler(Boolean.FALSE, queryDTO, null, null, null);
            if (queryDTO.getIsDriver().equals(BusinessConstant.Y)) {
                query.setIsDriver(BusinessConstant.Y);
            } else {
                query.setIsDriver(BusinessConstant.N);
            }

            query.setStartTime(cycleStartDate);
            query.setEndTime(cycleEndDate);
            log.info("考勤数据查询条件：{}", JSON.toJSONString(query));
            // 查询员工出勤列表信息
            PageInfo<AttendanceDTO> pageInfo = queryUserAttendanceList(query);
            // 处理返回的数据
            attendanceAfterHandler(pageInfo, queryDTO);
            // 获取员工出勤列表数据
            List<AttendanceDTO> attendanceDTOList = pageInfo.getList();
            List<Map<String, String>> resultList = new ArrayList<>();
            monthExportHandler(attendanceDTOList, resultList, cycleStartDate, cycleEndDate);
            return getPageResult(resultList, queryDTO, (int) pageInfo.getTotal(), pageInfo.getPages());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("考勤月报表导出报错", e);
            throw BusinessException.get(HrmsErrorCodeEnums.ATTENDANCE_MONTH_EXPORT_ERROR.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ATTENDANCE_MONTH_EXPORT_ERROR.getDesc()));
        }
    }

    @Override
    public List<Map<String, String>> attendanceMonthHandler(List<String> userCodeList, Date startTime, Date endTime) {
        log.info("attendanceMonthHandler | userIdList:{}", JSON.toJSONString(userCodeList));
        List<Map<String, String>> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(userCodeList) || startTime == null || endTime == null) {
            return resultList;
        }
        List<AttendanceDTO> attendanceDTOList = new ArrayList<>();
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectUserInfoByCodes(userCodeList);
        for (HrmsUserInfoDO userInfoDO : userInfoDOList) {
            AttendanceDTO attendanceDTO = BeanUtils.convert(userInfoDO, AttendanceDTO.class);
            attendanceDTO.setUserId(userInfoDO.getId());
            attendanceDTOList.add(attendanceDTO);
        }
        try {
            monthExportHandler(attendanceDTOList, resultList, startTime, endTime);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("薪资获取考勤月报表数据报错", e);
            throw BusinessException.get(HrmsErrorCodeEnums.ATTENDANCE_MONTH_EXPORT_ERROR.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ATTENDANCE_MONTH_EXPORT_ERROR.getDesc()));
        }
        if (CollectionUtils.isNotEmpty(resultList)) {
            log.info("attendanceMonthHandler|resultList:{}", JSON.toJSON(resultList));
        }
        return resultList;
    }


    private void monthExportHandler(List<AttendanceDTO> attendanceDTOList, List<Map<String, String>> resultList, Date startTime, Date endTime) {
        Long cycleStartDayId = Long.valueOf(DateUtil.format(startTime, "yyyyMMdd"));
        Long cycleEndDayId = Long.valueOf(DateUtil.format(endTime, "yyyyMMdd"));
        // 获取员工考勤数据的用户id列表
        List<Long> userIdList = attendanceDTOList.stream().map(UserInformationDTO::getUserId).collect(Collectors.toList());
        // 根绝用户id列表获取所有用户信息
        List<HrmsUserInfoDO> userInfoDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userIdList)) {
            userInfoDOList = hrmsUserInfoDao.listByIds(userIdList);
        }
        // 将用户信息列表转换为用户id作为key的map
        Map<Long, HrmsUserInfoDO> userInfoMap = userInfoDOList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getId, o -> o, (v1, v2) -> v1));
        // 获取用户的所有用户code
        List<String> userCodeList = userInfoDOList.stream().map(HrmsUserInfoDO::getUserCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        //通过用户id获取这些用户的入职信息
        List<HrmsUserEntryRecordDO> entryRecordDOList = hrmsUserEntryRecordManage.selectUserEntryByUserIds(userIdList);
        // 将入职信息列表转换为用户id作为key的map
        Map<Long, HrmsUserEntryRecordDO> entryRecordMaps = entryRecordDOList.stream().collect(Collectors.toMap(HrmsUserEntryRecordDO::getUserId, o -> o, (v1, v2) -> v1));
        //通过用户id获取这些用户的离职信息
        List<HrmsUserDimissionRecordDO> dimissionRecordDOList = hrmsUserDimissionRecordDao.listByUserIds(userIdList).stream().filter(item -> StringUtils.equalsIgnoreCase(item.getDimissionStatus(), DimissionStatusEnum.DIMISSION.getCode())).collect(Collectors.toList());
        // 将离职信息列表转换为用户id作为key的map
        Map<Long, HrmsUserDimissionRecordDO> dimissionRecordMaps = dimissionRecordDOList.stream().collect(Collectors.toMap(HrmsUserDimissionRecordDO::getUserId, o -> o, (v1, v2) -> v1));
        // 获取用工类型
        Map<String, DictVO> employeeTypeMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE);

        // 获取计薪周期内的这些用户的出勤明细信息
        List<HrmsAttendanceEmployeeDetailDO> employeeDetailDOList = hrmsAttendanceEmployeeDetailManage.selectAttendanceByCycleDay(userIdList, cycleStartDayId, cycleEndDayId);

        //获取计薪周期内的这些用户的所有异常出勤信息
        List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectAbnormalAttendanceByCycleDay(userIdList, cycleStartDayId, cycleEndDayId);

        //查询员工所有排班，然后查出打卡规则（需要获取当天的法定工作时长）
        List<HrmsAttendanceClassEmployeeConfigDO> classEmployeeConfigDOList = hrmsAttendanceClassEmployeeConfigManage.selectRecordByUserIdList(userIdList, cycleStartDayId, cycleEndDayId);
        // 获取排班班次id
        List<Long> classIdList = classEmployeeConfigDOList.stream().map(HrmsAttendanceClassEmployeeConfigDO::getClassId).filter(Objects::nonNull).collect(Collectors.toList());
        // 根据班次id获取排班班次配置信息
//        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = hrmsAttendancePunchClassConfigManage.selectClassByIdList(classIdList);
        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = punchConfigManageAdapter.selectClassByIdList(classIdList);
        // 根据班次id获取排班详情信息
//        List<HrmsAttendancePunchClassItemConfigDO> classItemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(classIdList);
        List<HrmsAttendancePunchClassItemConfigDO> classItemConfigDOList = punchConfigManageAdapter.selectItemConfigByClassId(classIdList);
        // 获取打卡规则id
        List<Long> punchConfigIdList = classEmployeeConfigDOList.stream().map(HrmsAttendanceClassEmployeeConfigDO::getPunchConfigId).filter(Objects::nonNull).collect(Collectors.toList());
        // 根据打卡规则id获取打卡规则信息
//        List<HrmsAttendancePunchConfigDO> punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByIdList(punchConfigIdList);
        List<HrmsAttendancePunchConfigDO> punchConfigDOList = punchConfigManageAdapter.selectAttendancePunchByIdList(punchConfigIdList);

        //免打卡需要用到
        AttendancePunchConfigRangeByDateQuery configRangeByDateQuery = new AttendancePunchConfigRangeByDateQuery();
        configRangeByDateQuery.setUserIds(userIdList);
        // 获取打卡规则范围信息
//        List<HrmsAttendancePunchConfigRangeDO> allPunchConfigRangeDOS = hrmsAttendancePunchConfigRangeManage.selectPunchConfigRangeByDate(configRangeByDateQuery);
        List<HrmsAttendancePunchConfigRangeDO> allPunchConfigRangeDOS = punchConfigManageAdapter.selectPunchConfigRangeByDate(configRangeByDateQuery);

        //获取用户改年的所有的打卡记录
        EmployeePunchCardRecordQuery employeePunchCardRecordQuery = new EmployeePunchCardRecordQuery();
        employeePunchCardRecordQuery.setStartTime(DateUtil.beginOfDay(DateUtil.offsetDay(startTime, -2)));
        employeePunchCardRecordQuery.setEndTime(DateUtil.endOfDay(DateUtil.offsetDay(endTime, 2)));
        employeePunchCardRecordQuery.setUserCodes(userCodeList);
        // 获取用户所有的打卡记录
        List<EmployeePunchRecordDO> allPunchRecordList = punchRecordDao.listRecords(employeePunchCardRecordQuery);
        // 转换打卡记录信息
        List<UserPunchRecordDTO> allPunchRecordDTOList = new ArrayList<>();
        for (EmployeePunchRecordDO employeePunchRecordDO : allPunchRecordList) {
            UserPunchRecordDTO userPunchRecordDTO = new UserPunchRecordDTO();
            userPunchRecordDTO.setId(employeePunchRecordDO.getId());
            userPunchRecordDTO.setUserCode(employeePunchRecordDO.getUserCode());
            userPunchRecordDTO.setFormId(employeePunchRecordDO.getFormId());
            String punchTimeString = DateUtil.format(employeePunchRecordDO.getPunchTime(), "yyyy-MM-dd HH:mm");
            userPunchRecordDTO.setFormatPunchTime(DateUtil.parse(punchTimeString + ":00", "yyyy-MM-dd HH:mm:ss"));
            allPunchRecordDTOList.add(userPunchRecordDTO);
        }
        //获取用户所有审批通过的请假/外勤/补时长/补卡单据
        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setUserIdList(userIdList);
        applicationFormQuery.setStatusList(Collections.singletonList(HrAttendanceApplicationFormStatusEnum.PASS.getCode()));
        applicationFormQuery.setFromTypeList(Arrays.asList(HrAttendanceApplicationFormTypeEnum.REISSUE_CARD.getCode(), HrAttendanceApplicationFormTypeEnum.LEAVE.getCode(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode(), HrAttendanceApplicationFormTypeEnum.ADD_DURATION.getCode()));
        // 获取所有审核通过的申请单
        List<HrmsApplicationFormDO> allPassFormDOList = hrmsApplicationFormManage.selectForm(applicationFormQuery);
        // 获取所有申请单id
        List<Long> allPassFormIdList = allPassFormDOList.stream().map(HrmsApplicationFormDO::getId).collect(Collectors.toList());
        // 获取所有申请单关联的申请单详情数据
        List<HrmsApplicationFormAttrDO> allPassFormAttrDOList = hrmsApplicationFormAttrManage.selectFormAttrByFormIdLit(allPassFormIdList);

        // 过滤获取所有请假/外勤/补时长单据
        List<HrmsApplicationFormDO> leavePassFormDOList = allPassFormDOList.stream().filter(item -> Arrays.asList(HrAttendanceApplicationFormTypeEnum.LEAVE.getCode(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode(), HrAttendanceApplicationFormTypeEnum.ADD_DURATION.getCode()).contains(item.getFormType())).collect(Collectors.toList());
        List<Long> leavePassFormIdList = leavePassFormDOList.stream().map(HrmsApplicationFormDO::getId).collect(Collectors.toList());
        List<HrmsApplicationFormAttrDO> leavePassFormAttrDOList = allPassFormAttrDOList.stream().filter(item -> leavePassFormIdList.contains(item.getFormId())).collect(Collectors.toList());

        // 过滤获取所有补卡 数据
        List<HrmsApplicationFormDO> reissuePassFormDOList = allPassFormDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getFormType(), HrAttendanceApplicationFormTypeEnum.REISSUE_CARD.getCode())).collect(Collectors.toList());
        List<Long> reissuePassFormIdList = reissuePassFormDOList.stream().map(HrmsApplicationFormDO::getId).collect(Collectors.toList());
        List<HrmsApplicationFormAttrDO> reissuePassFormAttrDOList = allPassFormAttrDOList.stream().filter(item -> reissuePassFormIdList.contains(item.getFormId())).collect(Collectors.toList());

        //获取所有签约主体
        List<HrmsSettlementCenterVO> settlementDTOS = hrmsBaseDataService.getSettlementCenterSelectList();
        Map<Long, HrmsSettlementCenterVO> settlementDTOMap = settlementDTOS.stream().collect(Collectors.toMap(HrmsSettlementCenterVO::getCompanyOrgId, o -> o, (v1, v2) -> v1));

        // 获取当前时间
        Long nowDayId = Long.valueOf(DateUtil.format(DateUtil.date(), "yyyyMMdd"));

        //int Sr = 0;
        // 遍历所有周期内的考勤信息
        for (AttendanceDTO attendanceDTO : attendanceDTOList) {
            // 获取当前用户信息
            HrmsUserInfoDO userInfoDO = userInfoMap.get(attendanceDTO.getUserId());
            // 获取当前用户入职记录信息
            HrmsUserEntryRecordDO entryRecordDO = entryRecordMaps.get(attendanceDTO.getUserId());
            // 获取当前用户离职记录信息
            HrmsUserDimissionRecordDO dimissionRecordDO = dimissionRecordMaps.get(attendanceDTO.getUserId());
            // 打印日志
            if (ObjectUtil.isNull(entryRecordDO)) {
                log.info("遍历所有周期内的考勤信息：userId:{},status,{},work_status:{},入职记录为null", attendanceDTO.getUserId(), userInfoDO.getStatus(), userInfoDO.getWorkStatus());
            }
            if (ObjectUtil.isNull(dimissionRecordDO)) {
                log.info("遍历所有周期内的考勤信息：userId:{},status,{},work_status:{},离职记录为null", attendanceDTO.getUserId(), userInfoDO.getStatus(), userInfoDO.getWorkStatus());
            }
            //1、如果这个人入职时间在当前结算周期以后，直接跳过
            if (StringUtils.equalsIgnoreCase(userInfoDO.getStatus(), StatusEnum.ACTIVE.getCode()) && StringUtils.equalsIgnoreCase(userInfoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
                if (ObjectUtil.isNotNull(entryRecordDO) && ObjectUtil.isNotNull(entryRecordDO.getConfirmDate()) && entryRecordDO.getConfirmDate().after(endTime)) {
                    continue;
                }
            }
            // 如果这个人的离职时间在结算时间之前，直接跳过
            if (StringUtils.equalsIgnoreCase(userInfoDO.getWorkStatus(), WorkStatusEnum.DIMISSION.getCode())) {
                if (ObjectUtil.isNotNull(dimissionRecordDO) && ObjectUtil.isNotNull(dimissionRecordDO.getActualDimissionDate()) && dimissionRecordDO.getActualDimissionDate().before(startTime)) {
                    continue;
                }
            }
            Map<String, String> map = new LinkedHashMap<>();
            resultList.add(map);
            //Sr++;
            //map.put("sr", String.valueOf(Sr));
            map.put("employeeId", userInfoDO.getUserCode());
            map.put("name", userInfoDO.getUserName());
            map.put("nationality", userInfoDO.getCountryCode());
            map.put("country", userInfoDO.getLocationCountry());
            if (StringUtils.isNotBlank(userInfoDO.getSettlementCenterCode())) {
                HrmsSettlementCenterVO userSettement = settlementDTOMap.get(Long.valueOf(userInfoDO.getSettlementCenterCode()));
                if (userSettement != null) {
                    map.put("settlementCenterName", userSettement.getSettlementCenterName());
                }
            }
            map.put("vendorName", attendanceDTO.getVendorName());
            map.put("department", attendanceDTO.getDeptName());
            map.put("designation", attendanceDTO.getPostName());
            if (entryRecordDO != null && entryRecordDO.getConfirmDate() != null) {
                map.put("joiningDate", DateUtil.format(entryRecordDO.getConfirmDate(), "yyyy-MM-dd"));
            }
            DictVO dictVO = employeeTypeMap.get(userInfoDO.getEmployeeType());
            map.put("staffType", dictVO == null ? userInfoDO.getEmployeeType() : dictVO.getDataValue());
            map.put("employeeStatus", attendanceDTO.getStatus());
            if (StringUtils.equalsIgnoreCase(attendanceDTO.getStatus(), StatusEnum.DISABLED.getCode()) && attendanceDTO.getDisabledDate() != null) {
                map.put("disabledDate", DateUtil.format(attendanceDTO.getDisabledDate(), "yyyy-MM-dd"));
            }
            if (dimissionRecordDO != null && dimissionRecordDO.getActualDimissionDate() != null) {
                map.put("dimissionDate", DateUtil.format(dimissionRecordDO.getActualDimissionDate(), "yyyy-MM-dd"));
            }

            //获取该用户的出勤数据
            List<HrmsAttendanceEmployeeDetailDO> userAttendanceEmployeeDetailDOList = employeeDetailDOList.stream()
                    .filter(item -> item.getUserId().equals(userInfoDO.getId())).collect(Collectors.toList());
            //获取该用户的异常考勤数据
            List<HrmsEmployeeAbnormalAttendanceDO> userAbnormalAttendanceDOList = abnormalAttendanceDOList.stream()
                    .filter(item -> item.getUserId().equals(userInfoDO.getId())).collect(Collectors.toList());

            // 获取该用户审核通过的请假/外勤/补时长单据数据
            List<HrmsApplicationFormDO> userLeavePassFormDOList = leavePassFormDOList.stream()
                    .filter(item -> item.getUserId().equals(userInfoDO.getId())).collect(Collectors.toList());
            List<Long> userLeaveFormIdList = userLeavePassFormDOList.stream().map(HrmsApplicationFormDO::getId).collect(Collectors.toList());
            // 获取该用户审核通过的请假/外勤单据详情数据
            List<HrmsApplicationFormAttrDO> userLeavePassFormAttrDOList = leavePassFormAttrDOList.stream().filter(item -> userLeaveFormIdList.contains(item.getFormId())).collect(Collectors.toList());

            // 获取该用户审核通过的补卡单据数据
            List<HrmsApplicationFormDO> userReissuePassFormDOList = reissuePassFormDOList.stream()
                    .filter(item -> item.getUserId().equals(userInfoDO.getId())).collect(Collectors.toList());

            // 获取该用户的打卡规则范围数据
            List<HrmsAttendancePunchConfigRangeDO> userAllPunchConfigRangeDOS = allPunchConfigRangeDOS.stream().filter(item -> item.getBizId().equals(attendanceDTO.getUserId())).collect(Collectors.toList());
            // 获取该用户的排班数据
            List<HrmsAttendanceClassEmployeeConfigDO> userClassEmployeeConfigDOList = classEmployeeConfigDOList.stream().filter(item -> item.getUserId().equals(attendanceDTO.getUserId())).collect(Collectors.toList());
            //获取该用户的打卡数据
            List<UserPunchRecordDTO> userPunchRecordDTOList = allPunchRecordDTOList.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getUserCode(), userInfoDO.getUserCode())).collect(Collectors.toList());
            //应出勤天数
            BigDecimal attendanceDays = BigDecimal.ZERO;
            //实际出勤天数
            BigDecimal actualAttendanceDays = BigDecimal.ZERO;
            //排休天数
            BigDecimal offPhDays = BigDecimal.ZERO;
            //应出勤小时数
            BigDecimal attendanceHours = BigDecimal.ZERO;
            //实际出勤小时数
            BigDecimal actualAttendanceHours = BigDecimal.ZERO;

            //缺勤天数
            BigDecimal absentDays = BigDecimal.ZERO;
            //缺勤小时数
            BigDecimal absentHours = BigDecimal.ZERO;
            //缺勤次数
            BigDecimal absentCount = BigDecimal.ZERO;

            //存储该用户该请假类型的请假天数：key-请假类型，value-请假天数
            Map<String, String> leaveMap = new HashMap<>();

            // 计薪周期开始时间
            Long tempDayId = cycleStartDayId;
            //获取每天的出勤情况
            while (tempDayId <= cycleEndDayId) {
                String mapKey = "day" + tempDayId.toString();
                String mapValue = "";
                map.put(mapKey, mapValue);

                //先看有没有正常考勤
                Long finalTempDayId = tempDayId;
                //后移一天
                tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));

                // 拦截当前计薪周期内的未来的时间，不生成记录
                if (finalTempDayId >= nowDayId) {
                    continue;
                }
                StringBuilder flag = new StringBuilder();
                //该用户是否离职
                if (StringUtils.equalsIgnoreCase(userInfoDO.getWorkStatus(), WorkStatusEnum.DIMISSION.getCode()) && dimissionRecordDO != null && dimissionRecordDO.getPlanDimissionDate() != null) {
                    Long planDimissionDayId = Long.valueOf(DateUtil.format(dimissionRecordDO.getPlanDimissionDate(), "yyyyMMdd"));
                    // 离职时间大于当前时间
                    if (planDimissionDayId.compareTo(finalTempDayId) < 0) {
                        map.put(mapKey, mapValue);
                        continue;
                    }
                    // 离职当天-特殊处理 如果是离职当天，前面加上LWD-
                    if (planDimissionDayId.compareTo(finalTempDayId) == 0) {
                        flag.append("LWD-");
                    }
                }
                // 该用户是否停用
                if (StringUtils.equalsIgnoreCase(attendanceDTO.getStatus(), StatusEnum.DISABLED.getCode()) && attendanceDTO.getDisabledDate() != null) {
                    Long disabledDayId = Long.valueOf(DateUtil.format(attendanceDTO.getDisabledDate(), "yyyyMMdd"));
                    if (disabledDayId.compareTo(finalTempDayId) < 0) {
                        map.put(mapKey, mapValue);
                        continue;
                    }
                }
                // 该用户入职之前的时间不生成记录
                if (StringUtils.equalsIgnoreCase(userInfoDO.getStatus(), StatusEnum.ACTIVE.getCode())) {
                    Long entryRecordDate = Long.valueOf(DateUtil.format(entryRecordDO.getEntryDate(), "yyyyMMdd"));
                    // 如果该用户入职日期 > 当前日期，则为空
                    if (entryRecordDate.compareTo(finalTempDayId) > 0) {
                        map.put(mapKey, mapValue);
                        continue;
                    }
                    // 入职当天，特殊处理。如果是入职当天，前面加上DOJ-【就算未来已经知道他离职了，入职当天也显示DOJ前缀】
                    if (entryRecordDate.compareTo(finalTempDayId) == 0) {
                        flag.append("DOJ-");
                    }
                }

                //获取用户当天的出勤情况
                List<HrmsAttendanceEmployeeDetailDO> dayAttendanceEmployeeDetailDOList = userAttendanceEmployeeDetailDOList.stream().filter(item -> item.getDayId().equals(finalTempDayId)).collect(Collectors.toList());
                //获取用户当天的异常出勤情况
                List<HrmsEmployeeAbnormalAttendanceDO> dayAbnormalDOList = userAbnormalAttendanceDOList.stream().filter(item -> item.getDayId().equals(finalTempDayId)).collect(Collectors.toList());
                //获取当天的排班和打卡班次信息
                List<HrmsAttendanceClassEmployeeConfigDO> dayClassEmployeeConfigDOList = userClassEmployeeConfigDOList.stream().filter(item -> item.getDayId().compareTo(finalTempDayId) == 0).collect(Collectors.toList());
                List<HrmsAttendancePunchClassConfigDO> dayClassConfigDOS = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dayClassEmployeeConfigDOList) && dayClassEmployeeConfigDOList.get(0).getClassId() != null) {
                    dayClassConfigDOS = classConfigDOS.stream().filter(item -> item.getId().equals(dayClassEmployeeConfigDOList.get(0).getClassId())).collect(Collectors.toList());
                }
                List<HrmsAttendancePunchClassItemConfigDO> dayClassItemConfigDOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                    HrmsAttendancePunchClassConfigDO classConfigDO = dayClassConfigDOS.get(0);
                    dayClassItemConfigDOList = classItemConfigDOList.stream().filter(item -> item.getPunchClassId().equals(classConfigDO.getId())).collect(Collectors.toList());
                }
                List<HrmsAttendancePunchConfigDO> dayPunchConfigDOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                    HrmsAttendancePunchClassConfigDO classConfigDO = dayClassConfigDOS.get(0);
                    dayPunchConfigDOList = punchConfigDOList.stream().filter(item -> item.getId().equals(classConfigDO.getPunchConfigId())).collect(Collectors.toList());
                }

                //获取当天的出勤时间
                BigDecimal defaultLegalWorkingHours = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS;
                List<BigDecimal> defaultAbnormalHours = dayAttendanceEmployeeDetailDOList.stream()
                        .filter(item -> item.getLegalWorkingHours() != null && item.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > 0 && item.getAttendanceMinutes() != null && item.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0).map(HrmsAttendanceEmployeeDetailDO::getLegalWorkingHours).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(defaultAbnormalHours)) {
                    defaultLegalWorkingHours = defaultAbnormalHours.get(0);
                }
                if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                    if (dayClassConfigDOS.get(0).getLegalWorkingHours() != null && dayClassConfigDOS.get(0).getLegalWorkingHours().compareTo(BigDecimal.ZERO) > -1) {
                        defaultLegalWorkingHours = dayClassConfigDOS.get(0).getLegalWorkingHours();
                    }
                }
                BigDecimal defaultLegalWorkingMinutes = defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES);

                //获取当天的假期信息/改月的请假信息
                StringBuilder dayLeaveInfo = new StringBuilder();
                // 获取该用户当天考勤数据的请假信息--->使用请假类型(后期记录为名称)分组
                Map<String, List<HrmsAttendanceEmployeeDetailDO>> leaveAttendanceMap = dayAttendanceEmployeeDetailDOList.stream()
                        .filter(item -> item.getLeaveMinutes() != null && StringUtils.isNotBlank(item.getLeaveType()))
                        .collect(Collectors.groupingBy(HrmsAttendanceEmployeeDetailDO::getLeaveType));
                // 遍历考勤请假信息
                for (Map.Entry<String, List<HrmsAttendanceEmployeeDetailDO>> entry : leaveAttendanceMap.entrySet()) {
                    BigDecimal leaveMinutes = BigDecimal.ZERO;
                    // 遍历同一种类型的请假信息--->获取该用户的该请假类型的请假总分钟数
                    for (HrmsAttendanceEmployeeDetailDO employeeDetailDO : entry.getValue()) {
                        if (employeeDetailDO.getLeaveMinutes() != null && employeeDetailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                            leaveMinutes = leaveMinutes.add(employeeDetailDO.getLeaveMinutes());
                        }
                    }
                    // 将该用户该请假类型的总请假分钟转换为天数
                    BigDecimal leaveDays = leaveMinutes.divide(BusinessConstant.MINUTES.multiply(defaultLegalWorkingHours), 4, RoundingMode.HALF_UP);
                    // 如果是一整天则省略前面的1，否则加上，比如0.75L
                    if (leaveDays.compareTo(BigDecimal.ONE) == 0) {
                        dayLeaveInfo.append(entry.getValue().get(0).getConcreteType()).append(" ");
                    } else {
                        dayLeaveInfo.append(leaveDays.toString()).append(entry.getValue().get(0).getConcreteType()).append(" ");
                    }
                    String existLeaveDays = leaveMap.get(entry.getKey());
                    if (StringUtils.isBlank(existLeaveDays)) {
                        leaveMap.put(entry.getKey(), leaveDays.toString());
                    } else {
                        leaveMap.put(entry.getKey(), leaveDays.add(new BigDecimal(existLeaveDays)).toString());
                    }
                }

                //出勤时长
                BigDecimal attendanceMinutes = BigDecimal.ZERO;
                //请假时长
                BigDecimal leaveMinutes = BigDecimal.ZERO;
                //补时长
                BigDecimal addDurationMinutes = BigDecimal.ZERO;
                //实际打卡时长（仅补时长场景）
                BigDecimal actualPunchMinutes = BigDecimal.ZERO;
                //先看该用户当天正常考勤表的数据是不是正常，统计外勤时长、补时长以及请假时长
                for (HrmsAttendanceEmployeeDetailDO detailDO : dayAttendanceEmployeeDetailDOList) {
                    if (detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        attendanceMinutes = attendanceMinutes.add(detailDO.getAttendanceMinutes());
                    }
                    if (detailDO.getLeaveMinutes() != null && detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        leaveMinutes = leaveMinutes.add(detailDO.getLeaveMinutes());
                    }
                    if (detailDO.getAddDurationMinutes() != null && (detailDO.getAddDurationMinutes().compareTo(BigDecimal.ZERO) > 0 || detailDO.getActualPunchMinutes().compareTo(BigDecimal.ZERO) > 0)) {
                        addDurationMinutes = addDurationMinutes.add(detailDO.getAddDurationMinutes());
                        actualPunchMinutes = actualPunchMinutes.add(detailDO.getActualPunchMinutes());
                    }
                }

                for (HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO : dayAbnormalDOList) {
                    //需要的是补工时未处理的异常
                    if (Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormalAttendanceDO.getAbnormalType())
                            && actualPunchMinutes.compareTo(BigDecimal.ZERO) == 0) {
                        AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
                        actualPunchMinutes = actualPunchMinutes.add(abnormalExtendDTO.getActualWorkingHours().multiply(BusinessConstant.MINUTES));
                    }
                }

                //先看用户当天是不是免打卡
                List<HrmsAttendancePunchConfigRangeDO> userDayNoPunchConfigRangeDOS = userAllPunchConfigRangeDOS.stream().filter(item -> item.getEffectTime().compareTo(DateUtil.endOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"))) < 1 && item.getExpireTime().compareTo(DateUtil.endOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"))) > -1).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userDayNoPunchConfigRangeDOS) && userDayNoPunchConfigRangeDOS.get(0).getIsNeedPunch().equals(0)) {
                    attendanceDays = attendanceDays.add(BigDecimal.ONE);
                    attendanceHours = attendanceHours.add(defaultLegalWorkingHours);
                    mapValue = "P";
                    BigDecimal dayActualAttendanceDays = BigDecimal.ONE;
                    BigDecimal dayActualAttendanceHours = defaultLegalWorkingHours;
                    //该用户当天完全请假
                    if (leaveMinutes.compareTo(defaultLegalWorkingMinutes) == 0) {
                        // 去掉请假特殊逻辑：请假一整天：excel展示"L"。使用具体的类型替换，比如"AL"、"ML"等
                        //mapValue = "L";
                        mapValue = flag.toString() + dayLeaveInfo;
                        map.put(mapKey, mapValue);
                        continue;
                    }
                    //该用户当天部分请假
                    if (leaveMinutes.compareTo(BigDecimal.ZERO) > 0) {
                        // 获取该用户该天实际出勤时长：【默认时长 - 请假时长 = 实际出勤时长】【四舍五入，保留两位小数】
                        dayActualAttendanceHours = (defaultLegalWorkingMinutes.subtract(leaveMinutes).subtract(addDurationMinutes)).divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
                        // 获取该用户该天实际出勤天数：【实际出勤时长 / 默认时长 = 实际出勤天数】【四舍五入，保留两位小数】
                        dayActualAttendanceDays = (defaultLegalWorkingMinutes.subtract(leaveMinutes).subtract(addDurationMinutes)).divide(defaultLegalWorkingMinutes, 4, RoundingMode.HALF_UP);
                        // 累加实际出勤天数
                        actualAttendanceDays = actualAttendanceDays.add(dayActualAttendanceDays);
                        // 累加实际出勤时长
                        actualAttendanceHours = actualAttendanceHours.add(dayActualAttendanceHours);
                        // excel展示的该用户该天出勤情况：【实际出勤天数 + "P" + 请假信息】
                        mapValue = flag.toString() + dayActualAttendanceDays + "P" + " " + dayLeaveInfo;
                    }
                    // 累加实际出勤天数
                    actualAttendanceDays = actualAttendanceDays.add(dayActualAttendanceDays);
                    // 累加实际出勤时长
                    actualAttendanceHours = actualAttendanceHours.add(dayActualAttendanceHours);
                    // 向map里面设置值：excel展示的该用户该天出勤情况：【实际出勤天数 + "P" + 请假信息】
                    map.put(mapKey, mapValue);
                    continue;
                }

                //该用户当天未排班(按照上班算，即该天是应出勤的)
                if (CollectionUtils.isEmpty(dayClassEmployeeConfigDOList)) {
                    // 获取该用户正常考勤数据里面的类型是否是休息日或者节假日
                    List<HrmsAttendanceEmployeeDetailDO> dayNoScheduleList = dayAttendanceEmployeeDetailDOList.stream()
                            .filter(item -> StringUtils.equalsIgnoreCase(item.getConcreteType(), PunchDayTypeEnum.PH.getCode())
                                    || StringUtils.equalsIgnoreCase(item.getConcreteType(), PunchDayTypeEnum.OFF.getCode())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(dayNoScheduleList)) {
                        //该用户当天未排班异常处理为节假日/周末
                        offPhDays = offPhDays.add(BigDecimal.ONE);
                        mapValue = flag.toString() + dayNoScheduleList.get(0).getConcreteType() + " " + dayLeaveInfo;
                        map.put(mapKey, mapValue);
                        continue;
                    }
                    //当天未排班异常处理为工作日或者异常还未处理，那么也是按照工作日计算
                    attendanceDays = attendanceDays.add(BigDecimal.ONE);
                    attendanceHours = attendanceHours.add(defaultLegalWorkingHours);
                    // 该用户该天的请假分钟与出勤分钟都为0 则为缺勤
                    if (leaveMinutes.compareTo(BigDecimal.ZERO) == 0 && attendanceMinutes.compareTo(BigDecimal.ZERO) == 0) {
                        //该用户该天异常未处理
                        mapValue = flag.toString() + "A";
                        map.put(mapKey, mapValue);
                        // 记录缺勤天数
                        absentDays = absentDays.add(BigDecimal.ONE);
                        // 记录缺勤小时
                        absentHours = absentHours.add(defaultLegalWorkingHours);
                        // 记录缺勤次数
                        absentCount = absentCount.add(BigDecimal.ONE);
                        continue;
                    }
                    // 该用户该天出勤分钟数大于0
                    if (attendanceMinutes.compareTo(BigDecimal.ZERO) > 0) {
                        // 如果该用户该天出勤分钟数 = 默认出勤分钟数，则为全天出勤
                        if (attendanceMinutes.compareTo(defaultLegalWorkingMinutes) == 0) {
                            mapValue = flag.toString() + "P";
                            map.put(mapKey, mapValue);
                            continue;
                        }
                        // 否则为部分出勤，计算实际出勤小时数
                        BigDecimal dayActualAttendanceHours = attendanceMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
                        // 计算实际出勤天数
                        BigDecimal dayActualAttendanceDays = attendanceMinutes.divide(defaultLegalWorkingMinutes, 4, RoundingMode.HALF_UP);
                        // 累加实际出勤天数
                        actualAttendanceDays = actualAttendanceDays.add(dayActualAttendanceDays);
                        // 累加实际出勤小时数
                        actualAttendanceHours = actualAttendanceHours.add(dayActualAttendanceHours);
                        // 拼接该用户该天出勤情况：【实际出勤天数 + "P"】
                        mapValue = dayActualAttendanceDays + "P";
                    }
                    // excel展示的该用户该天出勤情况：【实际出勤天数 + "P" + 请假信息】
                    mapValue = flag.toString() + mapValue + " " + dayLeaveInfo;
                    // 向map里面设置值：excel展示的该用户该天出勤情况：【实际出勤天数 + "P" + 请假信息】
                    map.put(mapKey, mapValue);
                    continue;
                }

                //该用户当天排班如果是PH/OFF就无需调用
                List<HrmsAttendanceClassEmployeeConfigDO> phEmployeeList = dayClassEmployeeConfigDOList.stream().filter(o -> StringUtils.equalsIgnoreCase(o.getDayPunchType(), PunchDayTypeEnum.PH.getCode()) || StringUtils.equalsIgnoreCase(o.getDayPunchType(), PunchDayTypeEnum.OFF.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(phEmployeeList)) {
                    // 统计排休天数
                    offPhDays = offPhDays.add(BigDecimal.ONE);
                    mapValue = flag.toString() + phEmployeeList.get(0).getDayPunchType() + " " + dayLeaveInfo;
                    map.put(mapKey, mapValue);
                    continue;
                }

                // （ 没有排班 + 有排班但是是PH/OFF ）之后的就只剩下有排班不是PH/OFF的情况了。统计该用户应出勤天数
                attendanceDays = attendanceDays.add(BigDecimal.ONE);
                // 统计该用户应出勤小时数
                attendanceHours = attendanceHours.add(defaultLegalWorkingHours);

                // 请假申请时长
                BigDecimal formLeaveMinutes = BigDecimal.ZERO;
                // 外勤时长
                BigDecimal formOutOffOfficeMinutes = BigDecimal.ZERO;
                for (HrmsAttendanceEmployeeDetailDO detailDO : dayAttendanceEmployeeDetailDOList) {
                    if (detailDO.getFormId() != null && detailDO.getLeaveMinutes() != null && detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        formLeaveMinutes = formLeaveMinutes.add(detailDO.getLeaveMinutes());
                    }
                    if (detailDO.getFormId() != null && detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        formOutOffOfficeMinutes = formOutOffOfficeMinutes.add(detailDO.getAttendanceMinutes());
                    }
                }
                //打卡的时间
                BigDecimal availableMinutes = BigDecimal.ZERO;
                //接下来需要重新计算当天的考勤，根据打卡时间来获取
                //仅仅是打卡时间
                if (CollectionUtils.isNotEmpty(dayPunchConfigDOList) && StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())) {
                    availableMinutes = userFreeDayAttendanceHandler(finalTempDayId, dayClassItemConfigDOList, userPunchRecordDTOList);
                    if (availableMinutes.compareTo(defaultLegalWorkingMinutes) > 0) {
                        availableMinutes = defaultLegalWorkingMinutes;
                    }
                    availableMinutes = availableMinutes.subtract(formLeaveMinutes).subtract(formOutOffOfficeMinutes);
                }
                if (CollectionUtils.isNotEmpty(dayPunchConfigDOList) && StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FIXED_WORK_ONCE.name())) {
                    availableMinutes = userOnceDayAttendanceHandler(finalTempDayId, dayClassItemConfigDOList, userPunchRecordDTOList, leaveMinutes, defaultLegalWorkingMinutes, formOutOffOfficeMinutes);
                    availableMinutes = availableMinutes.subtract(formLeaveMinutes).subtract(formOutOffOfficeMinutes);
                }
                if (CollectionUtils.isNotEmpty(dayPunchConfigDOList)
                        && !StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())
                        && !StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FIXED_WORK_ONCE.name())) {
                    if (addDurationMinutes.compareTo(BigDecimal.ZERO) > 0 || actualPunchMinutes.compareTo(BigDecimal.ZERO) > 0) {
                        //补时长实际打卡时间
                        availableMinutes = actualPunchMinutes;
                    } else {
                        availableMinutes = userShiftDayAttendanceHandler(finalTempDayId, dayClassItemConfigDOList, userPunchRecordDTOList, userLeavePassFormDOList, userLeavePassFormAttrDOList);
                        availableMinutes = availableMinutes.subtract(formLeaveMinutes).subtract(formOutOffOfficeMinutes);
                    }
                }
                /*
                 * 1. 前置拦截：在根据打卡记录判断考勤时长之前，先判断该用户的出勤时长是否是满勤，也就是法定时长
                 * 2. 兼容批量异常处理的情况。如果是批量异常处理：考勤表直接会生成p的记录，异常表的异常状态会是PASS
                 */
                if (attendanceMinutes.compareTo(defaultLegalWorkingMinutes) == 0) {
                    mapValue = flag.toString() + "P";
                    map.put(mapKey, mapValue);
                    actualAttendanceDays = actualAttendanceDays.add(BigDecimal.ONE);
                    actualAttendanceHours = actualAttendanceHours.add(defaultLegalWorkingHours);
                    continue;
                }
                if (availableMinutes.compareTo(BigDecimal.ZERO) == 0
                        && formLeaveMinutes.compareTo(BigDecimal.ZERO) == 0
                        && formOutOffOfficeMinutes.compareTo(BigDecimal.ZERO) == 0
                        && addDurationMinutes.compareTo(BigDecimal.ZERO) == 0) {
                    mapValue = flag.toString() + "A";
                    map.put(mapKey, mapValue);
                    absentDays = absentDays.add(BigDecimal.ONE);
                    absentHours = absentHours.add(defaultLegalWorkingHours);
                    absentCount = absentCount.add(BigDecimal.ONE);
                    continue;
                }
                BigDecimal dayAttendanceMinutes = availableMinutes.add(formOutOffOfficeMinutes).add(addDurationMinutes);
                if (dayAttendanceMinutes.compareTo(BigDecimal.ZERO) > 0) {
                    if (dayAttendanceMinutes.compareTo(defaultLegalWorkingMinutes) == 0) {
                        mapValue = flag.toString() + "P";
                        map.put(mapKey, mapValue);
                        actualAttendanceDays = actualAttendanceDays.add(BigDecimal.ONE);
                        actualAttendanceHours = actualAttendanceHours.add(defaultLegalWorkingHours);
                        continue;
                    }
                    BigDecimal dayActualAttendanceHours = dayAttendanceMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
                    BigDecimal dayActualAttendanceDays = dayAttendanceMinutes.divide(defaultLegalWorkingMinutes, 4, RoundingMode.HALF_UP);
                    actualAttendanceDays = actualAttendanceDays.add(dayActualAttendanceDays);
                    actualAttendanceHours = actualAttendanceHours.add(dayActualAttendanceHours);
                    mapValue = dayActualAttendanceDays + "P";
                }
                mapValue = flag.toString() + mapValue + " " + dayLeaveInfo;
                map.put(mapKey, mapValue);
            }

            map.put("attendanceDays", attendanceDays.toString());
            map.put("present", actualAttendanceDays.toString());
            map.put("offDay", offPhDays.toString());
            map.put("attendanceHour", attendanceHours.toString());
            map.put("presentHour", actualAttendanceHours.toString());

            map.put("absentDays", absentDays.toString());
            map.put("absentHours", absentHours.toString());
            map.put("absentCount", absentCount.toString());

            //迟到次数
            BigDecimal lateCount = BigDecimal.ZERO;
            //迟到分钟数
            BigDecimal lateMinutes = BigDecimal.ZERO;
            //早退次数
            BigDecimal leaveEarly = BigDecimal.ZERO;
            //早退分钟数
            BigDecimal leaveEarlyMinutes = BigDecimal.ZERO;
            //上班缺卡
            BigDecimal beforeOfficeLackCount = BigDecimal.ZERO;
            //下班缺卡
            BigDecimal afterOfficeLackCount = BigDecimal.ZERO;
            //补卡次数
            BigDecimal reissueCardCount = BigDecimal.ZERO;
            //时长异常次数
            BigDecimal abnormalDurationCount = BigDecimal.ZERO;
            //时长异常分钟数
            BigDecimal abnormalDurationMinutes = BigDecimal.ZERO;

            for (HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO : userAbnormalAttendanceDOList) {
                //需要的是未处理的异常
                if (AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(abnormalAttendanceDO.getStatus())) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.LATE.getCode())) {
                    lateCount = lateCount.add(BigDecimal.ONE);
                    if (StringUtils.isNotBlank(abnormalAttendanceDO.getExtend())) {
                        AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
                        lateMinutes = lateMinutes.add(BigDecimal.valueOf(DateUtil.between(abnormalExtendDTO.getActualPunchTime(), abnormalExtendDTO.getCorrectPunchTime(), DateUnit.MINUTE)));
                    }
                }
                if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode())) {
                    leaveEarly = leaveEarly.add(BigDecimal.ONE);
                    if (StringUtils.isNotBlank(abnormalAttendanceDO.getExtend())) {
                        AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
                        leaveEarlyMinutes = leaveEarlyMinutes.add(BigDecimal.valueOf(DateUtil.between(abnormalExtendDTO.getActualPunchTime(), abnormalExtendDTO.getCorrectPunchTime(), DateUnit.MINUTE)));
                    }
                }
                if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode())) {
                    beforeOfficeLackCount = beforeOfficeLackCount.add(BigDecimal.ONE);
                }
                if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode())) {
                    afterOfficeLackCount = afterOfficeLackCount.add(BigDecimal.ONE);
                }
                if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode())) {
                    abnormalDurationCount = abnormalDurationCount.add(BigDecimal.ONE);
                    if (StringUtils.isNotBlank(abnormalAttendanceDO.getExtend())) {
                        AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
                        abnormalDurationMinutes = abnormalDurationMinutes.add((abnormalExtendDTO.getLegalWorkingHours().subtract(abnormalExtendDTO.getActualWorkingHours())).multiply(BusinessConstant.MINUTES));
                    }
                }
            }

            for (HrmsApplicationFormDO formDO : userReissuePassFormDOList) {
                List<HrmsApplicationFormAttrDO> existReissuePassFormAttrDOList = reissuePassFormAttrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(existReissuePassFormAttrDOList)) {
                    continue;
                }
                Long reissueDayId = Long.valueOf(existReissuePassFormAttrDOList.get(0).getAttrValue());
                if (reissueDayId.compareTo(cycleStartDayId) > -1 && reissueDayId.compareTo(cycleEndDayId) < 1) {
                    reissueCardCount = reissueCardCount.add(BigDecimal.ONE);
                }
            }
            map.put("lateCount", lateCount.toString());
            map.put("lateMinutes", lateMinutes.toString());
            map.put("leaveEarlyCount", leaveEarly.toString());
            map.put("leaveEarlyMinutes", leaveEarlyMinutes.toString());
            map.put("beforeOfficeLackCount", beforeOfficeLackCount.toString());
            map.put("afterOfficeLackCount", afterOfficeLackCount.toString());
            map.put("reissueCardCount", reissueCardCount.toString());
            map.put("abnormalDurationCount", abnormalDurationCount.toString());
            map.put("abnormalDurationMinutes", abnormalDurationMinutes.toString());

            BigDecimal outOfOfficeMinutes = BigDecimal.ZERO;
            for (HrmsAttendanceEmployeeDetailDO detailDO : userAttendanceEmployeeDetailDOList) {
                if (detailDO.getFormId() != null && detailDO.getAttendanceMinutes() != null) {
                    outOfOfficeMinutes = outOfOfficeMinutes.add(detailDO.getAttendanceMinutes());
                }
            }
            map.put("outOfOfficeHours", outOfOfficeMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP).toString());
            for (Map.Entry<String, String> entry : leaveMap.entrySet()) {
                map.put(entry.getKey(), entry.getValue());
            }
        }
    }

    @Override
    public PaginationResult<Map<String, String>> attendanceDayExport(AttendanceExportQueryDTO queryDTO) {
        //拼接查询条件
        setExportQueryParam(queryDTO);
        UserAttendanceQuery query = attendanceBeforeHandler(Boolean.TRUE, queryDTO, null, null, null);
        if (queryDTO.getIsDriver().equals(BusinessConstant.Y)) {
            query.setIsDriver(BusinessConstant.Y);
        } else {
            query.setIsDriver(BusinessConstant.N);
        }
        // query.setShowCount(50000);
        PageInfo<AttendanceDTO> pageInfo = queryUserAttendanceList(query);
        attendanceAfterHandler(pageInfo, queryDTO);
        List<AttendanceDTO> attendanceDTOList = pageInfo.getList();
        List<Long> userIdList = attendanceDTOList.stream().map(UserInformationDTO::getUserId).collect(Collectors.toList());
        List<HrmsUserInfoDO> userInfoDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userIdList)) {
            userInfoDOList = hrmsUserInfoDao.listByIds(userIdList);
        }
        Map<Long, HrmsUserInfoDO> userInfoMap = userInfoDOList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getId, o -> o, (v1, v2) -> v1));
        List<String> userCodeList = userInfoDOList.stream().map(HrmsUserInfoDO::getUserCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        Date cycleStartDate = queryDTO.getStartTime();
        Date cycleEndDate = queryDTO.getEndTime();
        Long cycleStartDayId = Long.valueOf(DateUtil.format(cycleStartDate, "yyyyMMdd"));
        Long cycleEndDayId = Long.valueOf(DateUtil.format(cycleEndDate, "yyyyMMdd"));

        //查询所有正常考勤
        List<HrmsAttendanceEmployeeDetailDO> employeeDetailDOList = hrmsAttendanceEmployeeDetailManage.selectAttendanceByCycleDay(userIdList, cycleStartDayId, cycleEndDayId);

        //获取用户当前时间段内的所有异常
        List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = hrmsEmployeeAbnormalAttendanceManage.selectAbnormalAttendanceByCycleDay(userIdList, cycleStartDayId, cycleEndDayId);

        //查询员工改年的所有排班，然后查出打卡规则（需要获取当天的法定工作时长）
        List<HrmsAttendanceClassEmployeeConfigDO> classEmployeeConfigDOList = hrmsAttendanceClassEmployeeConfigManage.selectRecordByUserIdList(userIdList, cycleStartDayId, cycleEndDayId);
        List<Long> classIdList = classEmployeeConfigDOList.stream().map(HrmsAttendanceClassEmployeeConfigDO::getClassId).filter(Objects::nonNull).collect(Collectors.toList());
//        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = hrmsAttendancePunchClassConfigManage.selectClassByIdList(classIdList);
        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = punchConfigManageAdapter.selectClassByIdList(classIdList);
//        List<HrmsAttendancePunchClassItemConfigDO> classItemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(classIdList);
        List<HrmsAttendancePunchClassItemConfigDO> classItemConfigDOList = punchConfigManageAdapter.selectItemConfigByClassId(classIdList);
        List<Long> punchConfigIdList = classEmployeeConfigDOList.stream().map(HrmsAttendanceClassEmployeeConfigDO::getPunchConfigId).filter(Objects::nonNull).collect(Collectors.toList());
//        List<HrmsAttendancePunchConfigDO> punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByIdList(punchConfigIdList);
        List<HrmsAttendancePunchConfigDO> punchConfigDOList = punchConfigManageAdapter.selectAttendancePunchByIdList(punchConfigIdList);

        //免打卡需要用到
        AttendancePunchConfigRangeByDateQuery configRangeByDateQuery = new AttendancePunchConfigRangeByDateQuery();
        configRangeByDateQuery.setUserIds(userIdList);
//        List<HrmsAttendancePunchConfigRangeDO> allPunchConfigRangeDOS = hrmsAttendancePunchConfigRangeManage.selectPunchConfigRangeByDate(configRangeByDateQuery);
        List<HrmsAttendancePunchConfigRangeDO> allPunchConfigRangeDOS = punchConfigManageAdapter.selectPunchConfigRangeByDate(configRangeByDateQuery);

        //获取用户改年的所有的打卡记录
        EmployeePunchCardRecordQuery employeePunchCardRecordQuery = new EmployeePunchCardRecordQuery();
        employeePunchCardRecordQuery.setStartTime(DateUtil.beginOfDay(DateUtil.offsetDay(query.getStartTime(), -2)));
        employeePunchCardRecordQuery.setEndTime(DateUtil.endOfDay(DateUtil.offsetDay(query.getEndTime(), 2)));
        employeePunchCardRecordQuery.setUserCodes(userCodeList);
        List<EmployeePunchRecordDO> allPunchRecordList = punchRecordDao.listRecords(employeePunchCardRecordQuery);
        List<UserPunchRecordDTO> allPunchRecordDTOList = new ArrayList<>();
        for (EmployeePunchRecordDO employeePunchRecordDO : allPunchRecordList) {
            UserPunchRecordDTO userPunchRecordDTO = new UserPunchRecordDTO();
            userPunchRecordDTO.setId(employeePunchRecordDO.getId());
            userPunchRecordDTO.setUserCode(employeePunchRecordDO.getUserCode());
            userPunchRecordDTO.setFormId(employeePunchRecordDO.getFormId());
            userPunchRecordDTO.setPunchArea(employeePunchRecordDO.getPunchArea());
            String punchTimeString = DateUtil.format(employeePunchRecordDO.getPunchTime(), "yyyy-MM-dd HH:mm");
            userPunchRecordDTO.setFormatPunchTime(DateUtil.parse(punchTimeString + ":00", "yyyy-MM-dd HH:mm:ss"));
            allPunchRecordDTOList.add(userPunchRecordDTO);
        }
        //获取用户所有审批通过、审批中的请假/外勤/补卡单据
        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setUserIdList(userIdList);
        applicationFormQuery.setStatusList(Arrays.asList(HrAttendanceApplicationFormStatusEnum.PASS.getCode(), HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode()));
        applicationFormQuery.setFromTypeList(Arrays.asList(HrAttendanceApplicationFormTypeEnum.REISSUE_CARD.getCode(), HrAttendanceApplicationFormTypeEnum.LEAVE.getCode(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode(), HrAttendanceApplicationFormTypeEnum.ADD_DURATION.getCode()));
        List<HrmsApplicationFormDO> allFormDOList = hrmsApplicationFormManage.selectForm(applicationFormQuery);
        List<Long> allFormIdList = allFormDOList.stream().map(HrmsApplicationFormDO::getId).collect(Collectors.toList());
        List<HrmsApplicationFormAttrDO> allFormAttrDOList = hrmsApplicationFormAttrManage.selectFormAttrByFormIdLit(allFormIdList);

        List<HrmsApplicationFormDO> leaveFormDOList = allFormDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getFormType(), HrAttendanceApplicationFormTypeEnum.LEAVE.getCode()) || StringUtils.equalsIgnoreCase(item.getFormType(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode())).collect(Collectors.toList());
        List<HrmsApplicationFormDO> reissueFormDOList = allFormDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getFormType(), HrAttendanceApplicationFormTypeEnum.REISSUE_CARD.getCode())).collect(Collectors.toList());

        List<HrmsApplicationFormDO> reissuePassFormDOList = reissueFormDOList.stream().filter(item -> HrAttendanceApplicationFormStatusEnum.PASS.getCode().equals(item.getFormStatus())).collect(Collectors.toList());
        List<Long> reissuePassFormIdList = reissuePassFormDOList.stream().map(HrmsApplicationFormDO::getId).collect(Collectors.toList());
        List<HrmsApplicationFormAttrDO> reissuePassFormAttrDOList = allFormAttrDOList.stream().filter(item -> reissuePassFormIdList.contains(item.getFormId())).collect(Collectors.toList());

        //获取用户出差单据
        ApplicationFormApiQuery formApiQuery = ApplicationFormApiQuery.builder()
                .userCodeList(userCodeList)
                .statusList(Arrays.asList(HrAttendanceApplicationFormStatusEnum.PASS.getCode()
                        , HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode()))
                .build();
        List<BusinessTravelFormApiDTO> travelFormList = RpcResultProcessor.process(bizApplicationFormApi.getBusinessTravelForm(formApiQuery));

        //获取所有签约主体
        List<HrmsSettlementCenterVO> settlementDTOS = hrmsBaseDataService.getSettlementCenterSelectList();
        Map<Long, HrmsSettlementCenterVO> settlementDTOMap = settlementDTOS.stream().collect(Collectors.toMap(HrmsSettlementCenterVO::getCompanyOrgId, o -> o, (v1, v2) -> v1));

        //入职信息
        List<HrmsUserEntryRecordDO> entryRecordDOList = hrmsUserEntryRecordManage.selectUserEntryByUserIds(userIdList);
        Map<Long, HrmsUserEntryRecordDO> entryRecordMaps = entryRecordDOList.stream().collect(Collectors.toMap(HrmsUserEntryRecordDO::getUserId, o -> o, (v1, v2) -> v1));
        //离职信息
        List<HrmsUserDimissionRecordDO> dimissionRecordDOList = hrmsUserDimissionRecordDao.listByUserIds(userIdList).stream().filter(item -> StringUtils.equalsIgnoreCase(item.getDimissionStatus(), DimissionStatusEnum.DIMISSION.getCode())).collect(Collectors.toList());
        Map<Long, HrmsUserDimissionRecordDO> dimissionRecordMaps = dimissionRecordDOList.stream().collect(Collectors.toMap(HrmsUserDimissionRecordDO::getUserId, o -> o, (v1, v2) -> v1));

        Map<String, DictVO> employeeTypeMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE);

        List<Map<String, String>> resultList = new ArrayList<>();
        //int Sr = 0;
        //已每个人的维度遍历
        for (AttendanceDTO attendanceDTO : attendanceDTOList) {
            HrmsUserInfoDO userInfoDO = userInfoMap.get(attendanceDTO.getUserId());
            HrmsUserEntryRecordDO entryRecordDO = entryRecordMaps.get(attendanceDTO.getUserId());
            HrmsUserDimissionRecordDO dimissionRecordDO = dimissionRecordMaps.get(attendanceDTO.getUserId());

            List<HrmsApplicationFormDO> userLeaveFormDOList = leaveFormDOList.stream().filter(item -> item.getUserId().equals(userInfoDO.getId())).collect(Collectors.toList());
            List<HrmsApplicationFormDO> userLeavePassFormDOList = userLeaveFormDOList.stream().filter(item -> HrAttendanceApplicationFormStatusEnum.PASS.getCode().equals(item.getFormStatus())).collect(Collectors.toList());
            List<Long> userLeavePassFormIdList = userLeavePassFormDOList.stream().map(HrmsApplicationFormDO::getId).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> userLeavePassFormAttrDOList = allFormAttrDOList.stream().filter(item -> userLeavePassFormIdList.contains(item.getFormId())).collect(Collectors.toList());

            //获取用户的打卡时间
            List<UserPunchRecordDTO> userPunchRecordDTOList = allPunchRecordDTOList.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getUserCode(), userInfoDO.getUserCode())).collect(Collectors.toList());

            Long tempDayId = cycleStartDayId;
            //获取该用户每天的出勤情况
            while (tempDayId <= cycleEndDayId) {
                //先看有没有正常考勤
                Long finalTempDayId = tempDayId;
                //后移一天
                tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));

                HrmsUserDimissionRecordDO userDimissionRecordDO = dimissionRecordMaps.get(attendanceDTO.getUserId());
                if (userDimissionRecordDO != null && userDimissionRecordDO.getPlanDimissionDate() != null) {
                    Long planDimissionDateDayId = Long.valueOf(DateUtil.format(userDimissionRecordDO.getPlanDimissionDate(), "yyyyMMdd"));
                    if (finalTempDayId > planDimissionDateDayId) {
                        continue;
                    }
                }
                HrmsUserEntryRecordDO userEntryRecordDO = entryRecordMaps.get(attendanceDTO.getUserId());
                if (userEntryRecordDO != null && userEntryRecordDO.getEntryDate() != null) {
                    Long entryDateDayId = Long.valueOf(DateUtil.format(userEntryRecordDO.getEntryDate(), "yyyyMMdd"));
                    if (finalTempDayId < entryDateDayId) {
                        continue;
                    }
                }
                Map<String, String> map = new LinkedHashMap<>();
                resultList.add(map);
                //Sr++;
                //map.put("sr", String.valueOf(Sr));
                map.put("employeeId", userInfoDO.getUserCode());
                map.put("name", userInfoDO.getUserName());
                map.put("nationality", userInfoDO.getCountryCode());
                map.put("country", userInfoDO.getLocationCountry());
                if (StringUtils.isNotBlank(userInfoDO.getSettlementCenterCode())) {
                    HrmsSettlementCenterVO userSettement = settlementDTOMap.get(Long.valueOf(userInfoDO.getSettlementCenterCode()));
                    if (userSettement != null) {
                        map.put("settlementCenterName", userSettement.getSettlementCenterName());
                    }
                }
                map.put("vendorName", attendanceDTO.getVendorName());
                map.put("department", attendanceDTO.getDeptName());
                map.put("designation", attendanceDTO.getPostName());
                if (entryRecordDO != null && entryRecordDO.getEntryDate() != null) {
                    map.put("joiningDate", DateUtil.format(entryRecordDO.getEntryDate(), "yyyy-MM-dd"));
                }
                DictVO dictVO = employeeTypeMap.get(userInfoDO.getEmployeeType());
                map.put("staffType", dictVO == null ? userInfoDO.getEmployeeType() : dictVO.getDataValue());
                map.put("employeeStatus", attendanceDTO.getStatus());
                if (StringUtils.equalsIgnoreCase(attendanceDTO.getStatus(), StatusEnum.DISABLED.getCode()) && attendanceDTO.getDisabledDate() != null) {
                    map.put("disabledDate", DateUtil.format(attendanceDTO.getDisabledDate(), "yyyy-MM-dd"));
                }
                if (dimissionRecordDO != null && dimissionRecordDO.getPlanDimissionDate().before(cycleStartDate)) {
                    map.put("dimissionDate", DateUtil.format(dimissionRecordDO.getPlanDimissionDate(), "yyyy-MM-dd"));
                }


                //获取当天的日历/班次/打卡等信息
                //获取用户当天的出勤情况
                List<HrmsAttendanceEmployeeDetailDO> dayAttendanceEmployeeDetailDOList = employeeDetailDOList.stream().filter(item -> item.getUserId().equals(attendanceDTO.getUserId()) && item.getDayId().equals(finalTempDayId)).collect(Collectors.toList());
                //异常考勤
                List<HrmsEmployeeAbnormalAttendanceDO> dayAbnormalAttendanceDOList = abnormalAttendanceDOList.stream().filter(item -> item.getUserId().equals(attendanceDTO.getUserId()) && item.getDayId().equals(finalTempDayId)).collect(Collectors.toList());
                //获取当天的排班和打卡班次信息
                List<HrmsAttendanceClassEmployeeConfigDO> dayClassEmployeeConfigDOList = classEmployeeConfigDOList.stream().filter(o -> o.getUserId().equals(attendanceDTO.getUserId()) && o.getDayId().compareTo(finalTempDayId) == 0).collect(Collectors.toList());
                List<HrmsAttendancePunchClassConfigDO> dayClassConfigDOS = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dayClassEmployeeConfigDOList) && dayClassEmployeeConfigDOList.get(0).getClassId() != null) {
                    dayClassConfigDOS = classConfigDOS.stream().filter(o -> o.getId().equals(dayClassEmployeeConfigDOList.get(0).getClassId())).collect(Collectors.toList());
                }
                List<HrmsAttendancePunchClassItemConfigDO> dayClassItemConfigDOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                    HrmsAttendancePunchClassConfigDO classConfigDO = dayClassConfigDOS.get(0);
                    dayClassItemConfigDOList = classItemConfigDOList.stream().filter(o -> o.getPunchClassId().equals(classConfigDO.getId())).collect(Collectors.toList());
                }
                List<HrmsAttendancePunchConfigDO> dayPunchConfigDOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                    HrmsAttendancePunchClassConfigDO classConfigDO = dayClassConfigDOS.get(0);
                    dayPunchConfigDOList = punchConfigDOList.stream().filter(o -> o.getId().equals(classConfigDO.getPunchConfigId())).collect(Collectors.toList());
                }

                //获取当天的出勤时间
                BigDecimal defaultLegalWorkingHours = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS;
                List<BigDecimal> defaultAbnormalHours = dayAttendanceEmployeeDetailDOList.stream()
                        .filter(item -> item.getLegalWorkingHours() != null && item.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > 0 && item.getAttendanceMinutes() != null && item.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0).map(HrmsAttendanceEmployeeDetailDO::getLegalWorkingHours).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(defaultAbnormalHours)) {
                    defaultLegalWorkingHours = defaultAbnormalHours.get(0);
                }

                if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                    if (dayClassConfigDOS.get(0).getLegalWorkingHours() != null && dayClassConfigDOS.get(0).getLegalWorkingHours().compareTo(BigDecimal.ZERO) > -1) {
                        defaultLegalWorkingHours = dayClassConfigDOS.get(0).getLegalWorkingHours();
                    }
                }
                map.put("attendanceHour", defaultLegalWorkingHours.toString());
                map.put("presentHour", BigDecimal.ZERO.toString());

                map.put("attendanceDate", finalTempDayId.toString());
                if (CollectionUtils.isNotEmpty(dayPunchConfigDOList)) {
                    map.put("punchRuleName", dayPunchConfigDOList.get(0).getPunchConfigName());
                    map.put("punchRuleType", dayPunchConfigDOList.get(0).getPunchConfigType());
                }
                if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                    map.put("shiftName", dayClassConfigDOS.get(0).getClassName());
                }

                //获取当天的假期信息
                Map<String, List<HrmsAttendanceEmployeeDetailDO>> leaveAttendanceMap = dayAttendanceEmployeeDetailDOList.stream()
                        .filter(item -> item.getLeaveMinutes() != null && StringUtils.isNotBlank(item.getLeaveType()))
                        .collect(Collectors.groupingBy(HrmsAttendanceEmployeeDetailDO::getLeaveType));
                for (Map.Entry<String, List<HrmsAttendanceEmployeeDetailDO>> entry : leaveAttendanceMap.entrySet()) {
                    BigDecimal leaveMinutes = BigDecimal.ZERO;
                    for (HrmsAttendanceEmployeeDetailDO employeeDetailDO : entry.getValue()) {
                        if (employeeDetailDO.getLeaveMinutes() != null && employeeDetailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                            leaveMinutes = leaveMinutes.add(employeeDetailDO.getLeaveMinutes());
                        }
                    }
                    BigDecimal leaveDays = leaveMinutes.divide(BusinessConstant.MINUTES.multiply(defaultLegalWorkingHours), 4, RoundingMode.HALF_UP);
                    String existLeaveDays = map.get(entry.getKey());
                    if (StringUtils.isBlank(existLeaveDays)) {
                        map.put(entry.getKey(), leaveDays.toString());
                    } else {
                        map.put(entry.getKey(), leaveDays.add(new BigDecimal(existLeaveDays)).toString());
                    }
                }

                //迟到次数
                BigDecimal lateCount = BigDecimal.ZERO;
                //迟到分钟数
                BigDecimal lateMinutes = BigDecimal.ZERO;
                //早退次数
                BigDecimal leaveEarly = BigDecimal.ZERO;
                //早退分钟数
                BigDecimal leaveEarlyMinutes = BigDecimal.ZERO;
                //上班缺卡
                BigDecimal beforeOfficeLackCount = BigDecimal.ZERO;
                //下班缺卡
                BigDecimal afterOfficeLackCount = BigDecimal.ZERO;
                //补卡次数
                BigDecimal reissueCardCount = BigDecimal.ZERO;
                //时长异常次数
                BigDecimal abnormalDurationCount = BigDecimal.ZERO;
                //时长异常分钟数
                BigDecimal abnormalDurationMinutes = BigDecimal.ZERO;

                //补工时场景当前实际打卡工作时长
                BigDecimal actualPunchMinutes = BigDecimal.ZERO;
                //异常类型
                Set<String> abnormalTypeSet = new HashSet<>();
                for (HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO : dayAbnormalAttendanceDOList) {
                    //需要的是未处理的异常
                    if (AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(abnormalAttendanceDO.getStatus())) {
                        continue;
                    }
                    AttendanceAbnormalTypeEnum attendanceAbnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(abnormalAttendanceDO.getAbnormalType());
                    if (attendanceAbnormalTypeEnum != null) {
                        abnormalTypeSet.add(RequestInfoHolder.isChinese() ? attendanceAbnormalTypeEnum.getDesc() : attendanceAbnormalTypeEnum.getDescEn());
                    }
                    if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.LATE.getCode())) {
                        lateCount = lateCount.add(BigDecimal.ONE);
                        if (StringUtils.isNotBlank(abnormalAttendanceDO.getExtend())) {
                            AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
                            lateMinutes = lateMinutes.add(BigDecimal.valueOf(DateUtil.between(abnormalExtendDTO.getActualPunchTime(), abnormalExtendDTO.getCorrectPunchTime(), DateUnit.MINUTE)));
                        }
                    }
                    if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode())) {
                        leaveEarly = leaveEarly.add(BigDecimal.ONE);
                        if (StringUtils.isNotBlank(abnormalAttendanceDO.getExtend())) {
                            AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
                            leaveEarlyMinutes = leaveEarlyMinutes.add(BigDecimal.valueOf(DateUtil.between(abnormalExtendDTO.getActualPunchTime(), abnormalExtendDTO.getCorrectPunchTime(), DateUnit.MINUTE)));
                        }
                    }
                    if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode())) {
                        beforeOfficeLackCount = beforeOfficeLackCount.add(BigDecimal.ONE);
                    }
                    if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode())) {
                        afterOfficeLackCount = afterOfficeLackCount.add(BigDecimal.ONE);
                    }
                    if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getAbnormalType(), AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode())) {
                        abnormalDurationCount = abnormalDurationCount.add(BigDecimal.ONE);
                        if (StringUtils.isNotBlank(abnormalAttendanceDO.getExtend())) {
                            AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
                            abnormalDurationMinutes = abnormalDurationMinutes.add((abnormalExtendDTO.getLegalWorkingHours().subtract(abnormalExtendDTO.getActualWorkingHours())).multiply(BusinessConstant.MINUTES));
                            actualPunchMinutes = actualPunchMinutes.add(abnormalExtendDTO.getActualWorkingHours().multiply(BusinessConstant.MINUTES));
                        }
                    }
                }

                for (HrmsApplicationFormDO formDO : reissuePassFormDOList) {
                    if (!formDO.getUserId().equals(userInfoDO.getId())) {
                        continue;
                    }
                    List<HrmsApplicationFormAttrDO> existReissuePassFormAttrDOList = reissuePassFormAttrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(existReissuePassFormAttrDOList)) {
                        continue;
                    }
                    Long reissueDayId = Long.valueOf(existReissuePassFormAttrDOList.get(0).getAttrValue());
                    if (Objects.nonNull(reissueDayId) && reissueDayId.equals(finalTempDayId)) {
                        reissueCardCount = reissueCardCount.add(BigDecimal.ONE);
                    }
                }
                map.put("lateCount", lateCount.toString());
                map.put("lateMinutes", lateMinutes.toString());
                map.put("leaveEarlyCount", leaveEarly.toString());
                map.put("leaveEarlyMinutes", leaveEarlyMinutes.toString());
                map.put("beforeOfficeLackCount", beforeOfficeLackCount.toString());
                map.put("afterOfficeLackCount", afterOfficeLackCount.toString());
                map.put("reissueCardCount", reissueCardCount.toString());
                map.put("abnormalType", abnormalTypeSet.toString());
                map.put("abnormalDurationCount", abnormalDurationCount.toString());
                map.put("abnormalDurationMinutes", abnormalDurationMinutes.toString());

                BigDecimal outOfOfficeMinutes = BigDecimal.ZERO;
                for (HrmsAttendanceEmployeeDetailDO detailDO : dayAttendanceEmployeeDetailDOList) {
                    if (detailDO.getFormId() != null && detailDO.getAttendanceMinutes() != null) {
                        outOfOfficeMinutes = outOfOfficeMinutes.add(detailDO.getAttendanceMinutes());
                    }
                }
                map.put("outOfOfficeHours", outOfOfficeMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP).toString());

                //设置常住地城市
                map.put("locationCity", userInfoDO.getLocationCity());
                //新增打卡记录、打卡区域、关联审批单
                List<HrmsApplicationFormDO> userFormList = allFormDOList.stream().filter(item -> userInfoDO.getId().equals(item.getUserId())).collect(Collectors.toList());
                List<Long> userFormIdList = userFormList.stream().map(HrmsApplicationFormDO::getId).collect(Collectors.toList());
                List<HrmsApplicationFormAttrDO> userFormAttrDOList = allFormAttrDOList.stream().filter(item -> userFormIdList.contains(item.getFormId())).collect(Collectors.toList());
                //获取用户出差单
                List<BusinessTravelFormApiDTO> userTravelFormList = travelFormList.stream().filter(item -> userInfoDO.getId().equals(item.getUserId())).collect(Collectors.toList());

                // 外勤/请假/补时长/实际打卡时长
                BigDecimal attendanceMinutes = BigDecimal.ZERO;
                BigDecimal leaveMinutes = BigDecimal.ZERO;
                BigDecimal addDurationMinutes = BigDecimal.ZERO;
                //先看当天正常考勤表的数据是不是正常
                for (HrmsAttendanceEmployeeDetailDO detailDO : dayAttendanceEmployeeDetailDOList) {
                    if (detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        attendanceMinutes = attendanceMinutes.add(detailDO.getAttendanceMinutes());
                    }
                    if (detailDO.getLeaveMinutes() != null && detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        leaveMinutes = leaveMinutes.add(detailDO.getLeaveMinutes());
                    }
                    if (detailDO.getAddDurationMinutes() != null && (detailDO.getAddDurationMinutes().compareTo(BigDecimal.ZERO) > 0 || detailDO.getActualPunchMinutes().compareTo(BigDecimal.ZERO) > 0)) {
                        addDurationMinutes = addDurationMinutes.add(detailDO.getAddDurationMinutes());
                        actualPunchMinutes = actualPunchMinutes.add(detailDO.getActualPunchMinutes());
                    }
                }
                //关联用户打卡记录及审批单
                BigDecimal defaultLegalWorkingMinutes = defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES);
                handleUserPunchRecord(map, finalTempDayId, dayPunchConfigDOList, dayClassItemConfigDOList
                        , userPunchRecordDTOList, dayAbnormalAttendanceDOList, userFormList, userFormAttrDOList
                        , userTravelFormList, defaultLegalWorkingMinutes, leaveMinutes);

                //先看用户当天是不是免打卡
                List<HrmsAttendancePunchConfigRangeDO> userDayNoPunchConfigRangeDOS = allPunchConfigRangeDOS.stream().filter(item -> item.getBizId().equals(attendanceDTO.getUserId()) && item.getEffectTime().compareTo(DateUtil.endOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"))) < 1 && item.getExpireTime().compareTo(DateUtil.endOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"))) > -1).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userDayNoPunchConfigRangeDOS) && userDayNoPunchConfigRangeDOS.get(0).getIsNeedPunch().equals(0)) {
                    map.put("attendanceStatus", RequestInfoHolder.isChinese() ? "正常" : "normal");
                    map.put("abnormalType", RequestInfoHolder.isChinese() ? "免打卡" : "No Punch Card");
                    continue;
                }

                //当天正常，时间够了
                if (attendanceMinutes.add(leaveMinutes).add(addDurationMinutes).add(actualPunchMinutes).compareTo(defaultLegalWorkingMinutes) >= 0) {
                    map.put("attendanceStatus", RequestInfoHolder.isChinese() ? "正常" : "normal");
                    map.put("presentHour", (attendanceMinutes.add(addDurationMinutes).add(actualPunchMinutes).add(leaveMinutes)).divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP).toString());
                    continue;
                }

                //当天未排班（肯定是异常，如果异常被处理，会在前一步返回）
                if (CollectionUtils.isEmpty(dayClassEmployeeConfigDOList)) {
                    map.put("attendanceStatus", RequestInfoHolder.isChinese() ? "异常" : "abnormal");
                    continue;
                }

                //当天排班如果是PH/OFF就无需调用
                List<HrmsAttendanceClassEmployeeConfigDO> phEmployeeList = dayClassEmployeeConfigDOList.stream().filter(o -> StringUtils.equalsIgnoreCase(o.getDayPunchType(), PunchDayTypeEnum.PH.getCode()) || StringUtils.equalsIgnoreCase(o.getDayPunchType(), PunchDayTypeEnum.OFF.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(phEmployeeList)) {
                    map.put("attendanceStatus", RequestInfoHolder.isChinese() ? "正常" : "normal");
                    continue;
                }
                //请假+外勤
                BigDecimal formMinutes = attendanceMinutes.add(leaveMinutes);

                //打卡的时间
                BigDecimal availableMinutes = BigDecimal.ZERO;
                //接下来需要重新计算当天的考勤，根据打卡时间来获取：【根据打卡时间获取的考勤时长是包括：正常打卡时长+请假时长+外勤时长】
                //仅仅是打卡时间
                if (CollectionUtils.isNotEmpty(dayPunchConfigDOList) && StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())) {
                    availableMinutes = userFreeDayAttendanceHandler(finalTempDayId, dayClassItemConfigDOList, userPunchRecordDTOList);
                    if (availableMinutes.compareTo(defaultLegalWorkingMinutes) > 0) {
                        availableMinutes = defaultLegalWorkingMinutes;
                    }
                    availableMinutes = availableMinutes.subtract(formMinutes);
                }
                //非自由上下班打卡
                if (CollectionUtils.isNotEmpty(dayPunchConfigDOList) && !StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())) {
                    if (actualPunchMinutes.compareTo(BigDecimal.ZERO) > 0 || addDurationMinutes.compareTo(BigDecimal.ZERO) > 0) {
                        //存在补时长
                        availableMinutes = availableMinutes.add(actualPunchMinutes);
                    } else {
                        availableMinutes = userShiftDayAttendanceHandler(finalTempDayId, dayClassItemConfigDOList, userPunchRecordDTOList, userLeavePassFormDOList, userLeavePassFormAttrDOList);
                        availableMinutes = availableMinutes.subtract(formMinutes);
                    }
                }
                // 根据打卡记录计算出的时间判断是否是等于法定工作时长，如果大于等于就是正常考勤，否则是异常考勤
                if (availableMinutes.add(addDurationMinutes).add(formMinutes).compareTo(defaultLegalWorkingMinutes) > -1) {
                    map.put("attendanceStatus", RequestInfoHolder.isChinese() ? "正常" : "normal");
                } else {
                    map.put("attendanceStatus", RequestInfoHolder.isChinese() ? "异常" : "abnormal");
                }
                if (availableMinutes.compareTo(BigDecimal.ZERO) == 0) {
                    map.put("absentCount", BigDecimal.ONE.toString());
                    map.put("absentHours", availableMinutes.divide(defaultLegalWorkingMinutes, 2, RoundingMode.HALF_UP).toString());
                    continue;
                }
                map.put("presentHour", (availableMinutes.add(addDurationMinutes).add(formMinutes)).divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP).toString());
            }
        }
        return getPageResult(resultList, queryDTO, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    @Override
    public PaginationResult<AttendanceDTO> warehouseStaffAttendanceList(WarehouseStaffAttendanceQueryDTO queryDTO) {
        UserAttendanceQuery query = attendanceBeforeHandler(Boolean.TRUE, queryDTO, BusinessConstant.N, BusinessConstant.Y, null);
        PageInfo<AttendanceDTO> pageInfo = queryUserAttendanceList(query);
        return attendanceAfterHandler(pageInfo, queryDTO);
    }


    @Override
    public List<AttendanceDTO> selectUserAttendances(UserAttendanceQuery query) {
        List<UserAttendanceBO> userAttendanceBOList = hrmsAttendanceEmployeeDetailManage.attendanceUserList(query);

        if (CollectionUtils.isEmpty(userAttendanceBOList)) {
            return new ArrayList<>();
        }
        List<Long> userIds = userAttendanceBOList.stream().map(o -> o.getUserId()).distinct().collect(Collectors.toList());
        query.setUserIds(userIds);
        List<HrmsAttendanceEmployeeDetailDO> attendanceList = hrmsAttendanceEmployeeDetailManage.userAttendanceList(query);
        if (CollectionUtils.isEmpty(attendanceList)) {
            return new ArrayList<>();
        }
        List<AttendanceDTO> attendanceDTOList = buildAttendanceDTO(query, userAttendanceBOList, attendanceList, Collections.emptyList());
        return attendanceDTOList;
    }

    @Override
    public List<AttendanceClassDetailDTO> detailUserClassDTO(AttendanceEmployeeDetailClassQuery query) {
        Long userId = Objects.isNull(query.getUserId()) ? RequestInfoHolder.getUserId() : query.getUserId();
        HrmsUserInfoDO userInfo = hrmsUserInfoDao.getById(userId);
        if (Objects.isNull(userInfo)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS);
        }
        //根据日期查询
        Long dayId = query.getDate();
        Long preDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(String.valueOf(dayId), "yyyyMMdd"), -1), "yyyyMMdd"));
        Long afterDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(String.valueOf(dayId), "yyyyMMdd"), 1), "yyyyMMdd"));
        //查询用户两天排班
        List<HrmsAttendanceClassEmployeeConfigDO> employeeConfigDOS = classEmployeeConfigDao.selectRecordByDayList(userId, Arrays.asList(dayId, preDayId, afterDayId));
        if (CollectionUtils.isEmpty(employeeConfigDOS)) {
            return Lists.newArrayList(AttendanceClassDetailDTO.builder().isWork(false).build());
        }
        Map<Long, List<HrmsAttendanceClassEmployeeConfigDO>> dayMap = employeeConfigDOS.stream().collect(Collectors.groupingBy(HrmsAttendanceClassEmployeeConfigDO::getDayId));
        List<HrmsAttendanceClassEmployeeConfigDO> employeeConfig_preDay = dayMap.get(preDayId);
        List<HrmsAttendanceClassEmployeeConfigDO> employeeConfig_day = dayMap.get(dayId);
        if (CollectionUtils.isEmpty(employeeConfig_day)) {
            return Lists.newArrayList(AttendanceClassDetailDTO.builder().isWork(false).build());
        }
        List<AttendanceClassDetailDTO> attendanceClassDetailDTOList = new ArrayList<>();
        //查询当天排班详情
        AttendanceClassDetailDTO attendanceClassDetailDTO = buildEmployeeClassDTO(employeeConfig_day.get(0), dayId, userInfo.getUserCode());
        attendanceClassDetailDTOList.add(attendanceClassDetailDTO);
        //前端传递选择为开始时间
        if (Objects.isNull(query.getIsEndTime()) || !query.getIsEndTime()) {
            if (CollectionUtils.isEmpty(attendanceClassDetailDTOList)) {
                return attendanceClassDetailDTOList;
            }
            //判断当天班次的结束时间是否跨天，如果跨天，则返回前后两天时间让用户选择
            List<HrmsAttendanceClassEmployeeConfigDO> employeeConfig_afterDay = dayMap.get(afterDayId);
            if (CollectionUtils.isEmpty(employeeConfig_afterDay)
                    || Objects.isNull(employeeConfig_afterDay.get(0).getClassId())) {
                return attendanceClassDetailDTOList;
            }
            //当天班次结束时间
            Date endTime = attendanceClassDetailDTOList.get(0).getEndTime();
            if (Objects.nonNull(endTime)
                    && afterDayId.equals(Long.valueOf(DateUtil.format(endTime, "yyyyMMdd")))) {
                //跨天则需要查询后一天的排班看是否和结束时间为同一天
//                List<HrmsAttendancePunchClassItemConfigDO> afterDayItemConfigList = hrmsAttendancePunchClassItemConfigDao.selectItemConfigByClassId(Arrays.asList(employeeConfig_afterDay.get(0).getClassId()));
                List<HrmsAttendancePunchClassItemConfigDO> afterDayItemConfigList = punchConfigDaoFacade.getClassItemConfigAdapter().selectItemConfigByClassId(Arrays.asList(employeeConfig_afterDay.get(0).getClassId()));
                if (CollectionUtils.isNotEmpty(afterDayItemConfigList)) {
                    HrmsAttendancePunchClassItemConfigDO afterDayItemConfig = afterDayItemConfigList.get(afterDayItemConfigList.size() - 1);
                    if (Objects.nonNull(afterDayItemConfig.getIsAcross()) && afterDayItemConfig.getIsAcross() == 0) {
                        AttendanceClassDetailDTO afterDayClassDetailDTO = buildEmployeeClassDTO(employeeConfig_afterDay.get(0), afterDayId, userInfo.getUserCode());
                        attendanceClassDetailDTOList.add(afterDayClassDetailDTO);
                    }
                }
            }
            return attendanceClassDetailDTOList;
        }
        //如果前一天没有排班或者前一天排班班次和当天班次一样，则返回当天排班详情即可
        if (CollectionUtils.isNotEmpty(employeeConfig_preDay)
                && Objects.nonNull(employeeConfig_preDay.get(0).getClassId())) {
            //前后两天班次不一样并且前一天的班次跨天，则返回前后两天时间天让用户选择
//            List<HrmsAttendancePunchClassItemConfigDO> preDayItemConfigList = hrmsAttendancePunchClassItemConfigDao.selectItemConfigByClassId(Arrays.asList(employeeConfig_preDay.get(0).getClassId()));
            List<HrmsAttendancePunchClassItemConfigDO> preDayItemConfigList = punchConfigDaoFacade.getClassItemConfigAdapter().selectItemConfigByClassId(Arrays.asList(employeeConfig_preDay.get(0).getClassId()));
            if (CollectionUtils.isNotEmpty(preDayItemConfigList)) {
                HrmsAttendancePunchClassItemConfigDO preDayItemConfig = preDayItemConfigList.get(preDayItemConfigList.size() - 1);
                if (Objects.nonNull(preDayItemConfig.getIsAcross()) && preDayItemConfig.getIsAcross() == 1) {
                    AttendanceClassDetailDTO preDayClassDetailDTO = buildEmployeeClassDTO(employeeConfig_preDay.get(0), preDayId, userInfo.getUserCode());
                    attendanceClassDetailDTOList.add(preDayClassDetailDTO);
                }
            }
        }
        //过滤结束时间不为当天的排班
        attendanceClassDetailDTOList.removeIf(item -> Objects.nonNull(item.getEndTime())
                && !dayId.equals(Long.valueOf(DateUtil.format(item.getEndTime(), "yyyyMMdd"))));
        return attendanceClassDetailDTOList;
    }

    @Override
    public List<AttendanceClassCalendarDTO> detailUserClassCalendar(Integer year, Long userIdParam) {
        Long userId = Objects.isNull(userIdParam) ? RequestInfoHolder.getUserId() : userIdParam;
        HrmsUserInfoDO userInfo = hrmsUserInfoDao.getById(userId);
        if (Objects.isNull(userInfo)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS);
        }
        if (Objects.isNull(year)) {
            return Lists.newArrayList();
        }
        //按年查找当前用户排班
        Long firstDayId = Long.valueOf(DateUtil.format(DateUtils.yearBegin(year), "yyyyMMdd"));
        Long lastDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtils.yearEnd(year), 1), "yyyyMMdd"));
        List<HrmsAttendanceClassEmployeeConfigDO> employeeConfigDOS = classEmployeeConfigDao.selectRecordByDateRange(userId, firstDayId, lastDayId);
        if (CollectionUtils.isEmpty(employeeConfigDOS)) {
            return Lists.newArrayList();
        }
        Map<Long, List<HrmsAttendanceClassEmployeeConfigDO>> dayMap = employeeConfigDOS.stream().collect(Collectors.groupingBy(HrmsAttendanceClassEmployeeConfigDO::getDayId));
        //过滤未排班数据dayId
        List<Long> NoClassDayId = Optional.ofNullable(employeeConfigDOS.stream()
                .filter(item -> Objects.isNull(item.getClassId()))
                .map(item -> item.getDayId())
                .collect(Collectors.toList())).orElse(Lists.newArrayList());
        //过滤已排班数据dayId
        List<Long> classDayId = Optional.ofNullable(employeeConfigDOS.stream()
                .filter(item -> Objects.nonNull(item.getClassId()))
                .map(item -> item.getDayId())
                .collect(Collectors.toList())).orElse(Lists.newArrayList());
        //遍历日期
        List<AttendanceClassCalendarDTO> dateList = Lists.newArrayList();
        while (firstDayId <= lastDayId) {
            AttendanceClassCalendarDTO dto = AttendanceClassCalendarDTO.builder().dayId(firstDayId).build();
            if (NoClassDayId.contains(firstDayId)) {
                //节假日
                List<HrmsAttendanceClassEmployeeConfigDO> currentDayList = dayMap.get(firstDayId);
                if (CollectionUtils.isNotEmpty(currentDayList)) {
                    dto.setType(currentDayList.get(0).getDayPunchType());
                }
                dateList.add(dto);
                firstDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(firstDayId.toString()), 1), "yyyyMMdd"));
                continue;
            }

            if (classDayId.contains(firstDayId)) {
                firstDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(firstDayId.toString()), 1), "yyyyMMdd"));
                continue;
            }
            //未排班
            dto.setType("NONE");
            dateList.add(dto);
            firstDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(firstDayId.toString()), 1), "yyyyMMdd"));
        }
        return dateList;
    }


    /**
     * 国际化处理
     *
     * @param userInformationDTO
     */
    private void handlerInformation(UserInformationDTO userInformationDTO) {
        userInformationDTO.setPostName(RequestInfoHolder.isChinese() ? userInformationDTO.getPostNameCn() : userInformationDTO.getPostNameEn());
        userInformationDTO.setDeptName(RequestInfoHolder.isChinese() ? userInformationDTO.getDeptNameCn() : userInformationDTO.getDeptNameEn());
    }


    /**
     * 查询出勤列表前置处理方法
     *
     * @param queryDTO
     */
    private UserAttendanceQuery attendanceBeforeHandler(Boolean isCheckDate, BaseAttendanceQueryDTO queryDTO, Integer isDriver, Integer isWarehouseStaff, List<String> employeeTypes) {
        if (isCheckDate) {
            //校验时间跨度
            boolean isOk = ChronoUnit.MONTHS.between(DateConvertUtils.toLocalDateTime(queryDTO.getStartTime()), DateConvertUtils.toLocalDateTime(queryDTO.getEndTime())) > DEFAULT_MOUTH_RANGE;

            if (isOk) {
                throw BusinessException.get(HrmsErrorCodeEnums.MAX_MONTH_RANGE_ERROR.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.MAX_MONTH_RANGE_ERROR.getDesc()));
            }
        }
        handlerQuery(queryDTO);
        //处理数据权限
        //systemResourceManage.setResource(queryDTO, BaseAttendanceQueryDTO::setResourceType, BaseAttendanceQueryDTO::setOrganizationIds);

        UserAttendanceQuery query = BeanUtils.convert(queryDTO, UserAttendanceQuery.class);

        query.setIsDriver(isDriver);
        query.setIsWarehouseStaff(isWarehouseStaff);
        query.setEmployeeTypes(employeeTypes);
        return query;
    }

    /**
     * 查询出勤列表后置处理方法
     *
     * @param pageInfo
     * @param query
     * @return
     */
    private PaginationResult<AttendanceDTO> attendanceAfterHandler(PageInfo<AttendanceDTO> pageInfo, BaseAttendanceQueryDTO query) {
        //处理返回类 中英文
        this.handlerResponse(pageInfo, query);
        return getPageResult(pageInfo, query);
    }


    /**
     * 处理查询类
     *
     * @param query
     */
    private void handlerQuery(BaseAttendanceQueryDTO query) {
        if (!StringUtils.isEmpty(query.getWorkNo())) {
            query.setWorkNo(query.getWorkNo().trim());
        }
        if (!StringUtils.isEmpty(query.getUserNameOrEmail())) {
            query.setUserNameOrEmail(query.getUserNameOrEmail().trim());
        }
    }


    /**
     * 处理返回类
     *
     * @param pageInfo
     */
    private void handlerResponse(PageInfo<AttendanceDTO> pageInfo, BaseAttendanceQueryDTO query) {
        List<AttendanceDTO> list = pageInfo.getList();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        //生成时间周期
        String timeCycle = DateConvertUtils.generateDateCycle(query.getStartTime(), query.getEndTime());
        list.forEach(item -> {
            item.setTimeCycle(timeCycle);
            item.setDeptName(RequestInfoHolder.isChinese() ? item.getDeptNameCn() : item.getDeptNameEn());
            item.setPostName(RequestInfoHolder.isChinese() ? item.getPostNameCn() : item.getPostNameEn());
        });
    }

    /**
     * 查询员工出勤列表信息
     *
     * @param query
     * @return
     */
    private PageInfo<AttendanceDTO> queryUserAttendanceList(UserAttendanceQuery query) {
        log.info("userAttendance param:{}", JSON.toJSONString(query));
        String country = query.getCountry();
        List<String> paramCountryList = new ArrayList<>();
        if (StringUtils.isNotBlank(country)) {
            paramCountryList.add(country);
        }
        Boolean isChooseDept = Boolean.FALSE;
        if (CollUtil.isNotEmpty(query.getDeptIds())) {
            // 设置标志
            isChooseDept = Boolean.TRUE;
        }
        PermissionCountryDeptVO permissionDept = userResourceService.getPermissionCountryDeptVO(query.getDeptIds(), Lists.newArrayList(paramCountryList));
        Boolean hasOrDeptAndCountryPermission = permissionDept.getHasOrDeptAndCountryPermission();
        log.info("userAttendance permissionDept:{}", JSON.toJSONString(permissionDept));
        if (CollectionUtils.isNotEmpty(query.getDeptIds()) && permissionDept.getHasDeptPermission().equals(Boolean.FALSE)) {
            return new PageInfo<>(new ArrayList<>(), 0);
        }

        // 去掉外部传入国家的一票否决权
        //if (StringUtils.isNotEmpty(country) && permissionDept.getHasCountryPermission().equals(Boolean.FALSE)) {
        //    return new PageInfo<>(new ArrayList<>(), 0);
        //}

        if (hasOrDeptAndCountryPermission.equals(Boolean.FALSE)) {
            return new PageInfo<>(new ArrayList<>(), 0);
        }
        query.setDeptIds(permissionDept.getDeptIdList());
        query.setAuthLocationCountryList(permissionDept.getCountryList());

        // 非系统管理员
        query.setHasDeptPermission(permissionDept.getHasDeptPermission());
        query.setHasCountryPermission(permissionDept.getHasCountryPermission());
        query.setHasOrDeptAndCountryPermission(permissionDept.getHasOrDeptAndCountryPermission());
        query.setHasAndDeptAndCountryPermission(permissionDept.getHasAndDeptAndCountryPermission());
        query.setIsChooseDept(isChooseDept);


        //权限控制
        //PermissionDeptVO permissionDept = userResourceService.getPermissionDept(query.getDeptIds());
        //if (!permissionDept.getHasDeptPermission()) {
        //    return new PageInfo<>(new ArrayList<>(), 0);
        //}
        //query.setDeptIds(permissionDept.getDeptIdList());
        //List<String> authorizedBizCountryList = userResourceService.getAuthorizedBizCountryList(StringUtils.isBlank(query.getCountry()) ? new ArrayList<>() : Arrays.asList(query.getCountry()));
        //if (CollectionUtils.isEmpty(authorizedBizCountryList)) {
        //    return new PageInfo<>(new ArrayList<>(), 0);
        //}
        //query.setCountryList(authorizedBizCountryList);
        log.info("userAttendance param2:{}", JSON.toJSONString(query));

        Page<AttendanceDTO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<UserAttendanceBO> userIdPageInfo = page.doSelectPageInfo(() -> hrmsAttendanceEmployeeDetailManage.attendanceUserList(query));

        PageInfo<AttendanceDTO> pageInfo = BeanUtils.convert(userIdPageInfo, PageInfo.class);
        List<UserAttendanceBO> userAttendanceBOS = userIdPageInfo.getList();
        if (CollectionUtils.isEmpty(userAttendanceBOS)) {
            List<AttendanceDTO> attendanceDTOList = new ArrayList<>();
            pageInfo.setList(attendanceDTOList);
            return pageInfo;
        }
        //慢SQL优化
        List<Long> userId = userIdPageInfo.getList().stream().filter(item -> item.getUserId() != null).map(UserAttendanceBO::getUserId).collect(Collectors.toList());
        Long startDayId = Long.valueOf(DateUtil.format(new Date(), "yyyyMMdd"));
        Long endDayId = Long.valueOf(DateUtil.format(new Date(), "yyyyMMdd"));
        if (query.getStartTime() != null) {
            startDayId = Long.valueOf(DateUtil.format(query.getStartTime(), "yyyyMMdd"));
        }
        if (query.getEndTime() != null) {
            endDayId = Long.valueOf(DateUtil.format(query.getEndTime(), "yyyyMMdd"));
        }
        List<HrmsAttendanceEmployeeDetailDO> attendanceList = hrmsAttendanceEmployeeDetailManage.selectAttendanceByCycleDay(userId, startDayId, endDayId);

        List<HrmsEmployeeAbnormalAttendanceDO> abnormalList = hrmsEmployeeAbnormalAttendanceManage.selectAbnormalAttendanceByCycleDay(userId, startDayId, endDayId);

        List<AttendanceDTO> attendanceDTOList = buildAttendanceDTO(query, userAttendanceBOS, attendanceList, abnormalList);
        pageInfo.setList(attendanceDTOList);
        return pageInfo;
    }

    /**
     * 构建员工出勤列表DTO数据
     *
     * @param attendanceList
     * @return
     */
    private List<AttendanceDTO> buildAttendanceDTO(UserAttendanceQuery query, List<UserAttendanceBO> userAttendanceBOList, List<HrmsAttendanceEmployeeDetailDO> attendanceList, List<HrmsEmployeeAbnormalAttendanceDO> abnormalList) {
        //获取用户
        if (CollectionUtils.isEmpty(userAttendanceBOList)) {
            userAttendanceBOList = new ArrayList<>();
        }
        List<Long> userIds = new ArrayList<>();
        for (UserAttendanceBO userAttendanceBO : userAttendanceBOList) {
            if (userAttendanceBO.getUserId() == null) {
                continue;
            }
            userIds.add(userAttendanceBO.getUserId());
        }
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoDao.getByUserIds(userIds);
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return new ArrayList<>();
        }
        List<String> userCodeList = userInfoDOList.stream().map(HrmsUserInfoDO::getUserCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        //获取部门
        List<Long> deptIds = userInfoDOList.stream().map(HrmsUserInfoDO::getDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, HrmsEntDeptDO> deptMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(deptIds)) {
            List<HrmsEntDeptDO> deptDOList = hrmsEntDeptDao.listByDeptIds(deptIds);
            deptMap = deptDOList.stream().collect(Collectors.toMap(o -> o.getId().toString(), o -> o, (v1, v2) -> v1));
        }
        //获取岗位
        List<Long> postIds = userInfoDOList.stream().map(HrmsUserInfoDO::getPostId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, HrmsEntPostDO> postMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(postIds)) {
            List<HrmsEntPostDO> postDOList = hrmsEntPostDao.listByIds(postIds);
            postMap = postDOList.stream().collect(Collectors.toMap(o -> o.getId().toString(), o -> o, (v1, v2) -> v1));
        }

        //员工考勤数据按照userId进行分组
        Map<Long, List<HrmsAttendanceEmployeeDetailDO>> map = attendanceList.stream().collect(Collectors.groupingBy(HrmsAttendanceEmployeeDetailDO::getUserId));

        //员工异常考勤数据按照userId进行分组
        Map<Long, List<HrmsEmployeeAbnormalAttendanceDO>> abnormalMap = abnormalList.stream().collect(Collectors.groupingBy(HrmsEmployeeAbnormalAttendanceDO::getUserId));

        List<AttendanceDTO> attendanceDTOList = new ArrayList<>();

        Map<Long, HrmsUserInfoDO> userMap = userInfoDOList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getId, o -> o, (v1, v2) -> v1));

        Long startDayId = Long.valueOf(DateUtil.format(query.getStartTime(), "yyyyMMdd"));
        Long endDayId = Long.valueOf(DateUtil.format(query.getEndTime(), "yyyyMMdd"));

        //查询员工改年的所有排班，然后查出打卡规则（需要获取当天的法定工作时长）
        List<HrmsAttendanceClassEmployeeConfigDO> classEmployeeConfigDOList = hrmsAttendanceClassEmployeeConfigManage.selectRecordByUserIdList(userIds, startDayId, endDayId);
        List<Long> classIdList = classEmployeeConfigDOList.stream().map(HrmsAttendanceClassEmployeeConfigDO::getClassId).filter(Objects::nonNull).collect(Collectors.toList());
//        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = hrmsAttendancePunchClassConfigManage.selectClassByIdList(classIdList);
        List<HrmsAttendancePunchClassConfigDO> classConfigDOS = punchConfigManageAdapter.selectClassByIdList(classIdList);
//        List<HrmsAttendancePunchClassItemConfigDO> classItemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(classIdList);
        List<HrmsAttendancePunchClassItemConfigDO> classItemConfigDOList = punchConfigManageAdapter.selectItemConfigByClassId(classIdList);
        List<Long> punchConfigIdList = classEmployeeConfigDOList.stream().map(HrmsAttendanceClassEmployeeConfigDO::getPunchConfigId).filter(Objects::nonNull).collect(Collectors.toList());
//        List<HrmsAttendancePunchConfigDO> punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByIdList(punchConfigIdList);
        List<HrmsAttendancePunchConfigDO> punchConfigDOList = punchConfigManageAdapter.selectAttendancePunchByIdList(punchConfigIdList);

        //免打卡需要用到
        AttendancePunchConfigRangeByDateQuery configRangeByDateQuery = new AttendancePunchConfigRangeByDateQuery();
        configRangeByDateQuery.setUserIds(userIds);
//        List<HrmsAttendancePunchConfigRangeDO> allPunchConfigRangeDOS = hrmsAttendancePunchConfigRangeManage.selectPunchConfigRangeByDate(configRangeByDateQuery);
        List<HrmsAttendancePunchConfigRangeDO> allPunchConfigRangeDOS = punchConfigManageAdapter.selectPunchConfigRangeByDate(configRangeByDateQuery);

        //获取用户改年的所有的打卡记录
        EmployeePunchCardRecordQuery employeePunchCardRecordQuery = new EmployeePunchCardRecordQuery();
        employeePunchCardRecordQuery.setStartTime(DateUtil.beginOfDay(DateUtil.offsetDay(query.getStartTime(), -2)));
        employeePunchCardRecordQuery.setEndTime(DateUtil.endOfDay(DateUtil.offsetDay(query.getEndTime(), 2)));
        employeePunchCardRecordQuery.setUserCodes(userCodeList);
        List<EmployeePunchRecordDO> allPunchRecordList = punchRecordDao.listRecords(employeePunchCardRecordQuery);
        List<UserPunchRecordDTO> userPunchRecordDTOList = new ArrayList<>();
        for (EmployeePunchRecordDO employeePunchRecordDO : allPunchRecordList) {
            UserPunchRecordDTO userPunchRecordDTO = new UserPunchRecordDTO();
            userPunchRecordDTO.setId(employeePunchRecordDO.getId());
            userPunchRecordDTO.setUserCode(employeePunchRecordDO.getUserCode());
            userPunchRecordDTO.setFormId(employeePunchRecordDO.getFormId());
            String punchTimeString = DateUtil.format(employeePunchRecordDO.getPunchTime(), "yyyy-MM-dd HH:mm");
            userPunchRecordDTO.setFormatPunchTime(DateUtil.parse(punchTimeString + ":00", "yyyy-MM-dd HH:mm:ss"));
            userPunchRecordDTOList.add(userPunchRecordDTO);
        }
        //获取用户改年的所有审批通过的请假/外勤/补时长单据
        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setUserIdList(userIds);
        applicationFormQuery.setStatusList(Collections.singletonList(HrAttendanceApplicationFormStatusEnum.PASS.getCode()));
        applicationFormQuery.setFromTypeList(Arrays.asList(HrAttendanceApplicationFormTypeEnum.LEAVE.getCode(), HrAttendanceApplicationFormTypeEnum.OUT_OF_OFFICE.getCode(), HrAttendanceApplicationFormTypeEnum.ADD_DURATION.getCode()));
        List<HrmsApplicationFormDO> passFormDOList = hrmsApplicationFormManage.selectForm(applicationFormQuery);
        List<Long> passFormIdList = passFormDOList.stream().map(HrmsApplicationFormDO::getId).collect(Collectors.toList());
        List<HrmsApplicationFormAttrDO> passFormAttrDOList = hrmsApplicationFormAttrManage.selectFormAttrByFormIdLit(passFormIdList);

        for (UserAttendanceBO item : userAttendanceBOList) {
            List<HrmsAttendanceEmployeeDetailDO> detailList = map.get(item.getUserId());
            if (CollectionUtils.isEmpty(detailList)) {
                detailList = new ArrayList<>();
            }
            List<HrmsEmployeeAbnormalAttendanceDO> abnoramlAttendanceList = abnormalMap.get(item.getUserId());
            if (CollectionUtils.isEmpty(abnoramlAttendanceList)) {
                abnoramlAttendanceList = new ArrayList<>();
            }
            detailList = detailList.stream().sorted(Comparator.comparing(HrmsAttendanceEmployeeDetailDO::getLastUpdDate).reversed()).collect(Collectors.toList());
            AttendanceDTO attendanceDTO = new AttendanceDTO();
            attendanceDTOList.add(attendanceDTO);
            //用户基础信息构造
            baseInfoHandler(attendanceDTO, item.getUserId(), userMap, deptMap, postMap);

            List<UserPunchRecordDTO> userPunchRecordList = userPunchRecordDTOList.stream().filter(o -> StringUtils.equalsIgnoreCase(o.getUserCode(), attendanceDTO.getUserCode())).collect(Collectors.toList());
            List<HrmsApplicationFormDO> userPassFormDOList = passFormDOList.stream().filter(o -> o.getUserId().equals(item.getUserId())).collect(Collectors.toList());
            List<Long> userPassFormIdList = userPassFormDOList.stream().map(HrmsApplicationFormDO::getId).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> userPassFormAttrDOList = passFormAttrDOList.stream().filter(o -> userPassFormIdList.contains(o.getFormId())).collect(Collectors.toList());

            List<HrmsAttendancePunchConfigRangeDO> userPunchConfigRangeDOS = allPunchConfigRangeDOS.stream().filter(o -> o.getBizId().equals(item.getUserId())).collect(Collectors.toList());

            BigDecimal attendanceDays = BigDecimal.ZERO;
            //每天都需要遍历
            Long tempDayId = startDayId;
            //获取每天的出勤情况
            while (tempDayId <= endDayId) {
                Long finalTempDayId = tempDayId;
                //后移一天
                tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));
                //获取用户当天的出勤/异常情况
                List<HrmsAttendanceEmployeeDetailDO> dayAttendanceEmployeeDetailDOList = detailList.stream().filter(o -> o.getUserId().equals(item.getUserId()) && o.getDayId().equals(finalTempDayId)).collect(Collectors.toList());
                List<HrmsEmployeeAbnormalAttendanceDO> dayAbnormalAttendanceDOList = abnoramlAttendanceList.stream().filter(o -> o.getUserId().equals(item.getUserId()) && o.getDayId().equals(finalTempDayId)).collect(Collectors.toList());
                //获取当天的排班和打卡班次信息
                List<HrmsAttendanceClassEmployeeConfigDO> dayClassEmployeeConfigDOList = classEmployeeConfigDOList.stream().filter(o -> o.getUserId().equals(item.getUserId()) && o.getDayId().compareTo(finalTempDayId) == 0).collect(Collectors.toList());
                List<HrmsAttendancePunchClassConfigDO> dayClassConfigDOS = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dayClassEmployeeConfigDOList) && dayClassEmployeeConfigDOList.get(0).getClassId() != null) {
                    dayClassConfigDOS = classConfigDOS.stream().filter(o -> o.getId().equals(dayClassEmployeeConfigDOList.get(0).getClassId())).collect(Collectors.toList());
                }
                List<HrmsAttendancePunchClassItemConfigDO> dayClassItemConfigDOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                    HrmsAttendancePunchClassConfigDO classConfigDO = dayClassConfigDOS.get(0);
                    dayClassItemConfigDOList = classItemConfigDOList.stream().filter(o -> o.getPunchClassId().equals(classConfigDO.getId())).collect(Collectors.toList());
                }
                List<HrmsAttendancePunchConfigDO> dayPunchConfigDOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                    HrmsAttendancePunchClassConfigDO classConfigDO = dayClassConfigDOS.get(0);
                    dayPunchConfigDOList = punchConfigDOList.stream().filter(o -> o.getId().equals(classConfigDO.getPunchConfigId())).collect(Collectors.toList());
                }

                //外勤也算出勤，不算请假
                BigDecimal attendanceMinutes = BigDecimal.ZERO;
                BigDecimal leaveMinutes = BigDecimal.ZERO;
                //先看当天正常考勤表的数据是不是正常
                for (HrmsAttendanceEmployeeDetailDO detailDO : dayAttendanceEmployeeDetailDOList) {
                    if (detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        attendanceMinutes = attendanceMinutes.add(detailDO.getAttendanceMinutes());
                    }
                    if (detailDO.getLeaveMinutes() != null && detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        leaveMinutes = leaveMinutes.add(detailDO.getLeaveMinutes());
                    }
                }

                BigDecimal defaultLegalWorkingHours = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS;
                List<BigDecimal> defaultAbnormalHours = dayAttendanceEmployeeDetailDOList.stream()
                        .filter(o -> o.getLegalWorkingHours() != null && o.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > 0 && o.getAttendanceMinutes() != null && o.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0).map(HrmsAttendanceEmployeeDetailDO::getLegalWorkingHours).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(defaultAbnormalHours)) {
                    defaultLegalWorkingHours = defaultAbnormalHours.get(0);
                }

                if (CollectionUtils.isNotEmpty(dayClassConfigDOS)) {
                    if (dayClassConfigDOS.get(0).getLegalWorkingHours() != null && dayClassConfigDOS.get(0).getLegalWorkingHours().compareTo(BigDecimal.ZERO) > -1) {
                        defaultLegalWorkingHours = dayClassConfigDOS.get(0).getLegalWorkingHours();
                    }
                }

                //先看用户当天是不是免打卡
                List<HrmsAttendancePunchConfigRangeDO> userDayNoPunchConfigRangeDOS = userPunchConfigRangeDOS.stream().filter(o -> o.getEffectTime().compareTo(DateUtil.endOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"))) < 1 && o.getExpireTime().compareTo(DateUtil.endOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"))) > -1).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userDayNoPunchConfigRangeDOS) && userDayNoPunchConfigRangeDOS.get(0).getIsNeedPunch().equals(0)) {
                    //当天没有请假
                    if (leaveMinutes.compareTo(BigDecimal.ZERO) == 0) {
                        attendanceDays = attendanceDays.add(BigDecimal.ONE);
                        continue;
                    }
                    //当天完全请假
                    if (leaveMinutes.compareTo(defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES)) == 0) {
                        continue;
                    }
                    //部分请假
                    BigDecimal noPunchAttendanceMinutes = defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES).subtract(leaveMinutes);
                    attendanceDays = attendanceDays.add(noPunchAttendanceMinutes.divide(defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES), 4, RoundingMode.HALF_UP));
                    continue;
                }

                //当天未排班
                if (CollectionUtils.isEmpty(dayClassEmployeeConfigDOList)) {
                    List<HrmsAttendanceEmployeeDetailDO> dayNoScheduleList = dayAttendanceEmployeeDetailDOList.stream()
                            .filter(o -> StringUtils.equalsIgnoreCase(o.getConcreteType(), PunchDayTypeEnum.PH.getCode())
                                    || StringUtils.equalsIgnoreCase(o.getConcreteType(), PunchDayTypeEnum.OFF.getCode())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(dayNoScheduleList)) {
                        //当天未排班异常处理为节假日/周末
                        continue;
                    }
                    //当天未排班异常处理为工作日或者异常还未处理，那么也是按照工作日计算
                    if (attendanceMinutes.compareTo(BigDecimal.ZERO) > 0) {
                        attendanceDays = attendanceDays.add(attendanceMinutes.divide(defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES), 4, RoundingMode.HALF_UP));
                    }
                    continue;
                }

                //当天排班如果是PH/OFF就无需调用
                List<HrmsAttendanceClassEmployeeConfigDO> phEmployeeList = dayClassEmployeeConfigDOList.stream().filter(o -> StringUtils.equalsIgnoreCase(o.getDayPunchType(), PunchDayTypeEnum.PH.getCode()) || StringUtils.equalsIgnoreCase(o.getDayPunchType(), PunchDayTypeEnum.OFF.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(phEmployeeList)) {
                    continue;
                }
                BigDecimal formMinutes = BigDecimal.ZERO;
                BigDecimal addDurationMinutes = BigDecimal.ZERO;
                BigDecimal actualPunchMinutes = BigDecimal.ZERO;
                for (HrmsAttendanceEmployeeDetailDO detailDO : dayAttendanceEmployeeDetailDOList) {
                    if (detailDO.getFormId() != null && detailDO.getLeaveMinutes() != null && detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        formMinutes = formMinutes.add(detailDO.getLeaveMinutes());
                    }
                    if (detailDO.getFormId() != null && detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        formMinutes = formMinutes.add(detailDO.getAttendanceMinutes());
                    }
                    if (detailDO.getFormId() != null && detailDO.getAddDurationMinutes() != null) {
                        addDurationMinutes = addDurationMinutes.add(detailDO.getAddDurationMinutes());
                        actualPunchMinutes = actualPunchMinutes.add(detailDO.getActualPunchMinutes());
                    }
                }
                for (HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO : dayAbnormalAttendanceDOList) {
                    if (Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormalAttendanceDO.getAbnormalType()) && !AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(abnormalAttendanceDO.getStatus())) {
                        AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
                        actualPunchMinutes = actualPunchMinutes.add(abnormalExtendDTO.getActualWorkingHours().multiply(BusinessConstant.MINUTES));
                    }
                }
                // 外勤时间
                BigDecimal outOffOfficeMinutes = BigDecimal.ZERO;
                for (HrmsAttendanceEmployeeDetailDO detailDO : dayAttendanceEmployeeDetailDOList) {
                    if (detailDO.getFormId() != null && detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        outOffOfficeMinutes = outOffOfficeMinutes.add(detailDO.getAttendanceMinutes());
                    }
                }
                BigDecimal availableMinutes = BigDecimal.ZERO;
                //接下来需要重新计算当天的考勤，根据打卡时间来获取
                //仅仅是打卡时间
                if (CollectionUtils.isNotEmpty(dayPunchConfigDOList) && StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())) {
                    availableMinutes = userFreeDayAttendanceHandler(finalTempDayId, dayClassItemConfigDOList, userPunchRecordList);
                    if (availableMinutes.compareTo(defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES)) > 0) {
                        availableMinutes = defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES);
                    }
                    availableMinutes = availableMinutes.subtract(formMinutes);
                }
                //看当天是否是一次打卡规则
                if (CollectionUtils.isNotEmpty(dayPunchConfigDOList) && StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FIXED_WORK_ONCE.name())) {
                    BigDecimal defaultLegalWorkingMinutes = defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES);
                    availableMinutes = userOnceDayAttendanceHandler(finalTempDayId, dayClassItemConfigDOList
                            , userPunchRecordList, leaveMinutes, defaultLegalWorkingMinutes, outOffOfficeMinutes);
                    availableMinutes = availableMinutes.subtract(formMinutes);
                }
                //固定上下班/班次上下班
                if (CollectionUtils.isNotEmpty(dayPunchConfigDOList)
                        && !StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())
                        && !StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FIXED_WORK_ONCE.name())) {
                    //存在补时长
                    if (addDurationMinutes.compareTo(BigDecimal.ZERO) > 0 || actualPunchMinutes.compareTo(BigDecimal.ZERO) > 0) {
                        availableMinutes = availableMinutes.add(actualPunchMinutes);
                    } else {
                        availableMinutes = userShiftDayAttendanceHandler(finalTempDayId, dayClassItemConfigDOList, userPunchRecordList, userPassFormDOList, userPassFormAttrDOList);
                        availableMinutes = availableMinutes.subtract(formMinutes);
                    }
                }
                //打卡时间+请假+外勤+补工时
                BigDecimal dayAttendanceMinutes = availableMinutes.add(outOffOfficeMinutes);
                //正常表的出勤天数
                /*
                 * 1. 前置拦截：在根据打卡记录判断考勤时长之前，先判断该用户的出勤时长是否是满勤，也就是法定时长
                 * 2. 兼容批量异常处理的情况。如果是批量异常处理：考勤表直接会生成p的记录，异常表的异常状态会是PASS
                 */
                BigDecimal attendanceDay = dayAttendanceMinutes.divide(defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES), 4, RoundingMode.HALF_UP);
                if (attendanceMinutes.compareTo(defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES)) == 0) {
                    attendanceDay = BigDecimal.ONE;
                }
                attendanceDays = attendanceDays.add(attendanceDay.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : attendanceDay);
            }

            attendanceDTO.setAttendanceDays(attendanceDays);

            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            //工作日出勤列表
            List<HrmsAttendanceEmployeeDetailDO> presentList = detailList.stream().filter(o -> o.getAttendanceType().equals(AttendanceDayTypeEnum.PRESENT.name())).filter(o -> o.getIsAttendance().equals(BusinessConstant.Y)).filter(o -> o.getAttendanceHours() != null).collect(Collectors.toList());
            //休息日出勤列表
            List<HrmsAttendanceEmployeeDetailDO> weekendList = detailList.stream().filter(o -> o.getAttendanceType().equals(AttendanceDayTypeEnum.WEEKEND.name())).collect(Collectors.toList());
            //休息日天数
            //attendanceDTO.setWeekendCnt(weekendList == null ? 0 : weekendList.size());
            //休息日加班小时数
            BigDecimal overtimeHoursOnWeekend = weekendList.stream().map(HrmsAttendanceEmployeeDetailDO::getOvertimeHours).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            attendanceDTO.setOvertimeHoursOnWeekend(overtimeHoursOnWeekend);
            BigDecimal overtimeDaysOnWeekend = overtimeHoursOnWeekend.divide(BigDecimal.valueOf(8), 4);
            attendanceDTO.setOvertimeDaysOnWeekend(overtimeDaysOnWeekend);

            //节假日出勤列表
            List<HrmsAttendanceEmployeeDetailDO> holidayList = detailList.stream().filter(o -> o.getAttendanceType().equals(AttendanceDayTypeEnum.HOLIDAY.name())).collect(Collectors.toList());
            //节假日加班小时数
            BigDecimal overtimeHoursOnHoliday = holidayList.stream().map(HrmsAttendanceEmployeeDetailDO::getOvertimeHours).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            attendanceDTO.setOvertimeHoursOnHoliday(overtimeHoursOnHoliday);
            //节假日加班天数
            //attendanceDTO.setHolidayCnt(holidayList == null ? 0 : holidayList.size());
            BigDecimal overtimeDaysOnHoliday = overtimeHoursOnHoliday.divide(BigDecimal.valueOf(8), 4);
            attendanceDTO.setOvertimeDaysOnHoliday(overtimeDaysOnHoliday);

            //工作日加班数据
            List<HrmsAttendanceEmployeeDetailDO> presentOverTimeList = presentList.stream().filter(o -> (o.getOvertimeHours() != null && o.getOvertimeHours().compareTo(BigDecimal.ZERO) > 0)).collect(Collectors.toList());
            //工作日加班小时数
            BigDecimal overtimeHoursOnPresentDay = presentOverTimeList.stream().map(HrmsAttendanceEmployeeDetailDO::getOvertimeHours).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            attendanceDTO.setOvertimeHoursOnPresentDay(overtimeHoursOnPresentDay);
            BigDecimal overtimeDaysOnPresentDay = overtimeHoursOnPresentDay.divide(BigDecimal.valueOf(8), 4);
            attendanceDTO.setOvertimeDaysOnPresentDay(overtimeDaysOnPresentDay);

            Integer deliveredCount = detailList.stream().filter(o -> o.getDeliveredCount() != null).mapToInt(HrmsAttendanceEmployeeDetailDO::getDeliveredCount).sum();
            Integer deliveryCount = detailList.stream().filter(o -> o.getDeliveryCount() != null).mapToInt(HrmsAttendanceEmployeeDetailDO::getDeliveryCount).sum();
            Integer pickUpCount = detailList.stream().filter(o -> o.getPickUpCount() != null).mapToInt(HrmsAttendanceEmployeeDetailDO::getPickUpCount).sum();
            //派件数
            if (StringUtils.equalsIgnoreCase(detailList.get(0).getScanType(), AttendanceScanTypeEnum.SCAN_DELIVERY.getCode())) {
                attendanceDTO.setScanNumber(deliveryCount);
            } else {
                attendanceDTO.setDeliveryCount(deliveryCount);
                attendanceDTO.setDeliveredCount(deliveredCount);
                attendanceDTO.setPickUpCount(pickUpCount);
            }
            attendanceDTO.setLastUpdDate(detailList.get(0).getLastUpdDate());
            attendanceDTO.setLastUpdUserName(detailList.get(0).getLastUpdUserName());
        }
        return attendanceDTOList;
    }

    /**
     * 处理基础信息
     */
    private void baseInfoHandler(AttendanceDTO attendanceDTO, Long userId, Map<Long, HrmsUserInfoDO> userMap, Map<String, HrmsEntDeptDO> deptMap, Map<String, HrmsEntPostDO> postMap) {

        HrmsUserInfoDO userInfoDO = userMap.get(userId);
        attendanceDTO.setId(userInfoDO.getId());
        attendanceDTO.setUserId(userInfoDO.getId());
        attendanceDTO.setWorkNo(userInfoDO.getWorkNo());
        attendanceDTO.setUserName(userInfoDO.getUserName());
        attendanceDTO.setUserCode(userInfoDO.getUserCode());
        //设置公司信息
       /* attendanceDTO.setCompanyId(companyDO.getId());
        attendanceDTO.setCompanyNameCn(companyDO.getCompanyNameCn());
        attendanceDTO.setCompanyNameEn(companyDO.getCompanyNameEn());*/
        //设置部门信息
        if (userInfoDO.getDeptId() != null) {
            HrmsEntDeptDO deptDO = deptMap.get(userInfoDO.getDeptId().toString());
            if (deptDO != null) {
                attendanceDTO.setDeptId(deptDO.getId());
                attendanceDTO.setDeptName(RequestInfoHolder.isChinese() ? deptDO.getDeptNameCn() : deptDO.getDeptNameEn());
                attendanceDTO.setDeptNameCn(deptDO.getDeptNameCn());
                attendanceDTO.setDeptNameEn(deptDO.getDeptNameEn());
                attendanceDTO.setOrganizationCode("");
            }
        }

        //设置岗位信息
        if (userInfoDO.getPostId() != null) {
            HrmsEntPostDO postDO = postMap.get(userInfoDO.getPostId().toString());
            if (postDO != null) {
                attendanceDTO.setPostId(postDO.getId());
                attendanceDTO.setPostName(RequestInfoHolder.isChinese() ? postDO.getPostNameCn() : postDO.getPostNameEn());
                attendanceDTO.setPostNameCn(postDO.getPostNameCn());
                attendanceDTO.setPostNameEn(postDO.getPostNameEn());
            }
        }
        attendanceDTO.setWorkStatus(userInfoDO.getWorkStatus());
        attendanceDTO.setVendorId(userInfoDO.getVendorId());
        attendanceDTO.setVendorOrgId(userInfoDO.getVendorOrgId());
        attendanceDTO.setVendorCode(userInfoDO.getVendorCode());
        attendanceDTO.setVendorName(userInfoDO.getVendorName());
        attendanceDTO.setEmployeeType(userInfoDO.getEmployeeType());
        attendanceDTO.setCountry(userInfoDO.getLocationCountry());
        attendanceDTO.setStatus(userInfoDO.getStatus());
        attendanceDTO.setDisabledDate(userInfoDO.getDisabledDate());
    }

    private HrmsUserCertificateDO getCertificateByType(List<HrmsUserCertificateDO> existCertificateList) {
        Map<String, HrmsUserCertificateDO> certificateMap = existCertificateList.stream().collect(Collectors.toMap(o -> o.getCertificateTypeCode(), o -> o, (v1, v2) -> v1));

        HrmsUserCertificateDO certificateDO = certificateMap.get(CertificateTypeEnum.ID_CARD.getCode());
        if (certificateDO != null) {
            return certificateDO;
        }
        certificateDO = certificateMap.get(CertificateTypeEnum.RESIDENCY_PERMIT.getCode());
        if (certificateDO != null) {
            return certificateDO;
        }
        certificateDO = certificateMap.get(CertificateTypeEnum.PASS_PORT.getCode());
        if (certificateDO != null) {
            return certificateDO;
        }
        return existCertificateList.get(0);
    }

    private Boolean hasDayForm(Long dayId, List<HrmsApplicationFormDO> formDOList, List<HrmsApplicationFormAttrDO> formAttrList) {
        //查询请假周期包含当天的审批单据，并且没有被销假
        if (CollectionUtils.isEmpty(formDOList) || CollectionUtils.isEmpty(formAttrList)) {
            return false;
        }
        Map<Long, List<HrmsApplicationFormAttrDO>> passFormAttrMap = formAttrList.stream().collect(Collectors.groupingBy(o -> o.getFormId()));
        for (HrmsApplicationFormDO formDO : formDOList) {
            List<HrmsApplicationFormAttrDO> formAttrDOList = passFormAttrMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(formAttrDOList)) {
                continue;
            }
            List<HrmsApplicationFormAttrDO> isRevokeDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.isRevoke.getLowerCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(isRevokeDO) && StringUtils.isNotBlank(isRevokeDO.get(0).getAttrValue()) && isRevokeDO.get(0).getAttrValue().equals(BusinessConstant.Y.toString())) {
                continue;
            }
            List<HrmsApplicationFormAttrDO> leaveStartDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> leaveEndDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> reissueCardDayIdDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode())).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> outOfOfficeStartDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> outOfOfficeEndDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
            //查询请假时间是不是包含本次周期
            if (CollectionUtils.isNotEmpty(leaveStartDateDO) && CollectionUtils.isNotEmpty(leaveEndDateDO)) {
                Long leaveStartDayId = Long.valueOf(DateUtil.format(DateUtil.parse(leaveStartDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"), "yyyyMMdd"));
                DateTime leaveEndDate = DateUtil.parse(leaveEndDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Long leaveEndDayId = Long.valueOf(DateUtil.format(leaveEndDate, "yyyyMMdd"));
                if (leaveEndDayId.compareTo(dayId) < 0 || leaveStartDayId.compareTo(dayId) > 0) {
                    continue;
                }
                //结束时间不能为当天的最早时间
                DateTime attendanceDate = DateUtil.parse(String.valueOf(dayId), "yyyyMMdd");
                DateTime beginOfDay = DateUtil.beginOfDay(attendanceDate);
                if (leaveEndDate.compareTo(beginOfDay) == 0) {
                    continue;
                }

                return true;
            }
            if (CollectionUtils.isNotEmpty(outOfOfficeStartDateDO) && CollectionUtils.isNotEmpty(outOfOfficeEndDateDO)) {
                Long outOfOfficeStartDate = Long.valueOf(DateUtil.format(DateUtil.parse(outOfOfficeStartDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"), "yyyyMMdd"));
                Long outOfOfficeEndDate = Long.valueOf(DateUtil.format(DateUtil.parse(outOfOfficeEndDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss"), "yyyyMMdd"));
                if (outOfOfficeEndDate.compareTo(dayId) < 0 || outOfOfficeStartDate.compareTo(dayId) > 0) {
                    continue;
                }

                return true;
            }
            if (CollectionUtils.isNotEmpty(reissueCardDayIdDO)) {
                Long reissueCardDayId = Long.valueOf(reissueCardDayIdDO.get(0).getAttrValue());
                if (dayId.equals(reissueCardDayId)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 导出用户最早最晚打卡记录、打卡区域及请假/外勤/补卡/出差单据信息
     */
    private void handleUserPunchRecord(Map<String, String> map, Long finalTempDayId
            , List<HrmsAttendancePunchConfigDO> dayPunchConfigDOList
            , List<HrmsAttendancePunchClassItemConfigDO> dayClassItemConfigDOList
            , List<UserPunchRecordDTO> userPunchRecordDTOList
            , List<HrmsEmployeeAbnormalAttendanceDO> dayAbnormalAttendanceDOList
            , List<HrmsApplicationFormDO> userFormDOList, List<HrmsApplicationFormAttrDO> userFormAttrDOList
            , List<BusinessTravelFormApiDTO> userTravelFormList
            , BigDecimal defaultLegalWorkingMinutes, BigDecimal leaveMinutes) {

        //异常记录通过时段id分组
        Map<Long, List<HrmsEmployeeAbnormalAttendanceDO>> dayAbnormalMap = dayAbnormalAttendanceDOList.stream()
                .filter(item -> Objects.nonNull(item.getPunchClassItemConfigId()) && !AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(item.getStatus()))
                .collect(Collectors.groupingBy(HrmsEmployeeAbnormalAttendanceDO::getPunchClassItemConfigId));
        //关联请假和补卡单据
        List<UserDayAttendanceFormDTO> userDayFormList = new ArrayList<>();
        List<UserDayAttendanceFormDTO> userDayAttendanceFormList = getUserDayForm(finalTempDayId, userFormDOList, userFormAttrDOList);
        if (CollectionUtils.isNotEmpty(userDayAttendanceFormList)) {
            userDayFormList.addAll(userDayAttendanceFormList);
        }
        //关联打卡记录
        if (CollectionUtils.isNotEmpty(dayPunchConfigDOList)
                && StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())) {
            //自由打卡
            setPunchTimeAndArea(map, dayClassItemConfigDOList, dayAbnormalMap
                    , userPunchRecordDTOList, finalTempDayId
                    , AttendancePunchTypeEnum.FREE_WORK.name()
                    , defaultLegalWorkingMinutes, leaveMinutes
                    , userDayFormList);
        }
        if (CollectionUtils.isNotEmpty(dayPunchConfigDOList)
                && StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FIXED_WORK_ONCE.name())) {
            //一次规则打卡
            setPunchTimeAndArea(map, dayClassItemConfigDOList, dayAbnormalMap
                    , userPunchRecordDTOList, finalTempDayId
                    , AttendancePunchTypeEnum.FIXED_WORK_ONCE.name()
                    , defaultLegalWorkingMinutes, leaveMinutes
                    , userDayFormList);
        }
        if (CollectionUtils.isNotEmpty(dayPunchConfigDOList)
                && !StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FREE_WORK.name())
                && !StringUtils.equalsIgnoreCase(dayPunchConfigDOList.get(0).getPunchConfigType(), AttendancePunchTypeEnum.FIXED_WORK_ONCE.name())) {
            //固班和班次打卡
            setPunchTimeAndArea(map, dayClassItemConfigDOList, dayAbnormalMap
                    , userPunchRecordDTOList, finalTempDayId
                    , null, defaultLegalWorkingMinutes, leaveMinutes
                    , userDayFormList);
        }
        //关联出差单据
        List<UserDayAttendanceFormDTO> userDayTravelList = getUserDayTravel(finalTempDayId, userTravelFormList);
        if (CollectionUtils.isNotEmpty(userDayTravelList)) {
            userDayFormList.addAll(userDayTravelList);
        }
        setUserApprovalForm(map, userDayFormList);
    }

    /**
     * 设置用户打卡区域和最晚打卡时间
     */
    private void setPunchTimeAndArea(Map<String, String> map, List<HrmsAttendancePunchClassItemConfigDO> dayClassItemConfigDOList
            , Map<Long, List<HrmsEmployeeAbnormalAttendanceDO>> dayAbnormalMap, List<UserPunchRecordDTO> userPunchRecordDTOList
            , Long finalTempDayId, String punchClassType, BigDecimal defaultLegalWorkingMinutes, BigDecimal leaveMinutes
            , List<UserDayAttendanceFormDTO> userDayFormList) {
        //计算实际出勤时间(不含休息)
        BigDecimal actualHours = BigDecimal.ZERO;
        //计算实际出勤时间(含休息)
        BigDecimal actualHours_rest = BigDecimal.ZERO;
        //设置打卡时间和区域
        List<String> punchAreaDayList = new ArrayList<>();
        for (HrmsAttendancePunchClassItemConfigDO itemConfigDO : dayClassItemConfigDOList) {
            //设置上下班异常显示
            List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceList = dayAbnormalMap.get(itemConfigDO.getId());
            String punchNormalResult = RequestInfoHolder.isChinese() ? "正常" : "Normal";
            map.put("earliestPunchInResult" + itemConfigDO.getSortNo(), punchNormalResult);
            if (!AttendancePunchTypeEnum.FIXED_WORK_ONCE.name().equals(punchClassType)) {
                map.put("LatestPunchOutResult" + itemConfigDO.getSortNo(), punchNormalResult);
            }
            if (CollectionUtils.isNotEmpty(abnormalAttendanceList)) {
                for (HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO : abnormalAttendanceList) {
                    if (AttendanceAbnormalTypeEnum.getInLackCodeList().contains(abnormalAttendanceDO.getAbnormalType())
                            || AttendanceAbnormalTypeEnum.NO_PUNCH.getCode().equals(abnormalAttendanceDO.getAbnormalType())) {
                        AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(abnormalAttendanceDO.getAbnormalType());
                        String punchAbnormalResult = RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDesc() : abnormalTypeEnum.getDescEn();
                        map.put("earliestPunchInResult" + itemConfigDO.getSortNo(), punchAbnormalResult);
                        continue;
                    }
                    if (AttendanceAbnormalTypeEnum.getOutLackCodeList().contains(abnormalAttendanceDO.getAbnormalType())) {
                        AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(abnormalAttendanceDO.getAbnormalType());
                        String punchAbnormalResult = RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDesc() : abnormalTypeEnum.getDescEn();
                        map.put("LatestPunchOutResult" + itemConfigDO.getSortNo(), punchAbnormalResult);
                    }
                }
            }
            //获取当日最早最晚打卡时间
            DayPunchTimeDTO dayPunchTimeDTO;
            if (AttendancePunchTypeEnum.FREE_WORK.name().equals(punchClassType)) {
                dayPunchTimeDTO = hrmsAttendanceBaseService.getUserFreeWorkPunchClassItemDayTime(String.valueOf(finalTempDayId), itemConfigDO);
            } else {
                dayPunchTimeDTO = hrmsAttendanceBaseService.getUserPunchClassItemDayTime(finalTempDayId, itemConfigDO.getId(), dayClassItemConfigDOList);
            }
            //找出对应时段的打卡记录并排序
            List<UserPunchRecordDTO> userPunchRecordList = userPunchRecordDTOList.stream()
                    .filter(item -> item.getFormatPunchTime().compareTo(dayPunchTimeDTO.getDayPunchStartTime()) >= 0
                            && item.getFormatPunchTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) <= 0)
                    .sorted(Comparator.comparing(e -> e.getFormatPunchTime())).collect(Collectors.toList());
            //一次打卡规则计算出勤时间及打卡时间
            if (AttendancePunchTypeEnum.FIXED_WORK_ONCE.name().equals(punchClassType)) {
                setUserOncePunchTime(dayPunchTimeDTO, userPunchRecordList, itemConfigDO
                        , actualHours, actualHours_rest, map, defaultLegalWorkingMinutes
                        , leaveMinutes, userDayFormList);
            }
            if (CollectionUtils.isEmpty(userPunchRecordList)) continue;
            //判断自由打卡和班次打卡计算打卡时间
            if (AttendancePunchTypeEnum.FREE_WORK.name().equals(punchClassType)) {
                setUserFreeWorkPunchTime(dayPunchTimeDTO, userPunchRecordList, itemConfigDO
                        , actualHours, actualHours_rest, map);
            }
            //固班/班次
            if (StringUtils.isEmpty(punchClassType)) {
                setUserClassPunchTime(dayPunchTimeDTO, userPunchRecordList, itemConfigDO
                        , actualHours, actualHours_rest, map);
            }
            //设置打卡区域
            List<String> punchArea = userPunchRecordList.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getPunchArea()))
                    .map(item -> item.getPunchArea())
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(punchArea)) {
                punchAreaDayList.addAll(punchArea);
            }
        }
        map.put("punchArea", CollectionUtils.isEmpty(punchAreaDayList)
                ? "" : String.join(",", punchAreaDayList.stream().distinct().collect(Collectors.toList())));
    }

    /**
     * 设置用户关键审批单信息
     */
    private void setUserApprovalForm(Map<String, String> map, List<UserDayAttendanceFormDTO> userDayAttendanceFormDTOList) {
        if (CollectionUtils.isEmpty(userDayAttendanceFormDTOList)) {
            return;
        }
        //拼接关键审批单
        StringBuilder approvalFormStr = new StringBuilder();
        for (int i = 0; i < userDayAttendanceFormDTOList.size(); i++) {
            UserDayAttendanceFormDTO userDayAttendanceFormDTO = userDayAttendanceFormDTOList.get(i);
            approvalFormStr.append(userDayAttendanceFormDTO.getFormDesc());
            approvalFormStr.append("-");
            if (StringUtils.isNotBlank(userDayAttendanceFormDTO.getReissueTime())) {
                approvalFormStr.append(userDayAttendanceFormDTO.getReissueTime());
                approvalFormStr.append("-");
                approvalFormStr.append(userDayAttendanceFormDTO.getAbnormalType());
                approvalFormStr.append("-");
            }
            if (StringUtils.isNotBlank(userDayAttendanceFormDTO.getStartTime())) {
                approvalFormStr.append(userDayAttendanceFormDTO.getStartTime());
                approvalFormStr.append("~");
                approvalFormStr.append(userDayAttendanceFormDTO.getEndTime());
                approvalFormStr.append("-");
                approvalFormStr.append(userDayAttendanceFormDTO.getDurationTime());
                approvalFormStr.append("-");
            }
            approvalFormStr.append(userDayAttendanceFormDTO.getFormStatus());
            approvalFormStr.append("(");
            approvalFormStr.append(RequestInfoHolder.isChinese() ? "单号" : "Application Code");
            approvalFormStr.append(":");
            approvalFormStr.append(userDayAttendanceFormDTO.getFormCode());
            approvalFormStr.append(")");
            if (i != userDayAttendanceFormDTOList.size() - 1) {
                approvalFormStr.append(",");
            }
        }
        map.put("approvalForm", approvalFormStr.toString());
    }

    /**
     * 获取用户当日请假/外勤/补卡单据信息
     *
     * @return
     */
    private List<UserDayAttendanceFormDTO> getUserDayForm(Long dayId, List<HrmsApplicationFormDO> formDOList, List<HrmsApplicationFormAttrDO> formAttrList) {
        //查询请假周期包含当天的审批单据，并且没有被销假
        if (CollectionUtils.isEmpty(formDOList) || CollectionUtils.isEmpty(formAttrList)) {
            return Collections.emptyList();
        }
        List<UserDayAttendanceFormDTO> userDayFormList = new ArrayList<>();
        Map<Long, List<HrmsApplicationFormAttrDO>> passFormAttrMap = formAttrList.stream().collect(Collectors.groupingBy(o -> o.getFormId()));
        for (HrmsApplicationFormDO formDO : formDOList) {
            List<HrmsApplicationFormAttrDO> formAttrDOList = passFormAttrMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(formAttrDOList)) {
                continue;
            }
            List<HrmsApplicationFormAttrDO> isRevokeDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.isRevoke.getLowerCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(isRevokeDO) && StringUtils.isNotBlank(isRevokeDO.get(0).getAttrValue()) && isRevokeDO.get(0).getAttrValue().equals(BusinessConstant.Y.toString())) {
                continue;
            }
            List<HrmsApplicationFormAttrDO> leaveStartDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> leaveEndDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> leaveTypeDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> dayDurationAttr = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> reissueCardDayIdDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode())).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> reissueCardTypeDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.reissueCardType.getLowerCode())).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> outOfOfficeStartDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
            List<HrmsApplicationFormAttrDO> outOfOfficeEndDateDO = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
            //查询请假时间是不是包含本次周期
            if (CollectionUtils.isNotEmpty(leaveStartDateDO) && CollectionUtils.isNotEmpty(leaveEndDateDO)) {
                DateTime leaveStartDate = DateUtil.parse(leaveStartDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Long leaveStartDayId = Long.valueOf(DateUtil.format(leaveStartDate, "yyyyMMdd"));
                DateTime leaveEndDate = DateUtil.parse(leaveEndDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Long leaveEndDayId = Long.valueOf(DateUtil.format(leaveEndDate, "yyyyMMdd"));
                if (leaveEndDayId.compareTo(dayId) < 0 || leaveStartDayId.compareTo(dayId) > 0) {
                    continue;
                }
                //结束时间不能为当天的最早时间
                DateTime attendanceDate = DateUtil.parse(String.valueOf(dayId), "yyyyMMdd");
                DateTime beginOfDay = DateUtil.beginOfDay(attendanceDate);
                if (leaveEndDate.compareTo(beginOfDay) == 0) {
                    continue;
                }
                String leaveStartDateStr = DateUtil.format(leaveStartDate, "yyyy/MM/dd HH:mm");
                String leaveEndDateStr = DateUtil.format(leaveEndDate, "yyyy/MM/dd HH:mm");
                //构建请假实体
                UserDayAttendanceFormDTO formDTO = UserDayAttendanceFormDTO.builder().formId(formDO.getId()).formCode(formDO.getApplicationCode())
                        .formType(formDO.getFormType()).startTime(leaveStartDateStr).endTime(leaveEndDateStr).build();
                //设置单据状态和时间
                setFormStatus(formDTO, formDO.getFormStatus());
                setFormDuration(formDTO, dayDurationAttr);
                //设置假期类型
                if (CollectionUtils.isNotEmpty(leaveTypeDO)) {
                    String leaveType = leaveTypeDO.get(0).getAttrValue();
                    formDTO.setLeaveType(leaveType);
                    if (leaveType != null) {
                        Map<String, DictVO> leaveTypeEnumMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.HRMS_ATTENDANCE_LEAVE_TYPE);
                        Map<String, DictVO> lowerleaveTypeEnumMap = leaveTypeEnumMap.entrySet().stream().collect(Collectors.toMap(item -> item.getKey().toLowerCase(), Map.Entry::getValue));
                        DictVO dictVO = lowerleaveTypeEnumMap.get(leaveType.toLowerCase());
                        if (Objects.nonNull(dictVO)) {
                            leaveType = dictVO.getDataValue();
                        }
                    }
                    formDTO.setFormDesc(leaveType);
                }
                userDayFormList.add(formDTO);
            }
            if (CollectionUtils.isNotEmpty(outOfOfficeStartDateDO) && CollectionUtils.isNotEmpty(outOfOfficeEndDateDO)) {
                DateTime outOfOfficeStartDate = DateUtil.parse(outOfOfficeStartDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                DateTime outOfOfficeEndDate = DateUtil.parse(outOfOfficeEndDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Long outOfOfficeStartDayId = Long.valueOf(DateUtil.format(outOfOfficeStartDate, "yyyyMMdd"));
                Long outOfOfficeEndDayId = Long.valueOf(DateUtil.format(outOfOfficeEndDate, "yyyyMMdd"));
                if (outOfOfficeEndDayId.compareTo(dayId) < 0 || outOfOfficeStartDayId.compareTo(dayId) > 0) {
                    continue;
                }
                String outOfOfficeStartDateStr = DateUtil.format(outOfOfficeStartDate, "yyyy/MM/dd HH:mm");
                String outOfOfficeEndDateStr = DateUtil.format(outOfOfficeEndDate, "yyyy/MM/dd HH:mm");
                //构建外勤实体
                UserDayAttendanceFormDTO formDTO = UserDayAttendanceFormDTO.builder().formId(formDO.getId()).formCode(formDO.getApplicationCode())
                        .formType(formDO.getFormType()).startTime(outOfOfficeStartDateStr).endTime(outOfOfficeEndDateStr).build();
                //设置单据状态和时间
                setFormStatus(formDTO, formDO.getFormStatus());
                setFormDuration(formDTO, dayDurationAttr);
                //设置描述
                formDTO.setFormDesc(RequestInfoHolder.isChinese() ? "外勤" : "Out of office");
                userDayFormList.add(formDTO);
            }
            if (CollectionUtils.isNotEmpty(reissueCardDayIdDO)) {
                Long reissueCardDayId = Long.valueOf(reissueCardDayIdDO.get(0).getAttrValue());
                if (dayId.equals(reissueCardDayId)) {
                    DateTime reissueCardDate = DateUtil.parse(reissueCardDayIdDO.get(0).getAttrValue(), "yyyyMMdd");
                    String reissueCardDateStr = DateUtil.format(reissueCardDate, "yyyy/MM/dd");
                    //构建补卡实体
                    UserDayAttendanceFormDTO formDTO = UserDayAttendanceFormDTO.builder().formId(formDO.getId()).formCode(formDO.getApplicationCode())
                            .formType(formDO.getFormType()).reissueTime(reissueCardDateStr).build();
                    //设置单据状态
                    setFormStatus(formDTO, formDO.getFormStatus());
                    //设置异常类型
                    if (CollectionUtils.isNotEmpty(reissueCardTypeDO)) {
                        String abnormalType = reissueCardTypeDO.get(0).getAttrValue();
                        if (StringUtils.isNotBlank(abnormalType)) {
                            AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(abnormalType);
                            if (Objects.nonNull(abnormalTypeEnum)) {
                                formDTO.setAbnormalType(RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDesc() : abnormalTypeEnum.getDescEn());
                            }
                        }
                    }
                    //设置描述
                    formDTO.setFormDesc(RequestInfoHolder.isChinese() ? "补卡" : "Punch in/out Correction");
                    userDayFormList.add(formDTO);
                }
            }
        }
        return userDayFormList;
    }

    /**
     * 获取用户当天得出差单据
     *
     * @return
     */
    private List<UserDayAttendanceFormDTO> getUserDayTravel(Long dayId, List<BusinessTravelFormApiDTO> userTravelFormList) {
        if (CollectionUtils.isEmpty(userTravelFormList)) {
            return Collections.emptyList();
        }
        List<UserDayAttendanceFormDTO> userDayFormList = new ArrayList<>();
        for (BusinessTravelFormApiDTO travelFormDTO : userTravelFormList) {
            Date startDate = travelFormDTO.getStartDate();
            Date endDate = travelFormDTO.getEndDate();
            if (Objects.isNull(startDate) || Objects.isNull(endDate)) continue;
            Long startDayId = Long.valueOf(DateUtil.format(startDate, "yyyyMMdd"));
            Long endDayId = Long.valueOf(DateUtil.format(endDate, "yyyyMMdd"));
            if (dayId < startDayId || dayId > endDayId) continue;
            //构建出差实体
            UserDayAttendanceFormDTO formDTO = UserDayAttendanceFormDTO.builder()
                    .formCode(travelFormDTO.getApplicationCode())
                    .formType(travelFormDTO.getFromType())
                    .formDesc(RequestInfoHolder.isChinese() ? "出差" : "Business Trips")
                    .startTime(DateUtil.format(startDate, "yyyy/MM/dd"))
                    .endTime(DateUtil.format(endDate, "yyyy/MM/dd"))
                    .durationTime(travelFormDTO.getTravelDays() + (RequestInfoHolder.isChinese() ? "天" : "Days"))
                    .build();
            //设置单据状态
            setFormStatus(formDTO, travelFormDTO.getFromStatus());
            userDayFormList.add(formDTO);
        }
        return userDayFormList;
    }

    //设置单据状态
    private void setFormStatus(UserDayAttendanceFormDTO formDTO, String formStatus) {
        //设置单据状态
        if (StringUtils.isNotBlank(formStatus)) {
            HrAttendanceApplicationFormStatusEnum statusEnum = HrAttendanceApplicationFormStatusEnum.getInstance(formStatus);
            if (statusEnum != null) {
                formDTO.setFormStatus(RequestInfoHolder.isChinese() ? statusEnum.getDesc() : statusEnum.getDescEn());
            }
        }
    }

    //设置单据时间
    private void setFormDuration(UserDayAttendanceFormDTO formDTO, List<HrmsApplicationFormAttrDO> dayDurationAttr) {
        //设置请假/外勤单据时间
        if (CollectionUtils.isNotEmpty(dayDurationAttr)) {
            List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayDurationAttr.get(0).getAttrValue(), DayDurationInfoDTO.class);
            BigDecimal days = BigDecimal.ZERO;
            BigDecimal hours = BigDecimal.ZERO;
            BigDecimal minutes = BigDecimal.ZERO;
            for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                days = days.add(dayDurationInfoDTO.getDays());
                hours = hours.add(dayDurationInfoDTO.getHours());
                minutes = minutes.add(dayDurationInfoDTO.getMinutes());
            }
            if (RequestInfoHolder.isChinese()) {
                formDTO.setDurationTime(days + "天" + hours + "小时" + minutes + "分钟");
            } else {
                formDTO.setDurationTime(days + "days" + hours + "hours" + minutes + "minutes");
            }
            formDTO.setDays(days);
            formDTO.setHours(hours);
            formDTO.setMinutes(minutes);
        }
    }

    private void setExportQueryParam(AttendanceExportQueryDTO queryDTO) {
        // 拼接部门查询条件
        if (StringUtils.isNotBlank(queryDTO.getDeptIdString())) {
            List<String> deptIdStringList = Arrays.asList(queryDTO.getDeptIdString().split(","));
            List<Long> deptIdList = new ArrayList<>();
            deptIdStringList.forEach(item -> {
                deptIdList.add(Long.valueOf(item));
            });
            queryDTO.setDeptIds(deptIdList);
        }
        // 拼接员工状态查询条件
        if (StringUtils.isNotBlank(queryDTO.getWorkStatusString())) {
            queryDTO.setWorkStatus(Arrays.asList(queryDTO.getWorkStatusString().split(",")));
        }
        // 拼接打卡规则查询条件
        if (StringUtils.isNotBlank(queryDTO.getPunchConfigNoString())) {
            List<String> punchConfigNoList = Arrays.asList(queryDTO.getPunchConfigNoString().split(","));
            queryDTO.setPunchConfigNoList(punchConfigNoList);
        }
        // 拼接考勤日历查询条件
        if (StringUtils.isNotBlank(queryDTO.getAttendanceConfigNoString())) {
            List<String> attendanceConfigNoList = Arrays.asList(queryDTO.getAttendanceConfigNoString().split(","));
            queryDTO.setAttendanceConfigNoList(attendanceConfigNoList);
        }
        // 拼接常驻地城市查询条件
        if (StringUtils.isNotBlank(queryDTO.getLocationCityString())) {
            List<String> locationCityList = Arrays.asList(queryDTO.getLocationCityString().split(","));
            queryDTO.setLocationCityList(locationCityList);
        }
        // 拼接岗位查询条件
        if (StringUtils.isNotBlank(queryDTO.getPostIdString())) {
            List<String> postIdList = Arrays.asList(queryDTO.getPostIdString().split(","));
            List<Long> postIds = new ArrayList<>();
            postIdList.forEach(item -> {
                postIds.add(Long.valueOf(item));
            });
            queryDTO.setPostIds(postIds);
        }
    }

    private Date getMidDate(Date startDate, Date endDate) {
        // 获取开始时间和结束时间的毫秒数
        long startMillis = startDate.getTime();
        long endMillis = endDate.getTime();
        // 计算中间值的毫秒数
        long midpointMillis = (startMillis + endMillis) / 2;
        // 返回中间值对应的Date对象
        return new Date(midpointMillis);
    }

    private void setUserFreeWorkPunchTime(DayPunchTimeDTO dayPunchTimeDTO
            , List<UserPunchRecordDTO> userPunchRecordList
            , HrmsAttendancePunchClassItemConfigDO itemConfigDO
            , BigDecimal actualHours, BigDecimal actualHours_rest
            , Map<String, String> map) {
        Date earliestPunchInTime = userPunchRecordList.get(0).getFormatPunchTime();
        //上下班打卡时间间隔
        BigDecimal punchTimeInterval = itemConfigDO.getPunchTimeInterval();
        DateTime midDate = DateUtil.offsetHour(dayPunchTimeDTO.getDayPunchEndTime(), punchTimeInterval.negate().intValue());
        //如果最早的打卡时间 >= (最晚下班时间-打卡时间间隔),则为下班打卡
        if (userPunchRecordList.size() == 1) {
            if (earliestPunchInTime.compareTo(midDate) >= BusinessConstant.ZERO) {
                map.put("LatestPunchOutTime" + itemConfigDO.getSortNo(), DateUtil.format(earliestPunchInTime, "HH:mm"));
            } else {
                //设置最早打卡时间
                map.put("earliestPunchInTime" + itemConfigDO.getSortNo(), DateUtil.format(earliestPunchInTime, "HH:mm"));
            }
        }
        if (userPunchRecordList.size() > 1) {
            map.put("earliestPunchInTime" + itemConfigDO.getSortNo(), DateUtil.format(earliestPunchInTime, "HH:mm"));
            Date latestPunchOutTime = userPunchRecordList.get(userPunchRecordList.size() - 1).getFormatPunchTime();
            map.put("LatestPunchOutTime" + itemConfigDO.getSortNo(), DateUtil.format(latestPunchOutTime, "HH:mm"));
            BigDecimal diffHour = HrmsDateUtil.diffHour(latestPunchOutTime, earliestPunchInTime);
            actualHours_rest = actualHours_rest.add(diffHour);
            actualHours = actualHours.add(diffHour);
        }
        //设置出勤时间
        map.put("actualHours", actualHours.toString());
        map.put("actualHoursRest", actualHours_rest.toString());
    }

    private void setUserClassPunchTime(DayPunchTimeDTO dayPunchTimeDTO, List<UserPunchRecordDTO> userPunchRecordList
            , HrmsAttendancePunchClassItemConfigDO itemConfigDO, BigDecimal actualHours, BigDecimal actualHours_rest
            , Map<String, String> map) {
        //上班时间  默认和最早打卡时间同一天
        Date punchInTime = DateUtil.parse(DateUtil.format(dayPunchTimeDTO.getDayPunchStartTime(), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
        //上班时间早于最早打卡时间，跨天
        if (itemConfigDO.getPunchInTime().before(itemConfigDO.getEarliestPunchInTime())) {
            punchInTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(dayPunchTimeDTO.getDayPunchStartTime(), 1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
        }
        //下班时间  默认和最晚下班时间同一天
        Date punchOutTime = DateUtil.parse(DateUtil.format(dayPunchTimeDTO.getDayPunchEndTime(), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
        if (itemConfigDO.getPunchOutTime().after(itemConfigDO.getLatestPunchOutTime())) {
            punchOutTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(dayPunchTimeDTO.getDayPunchEndTime(), -1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
        }
        //获取中间时间
        Date midDate = getMidDate(punchInTime, punchOutTime);
        Date earliestPunchInTime = userPunchRecordList.get(0).getFormatPunchTime();
        //如果最早的打卡时间 >= 中间时间,则为下班打卡
        if (userPunchRecordList.size() == 1) {
            if (earliestPunchInTime.compareTo(midDate) >= BusinessConstant.ZERO) {
                map.put("LatestPunchOutTime" + itemConfigDO.getSortNo(), DateUtil.format(earliestPunchInTime, "HH:mm"));
            } else {
                //设置最早打卡时间
                map.put("earliestPunchInTime" + itemConfigDO.getSortNo(), DateUtil.format(earliestPunchInTime, "HH:mm"));
            }
        }
        if (userPunchRecordList.size() > 1) {
            //如果最早时间已经大于下班时间了，则不设置最早打卡时间
            if (earliestPunchInTime.compareTo(punchOutTime) < BusinessConstant.ZERO) {
                map.put("earliestPunchInTime" + itemConfigDO.getSortNo(), DateUtil.format(earliestPunchInTime, "HH:mm"));
            }
            Date latestPunchOutTime = userPunchRecordList.get(userPunchRecordList.size() - 1).getFormatPunchTime();
            map.put("LatestPunchOutTime" + itemConfigDO.getSortNo(), DateUtil.format(latestPunchOutTime, "HH:mm"));
            BigDecimal diffHour = HrmsDateUtil.diffHour(latestPunchOutTime, earliestPunchInTime);
            actualHours_rest = actualHours_rest.add(diffHour);
            //计算休息时间
            if (Objects.nonNull(itemConfigDO.getRestStartTime()) && Objects.nonNull(itemConfigDO.getRestEndTime())) {
                //休息开始时间,默认和开始时间同一天
                Date restStartTime = DateUtil.parse(DateUtil.format(punchInTime, "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                if (itemConfigDO.getRestStartTime().before(itemConfigDO.getPunchInTime())) {
                    restStartTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(punchInTime, 1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                }
                //休息结束时间,默认和开始时间同一天
                Date restEndTime = DateUtil.parse(DateUtil.format(restStartTime, "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                if (itemConfigDO.getRestEndTime().before(itemConfigDO.getRestStartTime())) {
                    restEndTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(restStartTime, 1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                }
                BigDecimal restHour = HrmsDateUtil.diffHour(restEndTime, restStartTime);
                //判断时间是否交叉
                if (earliestPunchInTime.compareTo(restEndTime) < 0 && restStartTime.compareTo(latestPunchOutTime) < 0) {
                    //比休息时间范围大
                    if (earliestPunchInTime.compareTo(restStartTime) < 0 && latestPunchOutTime.compareTo(restEndTime) > 0) {
                        diffHour = diffHour.subtract(restHour);
                    }
                    //存在交叉且最晚打卡时间 < 休息结束时间
                    if (earliestPunchInTime.compareTo(restStartTime) < 0 && latestPunchOutTime.compareTo(restEndTime) < 0) {
                        restHour = HrmsDateUtil.diffHour(latestPunchOutTime, restStartTime);
                        diffHour = diffHour.subtract(restHour);
                    }
                    //存在交叉且最晚打卡时间 > 休息结束时间
                    if (earliestPunchInTime.compareTo(restStartTime) > 0 && latestPunchOutTime.compareTo(restEndTime) > 0) {
                        restHour = HrmsDateUtil.diffHour(restEndTime, earliestPunchInTime);
                        diffHour = diffHour.subtract(restHour);
                    }
                    //判断上下班打卡时间是否在休息时间范围内
                    if ((earliestPunchInTime.compareTo(restStartTime) == 0 || earliestPunchInTime.after(restStartTime))
                            && (latestPunchOutTime.compareTo(restEndTime) == 0 || latestPunchOutTime.before(restEndTime))) {
                        diffHour = BigDecimal.ZERO;
                    }
                }
            }
            actualHours = actualHours.add(diffHour);
        }
        //设置出勤时间
        map.put("actualHours", actualHours.toString());
        map.put("actualHoursRest", actualHours_rest.toString());
    }

    private void setUserOncePunchTime(DayPunchTimeDTO dayPunchTimeDTO, List<UserPunchRecordDTO> userPunchRecordList
            , HrmsAttendancePunchClassItemConfigDO itemConfigDO, BigDecimal actualHours, BigDecimal actualHours_rest
            , Map<String, String> map, BigDecimal defaultLegalWorkingMinutes, BigDecimal leaveMinutes
            , List<UserDayAttendanceFormDTO> userDayFormList) {
//        //上班时间  默认和最早打卡时间同一天
//        Date punchInTime = DateUtil.parse(DateUtil.format(dayPunchTimeDTO.getDayPunchStartTime(), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
//        //上班时间早于最早打卡时间，跨天
//        if (itemConfigDO.getPunchInTime().before(itemConfigDO.getEarliestPunchInTime())) {
//            punchInTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(dayPunchTimeDTO.getDayPunchStartTime(), 1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
//        }
//        BigDecimal diffHour = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(userPunchRecordList)) {
            //未打卡不管当天有没有请假，出勤天数都是 0
            return;
        }
        //只设置时间，一次打卡不显示实际出勤时间
        Date earliestPunchInTime = userPunchRecordList.get(0).getFormatPunchTime();
        map.put("earliestPunchInTime" + itemConfigDO.getSortNo(), DateUtil.format(earliestPunchInTime, "HH:mm"));
//        // 一次打卡默认实际出勤时长 = 班次法定时长 - 请假时长
//        diffHour = (defaultLegalWorkingMinutes.subtract(leaveMinutes)).divide(new BigDecimal("60"), 2, RoundingMode.HALF_UP);
//        actualHours_rest = actualHours_rest.add(diffHour);
//
//        // 计算休息时间
//        if (Objects.nonNull(itemConfigDO.getRestStartTime()) && Objects.nonNull(itemConfigDO.getRestEndTime())) {
//            // 休息开始时间,默认和开始时间同一天
//            Date restStartTime = DateUtil.parse(DateUtil.format(punchInTime, "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
//            if (itemConfigDO.getRestStartTime().before(itemConfigDO.getPunchInTime())) {
//                restStartTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(punchInTime, 1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
//            }
//            // 休息结束时间,默认和开始时间同一天
//            Date restEndTime = DateUtil.parse(DateUtil.format(restStartTime, "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
//            if (itemConfigDO.getRestEndTime().before(itemConfigDO.getRestStartTime())) {
//                restEndTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(restStartTime, 1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
//            }
//            // 判断请假时间和休息时间是否有交叉
//            BigDecimal restMinutes = HrmsDateUtil.diffMins(restEndTime, restStartTime);
//            for (UserDayAttendanceFormDTO dayItemInfoDTO : userDayFormList) {
//                DateTime startTime = DateUtil.parse(dayItemInfoDTO.getStartTime(), "yyyy/MM/dd HH:mm");
//                DateTime endTime = DateUtil.parse(dayItemInfoDTO.getEndTime(), "yyyy/MM/dd HH:mm");
//                if (endTime.compareTo(restStartTime) < 1 || startTime.compareTo(restEndTime) > -1) {
//                    // 请假不在休息时间内
//                    continue;
//                }
//                if (startTime.compareTo(restStartTime) > -1 && endTime.compareTo(restEndTime) < 1) {
//                    // 请假完全在休息时间内
//                    diffHour.add(leaveMinutes.divide(new BigDecimal("60"), 2, RoundingMode.HALF_UP));
//                    continue;
//                }
//                if (startTime.compareTo(restStartTime) < 1 && endTime.compareTo(restEndTime) > -1) {
//                    // 请假完全包含休息时间
//                    restMinutes = BigDecimal.ZERO;
//                    continue;
//                }
//                if (startTime.compareTo(restStartTime) < 1) {
//                    restMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, endTime, DateUnit.MINUTE)));
//                    continue;
//                }
//                if (endTime.compareTo(restEndTime) > -1) {
//                    restMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restEndTime, startTime, DateUnit.MINUTE)));
//                }
//            }
//            if (restMinutes.compareTo(BigDecimal.ZERO) > 0) {
//                diffHour = diffHour.subtract(restMinutes.divide(new BigDecimal("60"), 2, RoundingMode.HALF_UP));
//            }
//        }
//        // 一次打卡默认有效出勤时长 = 班次法定时长 - 请假时长 - 休息时长
//        actualHours = actualHours.add(diffHour);
//        //设置出勤时间
//        map.put("actualHours", actualHours.toString());
//        map.put("actualHoursRest", actualHours_rest.toString());
    }

    /**
     * 生成排班明细
     *
     * @param employeeConfigDO
     * @param dayId
     * @param userCode
     * @return
     */
    private AttendanceClassDetailDTO buildEmployeeClassDTO(HrmsAttendanceClassEmployeeConfigDO employeeConfigDO, Long dayId, String userCode) {
        if (Objects.isNull(employeeConfigDO.getClassId()) || employeeConfigDO.getClassId() <= 0) {
            return AttendanceClassDetailDTO.builder().isWork(false).build();
        }
        //获取打卡规则
//        HrmsAttendancePunchConfigDO punchConfig = punchConfigDao.getById(employeeConfigDO.getPunchConfigId());
        HrmsAttendancePunchConfigDO punchConfig = punchConfigDaoFacade.getConfigAdapter().getById(employeeConfigDO.getPunchConfigId());
        if (Objects.isNull(punchConfig)) {
            return AttendanceClassDetailDTO.builder().isWork(false).build();
        }
        //获取当前时间的打卡班次及详情
//        List<HrmsAttendancePunchClassItemConfigDO> attendancePunchClassItemConfig = hrmsAttendancePunchClassItemConfigDao.selectItemConfigByClassId(Arrays.asList(employeeConfigDO.getClassId()));
        List<HrmsAttendancePunchClassItemConfigDO> attendancePunchClassItemConfig = punchConfigDaoFacade.getClassItemConfigAdapter().selectItemConfigByClassId(Arrays.asList(employeeConfigDO.getClassId()));
        hrmsAttendanceMobilePunchService.transferItemConfigTimeFormat(attendancePunchClassItemConfig, dayId);
        //排序
        List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOS = attendancePunchClassItemConfig
                .stream()
                .sorted(Comparator.comparing(HrmsAttendancePunchClassItemConfigDO::getSortNo))
                .collect(Collectors.toList());
        //自由打卡返回最早最晚时间即可
        HrmsAttendancePunchClassItemConfigDO itemConfigDO = itemConfigDOS.get(0);
        if (PunchConfigTypeEnum.FREE_WORK.getCode().equals(punchConfig.getPunchConfigType())) {
            return AttendanceClassDetailDTO.builder()
                    .dayId(dayId)
                    .classId(employeeConfigDO.getClassId())
                    .isWork(true)
                    .startTime(itemConfigDO.getEarliestPunchInTime())
                    .endTime(itemConfigDO.getLatestPunchOutTime())
                    .build();
        }
        //获取当前用户当天打卡信息
        EmployeePunchCardRecordQuery punchCardRecordQuery = new EmployeePunchCardRecordQuery();
        punchCardRecordQuery.setDayId(String.valueOf(dayId));
        punchCardRecordQuery.setUserCode(userCode);
        List<EmployeePunchRecordDO> userPunchRecords = punchRecordDao.listRecords(punchCardRecordQuery).stream()
                .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime)).collect(Collectors.toList());
        AttendanceClassDetailDTO userClassDetailDTO = AttendanceClassDetailDTO.builder()
                .dayId(dayId)
                .classId(employeeConfigDO.getClassId())
                .isWork(true)
                .startTime(itemConfigDO.getPunchInTime())
                .endTime(itemConfigDOS.size() > 1 ? itemConfigDOS.get(itemConfigDOS.size() - 1).getPunchOutTime()
                        : itemConfigDO.getPunchOutTime())
                .build();
        //没有打卡记录
        if (CollectionUtils.isEmpty(userPunchRecords)) {
            return userClassDetailDTO;
        }
        //存在多班次则判断最后班次的打卡时间
        if (itemConfigDOS.size() > 1) {
            itemConfigDO = itemConfigDOS.get(itemConfigDOS.size() - 1);
        }
        for (EmployeePunchRecordDO userPunchRecord : userPunchRecords) {
            //忽略秒
            Date punchTime = userPunchRecord.getPunchTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(punchTime);
            calendar.set(Calendar.SECOND, 0);
            punchTime = calendar.getTime();
            Date elasticPunchInTime = DateUtil.offsetMinute(itemConfigDO.getPunchInTime()
                    , itemConfigDO.getElasticTime().multiply(BusinessConstant.MINUTES).intValue());
            if (punchTime.compareTo(itemConfigDO.getPunchInTime()) > 0
                    && punchTime.compareTo(elasticPunchInTime) <= 0) {
                //存在弹性范围内打卡，默认下班时间加上弹性时间
                DateTime elasticPunchOutTime = DateUtil.offsetMinute(itemConfigDO.getPunchOutTime()
                        , itemConfigDO.getElasticTime().multiply(BusinessConstant.MINUTES).intValue());
                userClassDetailDTO.setEndTime(elasticPunchOutTime);
                break;
            }
        }
        return userClassDetailDTO;
    }
}
