package com.imile.hrms.common.adapter.distributed;

import lombok.Builder;
import lombok.Data;

/**
 * 事务操作结果
 * 
 * <AUTHOR> chen
 * @Date 2025/2/25
 * @Description
 */
@Data
@Builder
public class TransactionOperationResult<T> {
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 操作结果
     */
    private T result;
    
    /**
     * 操作ID
     */
    private String operationId;
    
    /**
     * 系统名称
     */
    private String systemName;
    
    /**
     * 错误信息
     */
    private Exception error;
    
    /**
     * 执行时间（毫秒）
     */
    private long executionTimeMs;
}
