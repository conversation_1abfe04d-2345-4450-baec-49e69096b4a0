package com.imile.hrms.common.adapter.distributed;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 分布式事务执行结果
 * 
 * <AUTHOR> chen
 * @Date 2025/2/25
 * @Description
 */
@Data
@Builder
public class DistributedTransactionResult<T> {
    
    /**
     * 整体事务是否成功
     */
    private boolean success;
    
    /**
     * 各个操作的结果
     */
    private List<TransactionOperationResult<T>> results;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 错误信息
     */
    private Exception error;
    
    /**
     * 总执行时间（毫秒）
     */
    private long totalExecutionTimeMs;
}
