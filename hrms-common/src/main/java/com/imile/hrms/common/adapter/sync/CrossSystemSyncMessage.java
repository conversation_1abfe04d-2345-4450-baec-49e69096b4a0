package com.imile.hrms.common.adapter.sync;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 跨系统同步消息
 * 
 * <AUTHOR> chen
 * @Date 2025/2/25
 * @Description
 */
@Data
@Builder
public class CrossSystemSyncMessage {
    
    /**
     * 操作ID
     */
    private String operationId;
    
    /**
     * 操作类型
     */
    private SyncOperationType operationType;
    
    /**
     * 源系统
     */
    private String sourceSystem;
    
    /**
     * 目标系统
     */
    private String targetSystem;
    
    /**
     * 数据类型
     */
    private String dataType;
    
    /**
     * 数据内容
     */
    private Object data;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 重试次数
     */
    private int retryCount;
    
    /**
     * 最大重试次数
     */
    private int maxRetryCount;
    
    public enum SyncOperationType {
        CREATE, UPDATE, DELETE
    }
}
