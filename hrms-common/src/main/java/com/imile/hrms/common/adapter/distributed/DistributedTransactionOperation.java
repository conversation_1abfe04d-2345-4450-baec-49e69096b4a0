package com.imile.hrms.common.adapter.distributed;

import lombok.Builder;
import lombok.Data;

import java.util.function.Supplier;

/**
 * 分布式事务操作定义
 * 
 * <AUTHOR> chen
 * @Date 2025/2/25
 * @Description
 */
@Data
@Builder
public class DistributedTransactionOperation<T> {
    
    /**
     * 操作ID
     */
    private String operationId;
    
    /**
     * 系统名称
     */
    private String systemName;
    
    /**
     * 执行操作
     */
    private Supplier<T> executeFunction;
    
    /**
     * 补偿操作
     */
    private Runnable compensateFunction;
    
    /**
     * 操作超时时间（毫秒）
     */
    private long timeoutMs;
    
    /**
     * 重试次数
     */
    private int retryCount;
    
    /**
     * 执行操作
     */
    public T execute() {
        return executeFunction.get();
    }
    
    /**
     * 执行补偿操作
     */
    public void compensate() {
        if (compensateFunction != null) {
            compensateFunction.run();
        }
    }
}
