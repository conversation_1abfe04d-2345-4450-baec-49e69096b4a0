package com.imile.hrms.common.adapter.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

/**
 * 跨系统适配器配置
 * 
 * <AUTHOR> chen
 * @Date 2025/2/25
 * @Description
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "hrms.cross-system")
public class CrossSystemAdapterConfig {
    
    /**
     * 是否启用跨系统模式
     */
    private boolean enabled = false;
    
    /**
     * 新系统配置
     */
    private SystemConfig newSystem = new SystemConfig();
    
    /**
     * 旧系统配置
     */
    private SystemConfig oldSystem = new SystemConfig();
    
    /**
     * 分布式事务配置
     */
    private DistributedTransactionConfig distributedTransaction = new DistributedTransactionConfig();
    
    /**
     * 同步配置
     */
    private SyncConfig sync = new SyncConfig();
    
    @Data
    public static class SystemConfig {
        /**
         * 系统名称
         */
        private String name;
        
        /**
         * 数据库配置
         */
        private String datasource;
        
        /**
         * API基础URL
         */
        private String baseUrl;
        
        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeout = 5000;
        
        /**
         * 读取超时时间（毫秒）
         */
        private int readTimeout = 10000;
    }
    
    @Data
    public static class DistributedTransactionConfig {
        /**
         * 默认超时时间（毫秒）
         */
        private long defaultTimeoutMs = 5000;
        
        /**
         * 默认重试次数
         */
        private int defaultRetryCount = 3;
        
        /**
         * 线程池大小
         */
        private int threadPoolSize = 10;
        
        /**
         * 是否启用补偿机制
         */
        private boolean compensationEnabled = true;
    }
    
    @Data
    public static class SyncConfig {
        /**
         * 是否启用异步同步
         */
        private boolean asyncEnabled = true;
        
        /**
         * Kafka配置
         */
        private KafkaConfig kafka = new KafkaConfig();
        
        /**
         * 批量同步大小
         */
        private int batchSize = 100;
        
        /**
         * 同步间隔（毫秒）
         */
        private long syncIntervalMs = 1000;
    }
    
    @Data
    public static class KafkaConfig {
        /**
         * 同步主题
         */
        private String syncTopic = "hrms.cross.system.sync";
        
        /**
         * 补偿主题
         */
        private String compensationTopic = "hrms.cross.system.compensation";
        
        /**
         * 消费者组
         */
        private String consumerGroup = "hrms-cross-system-group";
    }
}
