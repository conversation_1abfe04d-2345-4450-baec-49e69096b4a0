package com.imile.hrms.common.adapter.sync;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * 跨系统同步管理器
 * 使用消息队列实现异步数据同步
 * 
 * <AUTHOR> chen
 * @Date 2025/2/25
 * @Description
 */
@Slf4j
@Component
public class CrossSystemSyncManager {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String SYNC_TOPIC = "hrms.cross.system.sync";
    private static final String COMPENSATION_TOPIC = "hrms.cross.system.compensation";

    /**
     * 发送同步消息
     */
    public CompletableFuture<Void> sendSyncMessage(CrossSystemSyncMessage message) {
        return kafkaTemplate.send(SYNC_TOPIC, message.getOperationId(), message)
                .completable()
                .thenRun(() -> log.info("同步消息发送成功: {}", message.getOperationId()));
    }

    /**
     * 发送补偿消息
     */
    public CompletableFuture<Void> sendCompensationMessage(CrossSystemCompensationMessage message) {
        return kafkaTemplate.send(COMPENSATION_TOPIC, message.getOperationId(), message)
                .completable()
                .thenRun(() -> log.info("补偿消息发送成功: {}", message.getOperationId()));
    }

    /**
     * 处理同步消息
     */
    @KafkaListener(topics = SYNC_TOPIC, groupId = "hrms-sync-group")
    public void handleSyncMessage(CrossSystemSyncMessage message) {
        try {
            log.info("处理同步消息: {}", message.getOperationId());
            
            switch (message.getOperationType()) {
                case CREATE:
                    handleCreateSync(message);
                    break;
                case UPDATE:
                    handleUpdateSync(message);
                    break;
                case DELETE:
                    handleDeleteSync(message);
                    break;
                default:
                    log.warn("未知的同步操作类型: {}", message.getOperationType());
            }
            
        } catch (Exception e) {
            log.error("处理同步消息失败: {}", message.getOperationId(), e);
            // 发送补偿消息
            sendCompensationMessage(CrossSystemCompensationMessage.builder()
                .operationId(message.getOperationId())
                .originalMessage(message)
                .error(e.getMessage())
                .build());
        }
    }

    /**
     * 处理补偿消息
     */
    @KafkaListener(topics = COMPENSATION_TOPIC, groupId = "hrms-compensation-group")
    public void handleCompensationMessage(CrossSystemCompensationMessage message) {
        try {
            log.info("处理补偿消息: {}", message.getOperationId());
            
            // 执行补偿逻辑
            executeCompensation(message);
            
        } catch (Exception e) {
            log.error("处理补偿消息失败: {}", message.getOperationId(), e);
            // 可以考虑发送到死信队列或人工处理
        }
    }

    private void handleCreateSync(CrossSystemSyncMessage message) {
        // 实现创建同步逻辑
        log.info("执行创建同步: {}", message.getOperationId());
    }

    private void handleUpdateSync(CrossSystemSyncMessage message) {
        // 实现更新同步逻辑
        log.info("执行更新同步: {}", message.getOperationId());
    }

    private void handleDeleteSync(CrossSystemSyncMessage message) {
        // 实现删除同步逻辑
        log.info("执行删除同步: {}", message.getOperationId());
    }

    private void executeCompensation(CrossSystemCompensationMessage message) {
        // 实现补偿逻辑
        log.info("执行补偿操作: {}", message.getOperationId());
    }
}
