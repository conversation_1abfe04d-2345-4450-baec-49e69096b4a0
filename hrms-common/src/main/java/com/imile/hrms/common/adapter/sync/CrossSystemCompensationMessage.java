package com.imile.hrms.common.adapter.sync;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 跨系统补偿消息
 * 
 * <AUTHOR> chen
 * @Date 2025/2/25
 * @Description
 */
@Data
@Builder
public class CrossSystemCompensationMessage {
    
    /**
     * 操作ID
     */
    private String operationId;
    
    /**
     * 原始同步消息
     */
    private CrossSystemSyncMessage originalMessage;
    
    /**
     * 错误信息
     */
    private String error;
    
    /**
     * 补偿类型
     */
    private CompensationType compensationType;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 处理状态
     */
    private CompensationStatus status;
    
    public enum CompensationType {
        ROLLBACK,    // 回滚操作
        RETRY,       // 重试操作
        MANUAL       // 人工处理
    }
    
    public enum CompensationStatus {
        PENDING,     // 待处理
        PROCESSING,  // 处理中
        SUCCESS,     // 成功
        FAILED       // 失败
    }
}
