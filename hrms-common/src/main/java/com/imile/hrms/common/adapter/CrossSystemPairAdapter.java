package com.imile.hrms.common.adapter;

import com.imile.hrms.common.adapter.distributed.DistributedTransactionManager;
import com.imile.hrms.common.adapter.distributed.DistributedTransactionOperation;
import com.imile.hrms.common.adapter.distributed.DistributedTransactionResult;
import com.imile.hrms.common.adapter.exception.DataInconsistencyException;
import com.imile.hrms.common.adapter.exception.DataWriteException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 跨系统配对适配器
 * 支持不同数据库、跨事务的数据转换和同步
 * 
 * <AUTHOR> chen
 * @Date 2025/2/25
 * @Description
 */
@Slf4j
public abstract class CrossSystemPairAdapter<NEW, OLD> extends CrossSystemAbstractAdapter {

    private final DataConverter<NEW, OLD> converter;
    private final Class<NEW> newType;
    private final Class<OLD> oldType;

    @Autowired
    private DistributedTransactionManager distributedTransactionManager;

    @Autowired
    public CrossSystemPairAdapter(List<DataConverter<NEW, OLD>> converters) {
        TypeResolver typeResolver = new TypeResolver(getClass());
        this.newType = typeResolver.resolveType(0);
        this.oldType = typeResolver.resolveType(1);
        this.converter = selectConverter(converters);
    }

    private DataConverter<NEW, OLD> selectConverter(List<DataConverter<NEW, OLD>> converters) {
        return converters.stream()
                .filter(c -> c.getNewType().equals(newType) && c.getOldType().equals(oldType))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException(
                        String.format("No suitable DataConverter found for NEW type: %s and OLD type: %s",
                                newType.getName(), oldType.getName())
                ));
    }

    //============================跨系统读取操作==============================================

    /**
     * 跨系统读取单个对象（返回OLD格式）
     */
    protected OLD crossSystemRead(Supplier<NEW> newSupplier, Supplier<OLD> oldSupplier) {
        return shouldReadNew()
                ? Optional.ofNullable(newSupplier.get())
                .map(converter::convertFromNew)
                .orElse(null)
                : oldSupplier.get();
    }

    /**
     * 跨系统读取单个对象（返回NEW格式）
     */
    protected NEW crossSystemReadNew(Supplier<NEW> newSupplier, Supplier<OLD> oldSupplier) {
        return shouldReadNew()
                ? newSupplier.get()
                : Optional.ofNullable(oldSupplier.get())
                .map(converter::convertFromOld)
                .orElse(null);
    }

    /**
     * 跨系统批量读取（返回OLD格式）
     */
    protected List<OLD> crossSystemBatchRead(
            Supplier<List<NEW>> newBatchSupplier,
            Supplier<List<OLD>> oldBatchSupplier) {
        if (!shouldReadNew()) {
            return oldBatchSupplier.get();
        }

        return Optional.ofNullable(newBatchSupplier.get())
                .map(this::convertBatchToOld)
                .orElseGet(Collections::emptyList);
    }

    /**
     * 跨系统批量读取（返回NEW格式）
     */
    protected List<NEW> crossSystemBatchReadNew(
            Supplier<List<NEW>> newBatchSupplier,
            Supplier<List<OLD>> oldBatchSupplier) {
        if (shouldReadNew()) {
            return newBatchSupplier.get();
        }
        return Optional.ofNullable(oldBatchSupplier.get())
                .map(this::convertBatchToNew)
                .orElseGet(Collections::emptyList);
    }

    /**
     * 异步跨系统读取
     */
    protected CompletableFuture<OLD> crossSystemReadAsync(
            Supplier<NEW> newSupplier, 
            Supplier<OLD> oldSupplier) {
        return CompletableFuture.supplyAsync(() -> crossSystemRead(newSupplier, oldSupplier));
    }

    //============================跨系统写入操作==============================================

    /**
     * 跨系统保存或更新单个对象（基于OLD对象）
     */
    protected void crossSystemSaveOrUpdate(
            OLD oldData,
            Consumer<NEW> newConsumer,
            Consumer<OLD> oldConsumer) {
        
        List<DistributedTransactionOperation<Void>> operations = new ArrayList<>();
        
        if (shouldWriteOld()) {
            operations.add(DistributedTransactionOperation.<Void>builder()
                .operationId("old-system-save-" + oldData.hashCode())
                .systemName("OLD_SYSTEM")
                .executeFunction(() -> {
                    oldConsumer.accept(oldData);
                    return null;
                })
                .compensateFunction(() -> executeOldSystemCompensation())
                .timeoutMs(5000)
                .retryCount(3)
                .build());
        }
        
        if (shouldWriteNew()) {
            NEW newData = converter.convertFromOld(oldData);
            operations.add(DistributedTransactionOperation.<Void>builder()
                .operationId("new-system-save-" + newData.hashCode())
                .systemName("NEW_SYSTEM")
                .executeFunction(() -> {
                    newConsumer.accept(newData);
                    return null;
                })
                .compensateFunction(() -> executeNewSystemCompensation())
                .timeoutMs(5000)
                .retryCount(3)
                .build());
        }
        
        DistributedTransactionResult<Void> result = 
            distributedTransactionManager.executeDistributedTransaction(operations);
        
        if (!result.isSuccess()) {
            throw new DataWriteException("跨系统保存失败: " + result.getMessage(), result.getError());
        }
    }

    /**
     * 跨系统保存或更新单个对象（基于NEW对象）
     */
    protected void crossSystemSaveOrUpdateNew(
            NEW newData,
            Consumer<NEW> newConsumer,
            Consumer<OLD> oldConsumer) {
        
        List<DistributedTransactionOperation<Void>> operations = new ArrayList<>();
        
        if (shouldWriteNew()) {
            operations.add(DistributedTransactionOperation.<Void>builder()
                .operationId("new-system-save-" + newData.hashCode())
                .systemName("NEW_SYSTEM")
                .executeFunction(() -> {
                    newConsumer.accept(newData);
                    return null;
                })
                .compensateFunction(() -> executeNewSystemCompensation())
                .timeoutMs(5000)
                .retryCount(3)
                .build());
        }
        
        if (shouldWriteOld()) {
            OLD oldData = converter.convertFromNew(newData);
            operations.add(DistributedTransactionOperation.<Void>builder()
                .operationId("old-system-save-" + oldData.hashCode())
                .systemName("OLD_SYSTEM")
                .executeFunction(() -> {
                    oldConsumer.accept(oldData);
                    return null;
                })
                .compensateFunction(() -> executeOldSystemCompensation())
                .timeoutMs(5000)
                .retryCount(3)
                .build());
        }
        
        DistributedTransactionResult<Void> result = 
            distributedTransactionManager.executeDistributedTransaction(operations);
        
        if (!result.isSuccess()) {
            throw new DataWriteException("跨系统保存失败: " + result.getMessage(), result.getError());
        }
    }

    /**
     * 跨系统批量保存或更新
     */
    protected void crossSystemBatchSaveOrUpdate(
            Collection<OLD> oldDataList,
            Consumer<Collection<NEW>> newBatchConsumer,
            Consumer<Collection<OLD>> oldBatchConsumer) {
        
        List<DistributedTransactionOperation<Void>> operations = new ArrayList<>();
        
        if (shouldWriteOld()) {
            operations.add(DistributedTransactionOperation.<Void>builder()
                .operationId("old-system-batch-save")
                .systemName("OLD_SYSTEM")
                .executeFunction(() -> {
                    oldBatchConsumer.accept(oldDataList);
                    return null;
                })
                .compensateFunction(() -> executeOldSystemBatchCompensation(new ArrayList<>(oldDataList)))
                .timeoutMs(10000)
                .retryCount(2)
                .build());
        }
        
        if (shouldWriteNew()) {
            Collection<NEW> newDataList = oldDataList.stream()
                .map(converter::convertFromOld)
                .collect(Collectors.toList());
            
            operations.add(DistributedTransactionOperation.<Void>builder()
                .operationId("new-system-batch-save")
                .systemName("NEW_SYSTEM")
                .executeFunction(() -> {
                    newBatchConsumer.accept(newDataList);
                    return null;
                })
                .compensateFunction(() -> executeNewSystemBatchCompensation(new ArrayList<>(newDataList)))
                .timeoutMs(10000)
                .retryCount(2)
                .build());
        }
        
        DistributedTransactionResult<Void> result = 
            distributedTransactionManager.executeDistributedTransaction(operations);
        
        if (!result.isSuccess()) {
            throw new DataWriteException("跨系统批量保存失败: " + result.getMessage(), result.getError());
        }
    }

    //============================数据转换和一致性校验==============================================

    protected List<OLD> convertBatchToOld(List<NEW> newDataList) {
        return newDataList.parallelStream()
                .map(converter::convertFromNew)
                .collect(Collectors.toList());
    }

    protected List<NEW> convertBatchToNew(List<OLD> oldDataList) {
        return oldDataList.parallelStream()
                .map(converter::convertFromOld)
                .collect(Collectors.toList());
    }

    /**
     * 跨系统数据一致性校验
     */
    protected void validateCrossSystemConsistency(List<NEW> newDataList, List<OLD> oldDataList) {
        List<OLD> convertedNewData = convertBatchToOld(newDataList);
        Set<OLD> newDataSet = new LinkedHashSet<>(convertedNewData);
        Set<OLD> oldDataSet = new LinkedHashSet<>(oldDataList);

        if (!newDataSet.equals(oldDataSet)) {
            throw new DataInconsistencyException("跨系统数据不一致", newDataSet, oldDataSet);
        }
    }

    /**
     * 异步数据一致性校验
     */
    protected CompletableFuture<Boolean> validateCrossSystemConsistencyAsync(
            Supplier<List<NEW>> newDataSupplier,
            Supplier<List<OLD>> oldDataSupplier) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                List<NEW> newData = newDataSupplier.get();
                List<OLD> oldData = oldDataSupplier.get();
                validateCrossSystemConsistency(newData, oldData);
                return true;
            } catch (Exception e) {
                log.error("跨系统数据一致性校验失败", e);
                return false;
            }
        });
    }
}
