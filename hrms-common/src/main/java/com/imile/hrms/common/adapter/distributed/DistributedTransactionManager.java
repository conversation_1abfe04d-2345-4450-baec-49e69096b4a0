package com.imile.hrms.common.adapter.distributed;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Supplier;

/**
 * 分布式事务管理器
 * 用于管理跨系统的事务操作
 * 
 * <AUTHOR> chen
 * @Date 2025/2/25
 * @Description
 */
@Slf4j
@Component
public class DistributedTransactionManager {

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 执行分布式事务操作
     * 
     * @param operations 事务操作列表
     * @return 执行结果
     */
    public <T> DistributedTransactionResult<T> executeDistributedTransaction(
            List<DistributedTransactionOperation<T>> operations) {
        
        List<CompletableFuture<TransactionOperationResult<T>>> futures = new ArrayList<>();
        List<TransactionOperationResult<T>> results = new ArrayList<>();
        
        try {
            // 第一阶段：并行执行所有操作
            for (DistributedTransactionOperation<T> operation : operations) {
                CompletableFuture<TransactionOperationResult<T>> future = 
                    CompletableFuture.supplyAsync(() -> {
                        try {
                            T result = operation.execute();
                            return TransactionOperationResult.<T>builder()
                                .success(true)
                                .result(result)
                                .operationId(operation.getOperationId())
                                .systemName(operation.getSystemName())
                                .build();
                        } catch (Exception e) {
                            log.error("分布式事务操作失败: {}", operation.getOperationId(), e);
                            return TransactionOperationResult.<T>builder()
                                .success(false)
                                .error(e)
                                .operationId(operation.getOperationId())
                                .systemName(operation.getSystemName())
                                .build();
                        }
                    }, executorService);
                futures.add(future);
            }
            
            // 等待所有操作完成
            for (CompletableFuture<TransactionOperationResult<T>> future : futures) {
                results.add(future.get());
            }
            
            // 检查是否有失败的操作
            boolean hasFailure = results.stream().anyMatch(r -> !r.isSuccess());
            
            if (hasFailure) {
                // 第二阶段：执行补偿操作
                executeCompensation(operations, results);
                return DistributedTransactionResult.<T>builder()
                    .success(false)
                    .results(results)
                    .message("分布式事务执行失败，已执行补偿操作")
                    .build();
            }
            
            return DistributedTransactionResult.<T>builder()
                .success(true)
                .results(results)
                .message("分布式事务执行成功")
                .build();
                
        } catch (Exception e) {
            log.error("分布式事务执行异常", e);
            // 执行补偿操作
            executeCompensation(operations, results);
            return DistributedTransactionResult.<T>builder()
                .success(false)
                .results(results)
                .error(e)
                .message("分布式事务执行异常")
                .build();
        }
    }
    
    /**
     * 执行补偿操作
     */
    private <T> void executeCompensation(
            List<DistributedTransactionOperation<T>> operations,
            List<TransactionOperationResult<T>> results) {
        
        log.info("开始执行分布式事务补偿操作");
        
        for (int i = 0; i < operations.size(); i++) {
            DistributedTransactionOperation<T> operation = operations.get(i);
            if (i < results.size() && results.get(i).isSuccess()) {
                try {
                    operation.compensate();
                    log.info("补偿操作成功: {}", operation.getOperationId());
                } catch (Exception e) {
                    log.error("补偿操作失败: {}", operation.getOperationId(), e);
                }
            }
        }
    }
    
    /**
     * 异步执行分布式事务
     */
    public <T> CompletableFuture<DistributedTransactionResult<T>> executeDistributedTransactionAsync(
            List<DistributedTransactionOperation<T>> operations) {
        return CompletableFuture.supplyAsync(() -> executeDistributedTransaction(operations), executorService);
    }
}
