package com.imile.hrms.manage.newAttendance.punchConfig.adapter;

import com.imile.hrms.common.adapter.CrossSystemPairAdapter;
import com.imile.hrms.common.adapter.DaoAdapter;
import com.imile.hrms.common.adapter.DataConverter;
import com.imile.hrms.common.adapter.config.EnableNewAttendanceConfig;
import com.imile.hrms.common.adapter.sync.CrossSystemSyncManager;
import com.imile.hrms.common.adapter.sync.CrossSystemSyncMessage;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.PunchConfigDao;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchConfigDO;
import com.imile.hrms.dao.punch.dao.HrmsAttendancePunchConfigDao;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigDO;
import com.imile.hrms.manage.newAttendance.config.EnableNewAttendanceConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 跨系统打卡配置适配器
 * 支持不同数据库的打卡配置数据同步
 * 
 * <AUTHOR> chen
 * @Date 2025/2/25
 * @Description
 */
@Slf4j
@Component
public class CrossSystemPunchConfigAdapter extends CrossSystemPairAdapter<PunchConfigDO, HrmsAttendancePunchConfigDO> 
        implements DaoAdapter {

    @Resource
    private EnableNewAttendanceConfig config;
    
    @Resource
    private HrmsAttendancePunchConfigDao oldPunchConfigDao;
    
    @Resource
    private PunchConfigDao newPunchConfigDao;
    
    @Autowired
    private CrossSystemSyncManager syncManager;

    public CrossSystemPunchConfigAdapter(List<DataConverter<PunchConfigDO, HrmsAttendancePunchConfigDO>> dataConverters) {
        super(dataConverters);
    }

    @Override
    public Boolean isEnableNewModule() {
        return config.getIsEnablePunchConfig();
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return config.getPunchConfigDoubleWriteEnabled();
    }

    //============================业务方法==============================================

    /**
     * 根据ID查询打卡配置
     */
    public HrmsAttendancePunchConfigDO findById(Long id) {
        return crossSystemRead(
            () -> newPunchConfigDao.selectById(id),
            () -> oldPunchConfigDao.selectById(id)
        );
    }

    /**
     * 根据公司ID查询打卡配置列表
     */
    public List<HrmsAttendancePunchConfigDO> findByCompanyId(String companyId) {
        return crossSystemBatchRead(
            () -> newPunchConfigDao.selectByCompanyId(companyId),
            () -> oldPunchConfigDao.selectByCompanyId(companyId)
        );
    }

    /**
     * 保存打卡配置
     */
    public void save(HrmsAttendancePunchConfigDO punchConfig) {
        crossSystemSaveOrUpdate(
            punchConfig,
            newData -> {
                newPunchConfigDao.insert(newData);
                // 发送同步消息
                sendSyncMessage("CREATE", newData);
            },
            oldData -> {
                oldPunchConfigDao.insert(oldData);
                log.info("旧系统保存打卡配置成功: {}", oldData.getId());
            }
        );
    }

    /**
     * 更新打卡配置
     */
    public void update(HrmsAttendancePunchConfigDO punchConfig) {
        crossSystemSaveOrUpdate(
            punchConfig,
            newData -> {
                newPunchConfigDao.updateById(newData);
                // 发送同步消息
                sendSyncMessage("UPDATE", newData);
            },
            oldData -> {
                oldPunchConfigDao.updateById(oldData);
                log.info("旧系统更新打卡配置成功: {}", oldData.getId());
            }
        );
    }

    /**
     * 删除打卡配置
     */
    public void deleteById(Long id) {
        // 先查询要删除的数据
        HrmsAttendancePunchConfigDO toDelete = findById(id);
        if (toDelete == null) {
            log.warn("要删除的打卡配置不存在: {}", id);
            return;
        }

        crossSystemWrite(
            () -> {
                newPunchConfigDao.deleteById(id);
                // 发送同步消息
                sendSyncMessage("DELETE", toDelete);
            },
            () -> {
                oldPunchConfigDao.deleteById(id);
                log.info("旧系统删除打卡配置成功: {}", id);
            }
        );
    }

    /**
     * 批量保存打卡配置
     */
    public void batchSave(List<HrmsAttendancePunchConfigDO> punchConfigs) {
        crossSystemBatchSaveOrUpdate(
            punchConfigs,
            newDataList -> {
                newPunchConfigDao.insertBatch(newDataList);
                // 发送批量同步消息
                newDataList.forEach(data -> sendSyncMessage("CREATE", data));
            },
            oldDataList -> {
                oldPunchConfigDao.insertBatch(oldDataList);
                log.info("旧系统批量保存打卡配置成功，数量: {}", oldDataList.size());
            }
        );
    }

    //============================补偿操作实现==============================================

    @Override
    protected void executeOldSystemCompensation() {
        log.info("执行旧系统打卡配置补偿操作");
        // 实现具体的补偿逻辑，比如回滚最近的操作
        // 这里可以根据业务需求实现具体的补偿策略
    }

    @Override
    protected void executeNewSystemCompensation() {
        log.info("执行新系统打卡配置补偿操作");
        // 实现具体的补偿逻辑
        // 可以调用新系统的API进行数据回滚
    }

    @Override
    protected <T> void executeOldSystemBatchCompensation(List<T> dataList) {
        log.info("执行旧系统打卡配置批量补偿操作，数据量: {}", dataList.size());
        // 实现批量补偿逻辑
    }

    @Override
    protected <T> void executeNewSystemBatchCompensation(List<T> dataList) {
        log.info("执行新系统打卡配置批量补偿操作，数据量: {}", dataList.size());
        // 实现批量补偿逻辑
    }

    //============================私有方法==============================================

    /**
     * 发送同步消息
     */
    private void sendSyncMessage(String operationType, Object data) {
        try {
            CrossSystemSyncMessage message = CrossSystemSyncMessage.builder()
                .operationId(UUID.randomUUID().toString())
                .operationType(CrossSystemSyncMessage.SyncOperationType.valueOf(operationType))
                .sourceSystem("HRMS_OLD")
                .targetSystem("HRMS_NEW")
                .dataType("PUNCH_CONFIG")
                .data(data)
                .createTime(LocalDateTime.now())
                .retryCount(0)
                .maxRetryCount(3)
                .build();
            
            syncManager.sendSyncMessage(message);
        } catch (Exception e) {
            log.error("发送同步消息失败", e);
        }
    }

    /**
     * 数据一致性校验
     */
    public boolean validateDataConsistency(String companyId) {
        try {
            return validateCrossSystemConsistencyAsync(
                () -> newPunchConfigDao.selectByCompanyId(companyId),
                () -> oldPunchConfigDao.selectByCompanyId(companyId)
            ).get();
        } catch (Exception e) {
            log.error("数据一致性校验失败", e);
            return false;
        }
    }
}
