# HRMS跨系统迁移适配器架构设计方案

## 1. 方案概述

本方案针对HRMS系统从单一数据库架构迁移到跨系统（不同数据库、跨事务）架构的需求，提供了一套完整的适配器解决方案。该方案基于现有的适配器模式进行扩展，支持分布式事务管理、异步数据同步和补偿机制。

## 2. 架构设计

### 2.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐
│   旧系统(HRMS)   │    │   新系统(NEW)    │
│   MySQL数据库    │    │   PostgreSQL    │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
    ┌────────────────▼────────────────┐
    │     跨系统适配器层(Adapter)      │
    │  ┌─────────────────────────────┐ │
    │  │  分布式事务管理器            │ │
    │  └─────────────────────────────┘ │
    │  ┌─────────────────────────────┐ │
    │  │  数据转换器(Converter)       │ │
    │  └─────────────────────────────┘ │
    │  ┌─────────────────────────────┐ │
    │  │  异步同步管理器              │ │
    │  └─────────────────────────────┘ │
    └─────────────────────────────────┘
                     │
         ┌───────────▼───────────┐
         │    消息队列(Kafka)     │
         │   同步/补偿消息处理     │
         └───────────────────────┘
```

### 2.2 核心组件

#### 2.2.1 分布式事务管理器 (DistributedTransactionManager)
- **功能**：管理跨系统的事务操作，确保数据一致性
- **特性**：
  - 支持并行执行多个系统操作
  - 自动补偿机制（Saga模式）
  - 超时和重试机制
  - 异步执行支持

#### 2.2.2 跨系统抽象适配器 (CrossSystemAbstractAdapter)
- **功能**：提供跨系统操作的基础能力
- **特性**：
  - 统一的读写接口
  - 异步操作支持
  - 批量操作优化
  - 补偿操作抽象

#### 2.2.3 跨系统配对适配器 (CrossSystemPairAdapter)
- **功能**：处理新旧系统数据对象的转换和同步
- **特性**：
  - 自动数据类型转换
  - 批量数据处理
  - 数据一致性校验
  - 异步一致性检查

#### 2.2.4 异步同步管理器 (CrossSystemSyncManager)
- **功能**：使用消息队列实现异步数据同步
- **特性**：
  - Kafka消息队列支持
  - 同步消息处理
  - 补偿消息处理
  - 死信队列支持

## 3. 关键技术实现

### 3.1 分布式事务处理

采用**Saga模式**实现分布式事务：

```java
// 事务操作定义
List<DistributedTransactionOperation<Void>> operations = new ArrayList<>();

// 旧系统操作
operations.add(DistributedTransactionOperation.<Void>builder()
    .operationId("old-system-write")
    .systemName("OLD_SYSTEM")
    .executeFunction(() -> {
        oldSystemDao.save(data);
        return null;
    })
    .compensateFunction(() -> {
        oldSystemDao.rollback(data);
    })
    .timeoutMs(5000)
    .retryCount(3)
    .build());

// 新系统操作
operations.add(DistributedTransactionOperation.<Void>builder()
    .operationId("new-system-write")
    .systemName("NEW_SYSTEM")
    .executeFunction(() -> {
        newSystemApi.save(convertedData);
        return null;
    })
    .compensateFunction(() -> {
        newSystemApi.rollback(convertedData);
    })
    .timeoutMs(5000)
    .retryCount(3)
    .build());

// 执行分布式事务
DistributedTransactionResult<Void> result = 
    distributedTransactionManager.executeDistributedTransaction(operations);
```

### 3.2 异步数据同步

使用Kafka实现异步数据同步：

```java
// 发送同步消息
CrossSystemSyncMessage message = CrossSystemSyncMessage.builder()
    .operationId(UUID.randomUUID().toString())
    .operationType(SyncOperationType.CREATE)
    .sourceSystem("HRMS_OLD")
    .targetSystem("HRMS_NEW")
    .dataType("PUNCH_CONFIG")
    .data(punchConfig)
    .createTime(LocalDateTime.now())
    .build();

syncManager.sendSyncMessage(message);
```

### 3.3 数据一致性保证

#### 3.3.1 强一致性（同步模式）
- 使用分布式事务确保操作原子性
- 失败时自动执行补偿操作
- 支持事务回滚

#### 3.3.2 最终一致性（异步模式）
- 通过消息队列异步同步数据
- 支持重试机制
- 提供数据校验接口

### 3.4 补偿机制

```java
// 补偿操作实现
@Override
protected void executeOldSystemCompensation() {
    // 1. 查询最近的操作记录
    // 2. 执行回滚操作
    // 3. 记录补偿日志
    log.info("执行旧系统补偿操作");
}

@Override
protected void executeNewSystemCompensation() {
    // 1. 调用新系统回滚API
    // 2. 处理回滚结果
    // 3. 记录补偿日志
    log.info("执行新系统补偿操作");
}
```

## 4. 迁移策略

### 4.1 四阶段迁移

| 阶段 | 配置 | 读取策略 | 写入策略 | 说明 |
|------|------|----------|----------|------|
| 阶段1 | `enableNew=false`<br>`doubleWrite=false` | 旧系统 | 旧系统 | 迁移准备阶段 |
| 阶段2 | `enableNew=true`<br>`doubleWrite=true` | 按用户配置 | 双写 | 灰度验证阶段 |
| 阶段3 | `enableNew=true`<br>`doubleWrite=false` | 新系统 | 新系统 | 逐步切换阶段 |
| 阶段4 | `enableNew=true`<br>`doubleWrite=false` | 新系统 | 新系统 | 完全迁移阶段 |

### 4.2 用户级别灰度

```yaml
# 配置示例
hrms:
  cross-system:
    enabled: true
    migration:
      enable-users: "888888,2103429201"  # 启用新系统的用户
      enable-departments: "dept001,dept002"  # 启用新系统的部门
```

## 5. 配置说明

### 5.1 应用配置

```yaml
hrms:
  cross-system:
    enabled: true
    new-system:
      name: "NEW_HRMS"
      base-url: "http://new-hrms-api:8080"
      connect-timeout: 5000
      read-timeout: 10000
    old-system:
      name: "OLD_HRMS"
      datasource: "hrms-old-db"
    distributed-transaction:
      default-timeout-ms: 5000
      default-retry-count: 3
      thread-pool-size: 10
      compensation-enabled: true
    sync:
      async-enabled: true
      batch-size: 100
      sync-interval-ms: 1000
      kafka:
        sync-topic: "hrms.cross.system.sync"
        compensation-topic: "hrms.cross.system.compensation"
        consumer-group: "hrms-cross-system-group"
```

### 5.2 数据源配置

```yaml
spring:
  datasource:
    # 旧系统数据源
    old:
      url: ************************************
      username: hrms_old
      password: password
      driver-class-name: com.mysql.cj.jdbc.Driver
    # 新系统数据源
    new:
      url: *****************************************
      username: hrms_new
      password: password
      driver-class-name: org.postgresql.Driver
```

## 6. 使用示例

### 6.1 创建跨系统适配器

```java
@Component
public class CrossSystemPunchConfigAdapter 
    extends CrossSystemPairAdapter<PunchConfigDO, HrmsAttendancePunchConfigDO> 
    implements DaoAdapter {

    @Resource
    private EnableNewAttendanceConfig config;
    
    @Resource
    private HrmsAttendancePunchConfigDao oldDao;
    
    @Resource
    private PunchConfigDao newDao;

    @Override
    public Boolean isEnableNewModule() {
        return config.getIsEnablePunchConfig();
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return config.getPunchConfigDoubleWriteEnabled();
    }

    // 业务方法实现
    public void save(HrmsAttendancePunchConfigDO punchConfig) {
        crossSystemSaveOrUpdate(
            punchConfig,
            newData -> newDao.insert(newData),
            oldData -> oldDao.insert(oldData)
        );
    }
}
```

### 6.2 数据转换器

```java
@Component
public class PunchConfigConverter 
    implements DataConverter<PunchConfigDO, HrmsAttendancePunchConfigDO> {

    @Override
    public HrmsAttendancePunchConfigDO convertFromNew(PunchConfigDO newObj) {
        return PunchConfigMapstruct.INSTANCE.mapToOld(newObj);
    }

    @Override
    public PunchConfigDO convertFromOld(HrmsAttendancePunchConfigDO oldObj) {
        return PunchConfigMapstruct.INSTANCE.mapToNew(oldObj);
    }
}
```

## 7. 监控和运维

### 7.1 关键指标监控

- **事务成功率**：分布式事务的成功/失败比例
- **同步延迟**：异步同步的平均延迟时间
- **补偿执行率**：补偿操作的执行频率
- **数据一致性**：新旧系统数据一致性检查结果

### 7.2 日志记录

```java
// 操作日志
log.info("跨系统操作开始: operationId={}, type={}", operationId, operationType);
log.info("跨系统操作完成: operationId={}, result={}, duration={}ms",
         operationId, result, duration);

// 错误日志
log.error("跨系统操作失败: operationId={}, error={}", operationId, error);

// 补偿日志
log.warn("执行补偿操作: operationId={}, compensationType={}",
         operationId, compensationType);
```

### 7.3 健康检查

```java
@Component
public class CrossSystemHealthIndicator implements HealthIndicator {

    @Override
    public Health health() {
        // 检查新旧系统连接状态
        // 检查消息队列状态
        // 检查数据一致性
        return Health.up()
            .withDetail("oldSystem", "UP")
            .withDetail("newSystem", "UP")
            .withDetail("messageQueue", "UP")
            .build();
    }
}
```

## 8. 优势和特点

### 8.1 技术优势

1. **零停机迁移**：支持在线迁移，不影响业务运行
2. **数据一致性**：多种一致性保证机制
3. **灵活配置**：支持用户级别和部门级别的灰度配置
4. **自动补偿**：失败时自动执行补偿操作
5. **异步处理**：支持异步操作，提高性能

### 8.2 业务优势

1. **风险可控**：分阶段迁移，降低业务风险
2. **快速回滚**：支持快速切回旧系统
3. **渐进式迁移**：支持按用户、部门逐步迁移
4. **数据校验**：提供数据一致性校验工具

## 9. 注意事项

### 9.1 性能考虑

- 双写模式会增加写入延迟，需要评估性能影响
- 异步同步可能存在延迟，需要根据业务需求选择同步策略
- 分布式事务的超时时间需要合理配置

### 9.2 数据一致性

- 跨系统操作无法保证强一致性，需要接受最终一致性
- 需要定期执行数据一致性校验
- 补偿操作需要幂等性设计

### 9.3 运维复杂度

- 需要监控多个系统的状态
- 消息队列的运维和监控
- 补偿操作的人工干预机制

## 10. 实施计划

### 10.1 第一阶段：基础设施搭建（2周）

- [ ] 搭建新系统数据库环境
- [ ] 配置Kafka消息队列
- [ ] 实现分布式事务管理器
- [ ] 创建基础适配器类

### 10.2 第二阶段：核心功能开发（3周）

- [ ] 实现跨系统抽象适配器
- [ ] 实现跨系统配对适配器
- [ ] 开发数据转换器
- [ ] 实现异步同步管理器

### 10.3 第三阶段：业务适配器开发（4周）

- [ ] 开发打卡配置适配器
- [ ] 开发班次记录适配器
- [ ] 开发日历配置适配器
- [ ] 开发其他业务模块适配器

### 10.4 第四阶段：测试和优化（2周）

- [ ] 单元测试和集成测试
- [ ] 性能测试和优化
- [ ] 数据一致性测试
- [ ] 补偿机制测试

### 10.5 第五阶段：灰度发布（4周）

- [ ] 选择试点用户进行灰度测试
- [ ] 监控系统运行状态
- [ ] 收集反馈并优化
- [ ] 逐步扩大灰度范围

## 11. 风险评估

### 11.1 技术风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 分布式事务失败 | 中 | 数据不一致 | 实现补偿机制，定期数据校验 |
| 消息队列故障 | 中 | 异步同步中断 | 实现降级机制，支持同步模式 |
| 性能下降 | 低 | 用户体验影响 | 性能测试，优化配置 |

### 11.2 业务风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 数据丢失 | 高 | 业务中断 | 双写机制，实时备份 |
| 功能异常 | 中 | 用户投诉 | 充分测试，快速回滚 |
| 迁移延期 | 低 | 项目进度 | 分阶段实施，风险可控 |

## 12. 总结

本方案提供了一套完整的跨系统迁移解决方案，通过分布式事务管理、异步数据同步和补偿机制，实现了HRMS系统从单一数据库到跨系统架构的平滑迁移。该方案具有高可用性、高可靠性和高灵活性的特点，能够满足企业级系统迁移的需求。

### 12.1 核心价值

1. **技术先进性**：采用业界成熟的分布式事务和消息队列技术
2. **业务连续性**：确保迁移过程中业务不中断
3. **风险可控性**：分阶段实施，支持快速回滚
4. **扩展性**：架构设计支持未来的系统扩展

### 12.2 预期收益

1. **降低迁移风险**：通过分阶段迁移和补偿机制，大幅降低迁移风险
2. **提高系统性能**：新系统架构支持更高的并发和更好的性能
3. **增强系统可维护性**：模块化设计，便于后续维护和扩展
4. **提升用户体验**：平滑迁移，用户无感知切换
